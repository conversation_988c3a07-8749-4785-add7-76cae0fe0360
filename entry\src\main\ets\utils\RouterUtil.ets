import router from '@ohos.router';

/**
 * 路由管理工具类
 */
export class RouterUtil {
  /**
   * 导航到指定页面
   * @param url 页面URL
   * @param params 参数
   */
  static async push(url: string, params?: Record<string, Object>): Promise<void> {
    try {
      await router.pushUrl({
        url: url,
        params: params
      });
    } catch (error) {
      console.error('Navigation push failed:', error);
    }
  }

  /**
   * 替换当前页面
   * @param url 页面URL
   * @param params 参数
   */
  static async replace(url: string, params?: Record<string, Object>): Promise<void> {
    try {
      await router.replaceUrl({
        url: url,
        params: params
      });
    } catch (error) {
      console.error('Navigation replace failed:', error);
    }
  }

  /**
   * 返回上一页
   */
  static async back(): Promise<void> {
    try {
      await router.back();
    } catch (error) {
      console.error('Navigation back failed:', error);
    }
  }

  /**
   * 返回到指定页面
   * @param url 目标页面URL
   */
  static async backTo(url: string): Promise<void> {
    try {
      await router.back({
        url: url
      });
    } catch (error) {
      console.error('Navigation backTo failed:', error);
    }
  }

  /**
   * 清除所有页面并导航到指定页面
   * @param url 页面URL
   * @param params 参数
   */
  static async clearAndPush(url: string, params?: Record<string, Object>): Promise<void> {
    try {
      await router.clearUrl();
      await router.pushUrl({
        url: url,
        params: params
      });
    } catch (error) {
      console.error('Navigation clearAndPush failed:', error);
    }
  }

  /**
   * 获取当前页面参数
   */
  static getParams(): Record<string, Object> | undefined {
    return router.getParams();
  }

  /**
   * 获取页面栈信息
   */
  static getPageStack(): router.RouterState {
    return router.getState();
  }
}