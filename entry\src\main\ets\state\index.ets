// 状态管理模块导出
export { StateManager } from './StateManager';
export {
  StateObserver,
  StateSelector,
  StateUpdate,
  Computed,
  PersistState,
  DebounceState,
  ThrottleState,
  ValidateState,
  StateSubscription,
  StateQuery
} from './StateDecorators';

// 重新导出类型定义
export type {
  AppState,
  StateChangeType,
  StateChangeEvent,
  StateObserver as IStateObserver
} from './StateManager';

// 状态管理工具函数
export const createStateManager = () => StateManager.getInstance();
export const createStateQuery = () => new StateQuery();
export const createStateSubscription = (
  callback: (event: any) => void,
  observedTypes: string[] = []
) => new StateSubscription(callback, observedTypes);