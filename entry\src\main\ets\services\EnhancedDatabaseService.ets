import relationalStore from '@ohos.data.relationalStore';
import { NoteModel, CapsuleModel, SettingsModel } from '../model/NoteModel';
import { AppConstants } from '../constants/AppConstants';
import { DateUtils } from '../utils/DateUtils';

/**
 * 数据库操作结果
 */
export interface DatabaseResult<T> {
  success: boolean;
  data?: T;
  error?: Error;
  timestamp: string;
}

/**
 * 批量操作配置
 */
export interface BatchOperationConfig {
  batchSize: number;
  delayBetweenBatches: number;
  maxRetries: number;
  retryDelay: number;
}

/**
 * 缓存配置
 */
export interface CacheConfig {
  enabled: boolean;
  maxSize: number;
  ttl: number; // Time To Live in milliseconds
}

/**
 * 数据库事件类型
 */
export type DatabaseEventType = 
  | 'INSERT'
  | 'UPDATE'
  | 'DELETE'
  | 'BATCH_INSERT'
  | 'BATCH_UPDATE'
  | 'BATCH_DELETE'
  | 'TRANSACTION_START'
  | 'TRANSACTION_COMMIT'
  | 'TRANSACTION_ROLLBACK'
  | 'CACHE_HIT'
  | 'CACHE_MISS'
  | 'ERROR';

/**
 * 数据库事件
 */
export interface DatabaseEvent {
  type: DatabaseEventType;
  table: string;
  timestamp: string;
  details?: any;
  error?: Error;
}

/**
 * 数据库观察者接口
 */
export interface DatabaseObserver {
  onDatabaseEvent(event: DatabaseEvent): void;
  getObservedTables(): string[];
  getObservedEventTypes(): DatabaseEventType[];
}

/**
 * 增强的数据库服务类
 */
export class EnhancedDatabaseService {
  private static instance: EnhancedDatabaseService;
  private store: relationalStore.RdbStore | null = null;
  private cache: Map<string, { data: any; timestamp: number; ttl: number }> = new Map();
  private observers: Map<string, DatabaseObserver> = new Map();
  private operationQueue: Array<() => Promise<any>> = [];
  private isProcessingQueue = false;
  
  // 配置
  private readonly cacheConfig: CacheConfig = {
    enabled: true,
    maxSize: 1000,
    ttl: 5 * 60 * 1000 // 5分钟
  };
  
  private readonly batchConfig: BatchOperationConfig = {
    batchSize: 100,
    delayBetweenBatches: 100,
    maxRetries: 3,
    retryDelay: 1000
  };
  
  private constructor() {}
  
  static getInstance(): EnhancedDatabaseService {
    if (!EnhancedDatabaseService.instance) {
      EnhancedDatabaseService.instance = new EnhancedDatabaseService();
    }
    return EnhancedDatabaseService.instance;
  }
  
  /**
   * 初始化数据库
   */
  async init(context: Context): Promise<void> {
    try {
      const config: relationalStore.StoreConfig = {
        name: AppConstants.DATABASE_NAME,
        securityLevel: relationalStore.SecurityLevel.S1
      };
      
      this.store = await relationalStore.getRdbStore(context, config);
      await this.createTables();
      await this.createIndexes();
      
      this.emitEvent({
        type: 'TRANSACTION_COMMIT',
        table: 'system',
        timestamp: new Date().toISOString(),
        details: { action: 'init' }
      });
      
      console.info('Enhanced database initialized successfully');
    } catch (error) {
      this.emitEvent({
        type: 'ERROR',
        table: 'system',
        timestamp: new Date().toISOString(),
        error: error as Error
      });
      console.error('Failed to initialize enhanced database:', error);
      throw error;
    }
  }
  
  /**
   * 创建数据表
   */
  private async createTables(): Promise<void> {
    if (!this.store) return;
    
    // 创建笔记表
    await this.store.executeSql(`
      CREATE TABLE IF NOT EXISTS ${AppConstants.TABLE_NOTES} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        content TEXT,
        categoryId INTEGER DEFAULT 1,
        tags TEXT,
        mood TEXT DEFAULT '',
        weather TEXT DEFAULT '',
        location TEXT DEFAULT '',
        images TEXT DEFAULT '',
        voiceNote TEXT DEFAULT '',
        createTime TEXT NOT NULL,
        updateTime TEXT NOT NULL,
        isDeleted INTEGER DEFAULT 0,
        version INTEGER DEFAULT 1
      )
    `);
    
    // 创建时间胶囊表
    await this.store.executeSql(`
      CREATE TABLE IF NOT EXISTS ${AppConstants.TABLE_CAPSULES} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        content TEXT NOT NULL,
        openDate TEXT NOT NULL,
        createTime TEXT NOT NULL,
        isOpened INTEGER DEFAULT 0,
        isOpenTime INTEGER DEFAULT 0,
        version INTEGER DEFAULT 1
      )
    `);
    
    // 创建设置表
    await this.store.executeSql(`
      CREATE TABLE IF NOT EXISTS settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        darkMode INTEGER DEFAULT 2,
        reminderTime TEXT DEFAULT '21:00',
        reminderEnabled INTEGER DEFAULT 1,
        syncEnabled INTEGER DEFAULT 0,
        backupEnabled INTEGER DEFAULT 0,
        fontSize INTEGER DEFAULT 16,
        version INTEGER DEFAULT 1
      )
    `);
    
    // 创建操作日志表
    await this.store.executeSql(`
      CREATE TABLE IF NOT EXISTS operation_logs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        operation TEXT NOT NULL,
        table_name TEXT NOT NULL,
        record_id INTEGER,
        details TEXT,
        timestamp TEXT NOT NULL
      )
    `);
    
    await this.initDefaultSettings();
  }
  
  /**
   * 创建索引
   */
  private async createIndexes(): Promise<void> {
    if (!this.store) return;
    
    // 笔记表索引
    await this.store.executeSql(`CREATE INDEX IF NOT EXISTS idx_notes_create_time ON ${AppConstants.TABLE_NOTES}(createTime)`);
    await this.store.executeSql(`CREATE INDEX IF NOT EXISTS idx_notes_category_id ON ${AppConstants.TABLE_NOTES}(categoryId)`);
    await this.store.executeSql(`CREATE INDEX IF NOT EXISTS idx_notes_is_deleted ON ${AppConstants.TABLE_NOTES}(isDeleted)`);
    await this.store.executeSql(`CREATE INDEX IF NOT EXISTS idx_notes_mood ON ${AppConstants.TABLE_NOTES}(mood)`);
    await this.store.executeSql(`CREATE INDEX IF NOT EXISTS idx_notes_tags ON ${AppConstants.TABLE_NOTES}(tags)`);
    
    // 时间胶囊表索引
    await this.store.executeSql(`CREATE INDEX IF NOT EXISTS idx_capsules_open_date ON ${AppConstants.TABLE_CAPSULES}(openDate)`);
    await this.store.executeSql(`CREATE INDEX IF NOT EXISTS idx_capsules_is_opened ON ${AppConstants.TABLE_CAPSULES}(isOpened)`);
  }
  
  /**
   * 初始化默认设置
   */
  private async initDefaultSettings(): Promise<void> {
    if (!this.store) return;
    
    const resultSet = await this.store.querySql('SELECT COUNT(*) as count FROM settings');
    if (resultSet.goToFirstRow()) {
      const count = resultSet.getLong(resultSet.getColumnIndex('count'));
      if (count === 0) {
        await this.store.executeSql(`
          INSERT INTO settings (darkMode, reminderTime, reminderEnabled, syncEnabled, backupEnabled, fontSize)
          VALUES (2, '21:00', 1, 0, 0, 16)
        `);
      }
    }
    resultSet.close();
  }
  
  /**
   * 注册数据库观察者
   */
  public registerObserver(id: string, observer: DatabaseObserver): void {
    this.observers.set(id, observer);
  }
  
  /**
   * 注销数据库观察者
   */
  public unregisterObserver(id: string): void {
    this.observers.delete(id);
  }
  
  /**
   * 发送数据库事件
   */
  private emitEvent(event: DatabaseEvent): void {
    for (const observer of this.observers.values()) {
      const observedTables = observer.getObservedTables();
      const observedEventTypes = observer.getObservedEventTypes();
      
      if ((observedTables.length === 0 || observedTables.includes(event.table)) &&
          (observedEventTypes.length === 0 || observedEventTypes.includes(event.type))) {
        try {
          observer.onDatabaseEvent(event);
        } catch (error) {
          console.error('Error in database observer:', error);
        }
      }
    }
  }
  
  /**
   * 缓存操作
   */
  private getFromCache<T>(key: string): T | null {
    if (!this.cacheConfig.enabled) return null;
    
    const cached = this.cache.get(key);
    if (!cached) return null;
    
    const now = Date.now();
    if (now - cached.timestamp > cached.ttl) {
      this.cache.delete(key);
      return null;
    }
    
    this.emitEvent({
      type: 'CACHE_HIT',
      table: 'cache',
      timestamp: new Date().toISOString(),
      details: { key }
    });
    
    return cached.data;
  }
  
  private setCache<T>(key: string, data: T, ttl?: number): void {
    if (!this.cacheConfig.enabled) return;
    
    // 检查缓存大小
    if (this.cache.size >= this.cacheConfig.maxSize) {
      // 删除最旧的缓存项
      const oldestKey = this.cache.keys().next().value;
      this.cache.delete(oldestKey);
    }
    
    const cacheTtl = ttl || this.cacheConfig.ttl;
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl: cacheTtl
    });
    
    this.emitEvent({
      type: 'CACHE_MISS',
      table: 'cache',
      timestamp: new Date().toISOString(),
      details: { key }
    });
  }
  
  private clearCache(pattern?: string): void {
    if (pattern) {
      // 清除匹配模式的缓存
      for (const key of this.cache.keys()) {
        if (key.includes(pattern)) {
          this.cache.delete(key);
        }
      }
    } else {
      // 清除所有缓存
      this.cache.clear();
    }
  }
  
  /**
   * 错误重试机制
   */
  private async withRetry<T>(
    operation: () => Promise<T>,
    maxRetries: number = this.batchConfig.maxRetries,
    retryDelay: number = this.batchConfig.retryDelay
  ): Promise<DatabaseResult<T>> {
    let lastError: Error | null = null;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const result = await operation();
        return {
          success: true,
          data: result,
          timestamp: new Date().toISOString()
        };
      } catch (error) {
        lastError = error as Error;
        
        this.emitEvent({
          type: 'ERROR',
          table: 'system',
          timestamp: new Date().toISOString(),
          error: lastError,
          details: { attempt, maxRetries }
        });
        
        if (attempt < maxRetries) {
          await this.delay(retryDelay * attempt); // 指数退避
        }
      }
    }
    
    return {
      success: false,
      error: lastError!,
      timestamp: new Date().toISOString()
    };
  }
  
  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
  
  /**
   * 事务处理
   */
  public async executeTransaction<T>(
    operations: () => Promise<T>
  ): Promise<DatabaseResult<T>> {
    if (!this.store) {
      return {
        success: false,
        error: new Error('Database not initialized'),
        timestamp: new Date().toISOString()
      };
    }
    
    try {
      this.emitEvent({
        type: 'TRANSACTION_START',
        table: 'system',
        timestamp: new Date().toISOString()
      });
      
      await this.store.beginTransaction();
      const result = await operations();
      await this.store.commit();
      
      this.emitEvent({
        type: 'TRANSACTION_COMMIT',
        table: 'system',
        timestamp: new Date().toISOString(),
        details: { success: true }
      });
      
      return {
        success: true,
        data: result,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      if (this.store) {
        await this.store.rollBack();
      }
      
      this.emitEvent({
        type: 'TRANSACTION_ROLLBACK',
        table: 'system',
        timestamp: new Date().toISOString(),
        error: error as Error
      });
      
      return {
        success: false,
        error: error as Error,
        timestamp: new Date().toISOString()
      };
    }
  }
  
  /**
   * 批量操作
   */
  public async batchInsert<T>(
    table: string,
    items: T[],
    transform: (item: T) => any
  ): Promise<DatabaseResult<number[]>> {
    if (!this.store) {
      return {
        success: false,
        error: new Error('Database not initialized'),
        timestamp: new Date().toISOString()
      };
    }
    
    const results: number[] = [];
    const batchSize = this.batchConfig.batchSize;
    
    for (let i = 0; i < items.length; i += batchSize) {
      const batch = items.slice(i, i + batchSize);
      
      const result = await this.withRetry(async () => {
        const batchResults: number[] = [];
        
        for (const item of batch) {
          const values = transform(item);
          const rowId = await this.store!.insert(table, values);
          batchResults.push(rowId);
        }
        
        return batchResults;
      });
      
      if (!result.success) {
        return {
          success: false,
          error: result.error!,
          timestamp: new Date().toISOString()
        };
      }
      
      results.push(...result.data!);
      
      // 批次间延迟
      if (i + batchSize < items.length) {
        await this.delay(this.batchConfig.delayBetweenBatches);
      }
    }
    
    this.emitEvent({
      type: 'BATCH_INSERT',
      table,
      timestamp: new Date().toISOString(),
      details: { count: results.length }
    });
    
    return {
      success: true,
      data: results,
      timestamp: new Date().toISOString()
    };
  }
  
  /**
   * 批量更新
   */
  public async batchUpdate<T>(
    table: string,
    updates: Array<{ id: number; data: Partial<T> }>
  ): Promise<DatabaseResult<number>> {
    if (!this.store) {
      return {
        success: false,
        error: new Error('Database not initialized'),
        timestamp: new Date().toISOString()
      };
    }
    
    let updatedCount = 0;
    const batchSize = this.batchConfig.batchSize;
    
    for (let i = 0; i < updates.length; i += batchSize) {
      const batch = updates.slice(i, i + batchSize);
      
      const result = await this.withRetry(async () => {
        let count = 0;
        
        for (const update of batch) {
          const pred = new relationalStore.RdbPredicates(table);
          pred.equalTo('id', update.id);
          const rows = await this.store!.update(update.data, pred);
          count += rows;
        }
        
        return count;
      });
      
      if (!result.success) {
        return {
          success: false,
          error: result.error!,
          timestamp: new Date().toISOString()
        };
      }
      
      updatedCount += result.data!;
      
      if (i + batchSize < updates.length) {
        await this.delay(this.batchConfig.delayBetweenBatches);
      }
    }
    
    this.emitEvent({
      type: 'BATCH_UPDATE',
      table,
      timestamp: new Date().toISOString(),
      details: { count: updatedCount }
    });
    
    return {
      success: true,
      data: updatedCount,
      timestamp: new Date().toISOString()
    };
  }
  
  /**
   * 批量删除
   */
  public async batchDelete(
    table: string,
    ids: number[]
  ): Promise<DatabaseResult<number>> {
    if (!this.store) {
      return {
        success: false,
        error: new Error('Database not initialized'),
        timestamp: new Date().toISOString()
      };
    }
    
    let deletedCount = 0;
    const batchSize = this.batchConfig.batchSize;
    
    for (let i = 0; i < ids.length; i += batchSize) {
      const batch = ids.slice(i, i + batchSize);
      
      const result = await this.withRetry(async () => {
        let count = 0;
        
        for (const id of batch) {
          const pred = new relationalStore.RdbPredicates(table);
          pred.equalTo('id', id);
          const rows = await this.store!.delete(pred);
          count += rows;
        }
        
        return count;
      });
      
      if (!result.success) {
        return {
          success: false,
          error: result.error!,
          timestamp: new Date().toISOString()
        };
      }
      
      deletedCount += result.data!;
      
      if (i + batchSize < ids.length) {
        await this.delay(this.batchConfig.delayBetweenBatches);
      }
    }
    
    this.emitEvent({
      type: 'BATCH_DELETE',
      table,
      timestamp: new Date().toISOString(),
      details: { count: deletedCount }
    });
    
    return {
      success: true,
      data: deletedCount,
      timestamp: new Date().toISOString()
    };
  }
  
  /**
   * 保存笔记（带缓存）
   */
  async saveNote(note: NoteModel): Promise<DatabaseResult<number>> {
    const cacheKey = `note_${note.id}`;
    
    const result = await this.withRetry(async () => {
      if (!this.store) throw new Error('Database not initialized');
      
      if (note.id) {
        // 更新
        const updateTime = DateUtils.now();
        const values = {
          title: note.title,
          content: note.content,
          categoryId: note.categoryId,
          tags: note.tags,
          mood: note.mood || '',
          weather: note.weather || '',
          location: note.location || '',
          images: note.images || '',
          voiceNote: note.voiceNote || '',
          updateTime: updateTime
        };
        
        const pred = new relationalStore.RdbPredicates(AppConstants.TABLE_NOTES);
        pred.equalTo('id', note.id);
        const rows = await this.store.update(values, pred);
        
        // 清除相关缓存
        this.clearCache('notes_');
        this.clearCache(cacheKey);
        
        return note.id;
      } else {
        // 插入
        const now = DateUtils.now();
        const values = {
          title: note.title,
          content: note.content,
          categoryId: note.categoryId || 1,
          tags: note.tags || '',
          mood: note.mood || '',
          weather: note.weather || '',
          location: note.location || '',
          images: note.images || '',
          voiceNote: note.voiceNote || '',
          createTime: now,
          updateTime: now,
          isDeleted: 0
        };
        
        const rowId = await this.store.insert(AppConstants.TABLE_NOTES, values);
        
        // 清除相关缓存
        this.clearCache('notes_');
        
        return rowId;
      }
    });
    
    if (result.success) {
      this.emitEvent({
        type: note.id ? 'UPDATE' : 'INSERT',
        table: AppConstants.TABLE_NOTES,
        timestamp: new Date().toISOString(),
        details: { id: result.data }
      });
    }
    
    return result;
  }
  
  /**
   * 获取笔记列表（带缓存）
   */
  async getNotes(offset: number = 0, limit: number = AppConstants.PAGE_SIZE): Promise<DatabaseResult<NoteModel[]>> {
    const cacheKey = `notes_list_${offset}_${limit}`;
    
    // 尝试从缓存获取
    const cached = this.getFromCache<NoteModel[]>(cacheKey);
    if (cached) {
      return {
        success: true,
        data: cached,
        timestamp: new Date().toISOString()
      };
    }
    
    const result = await this.withRetry(async () => {
      if (!this.store) throw new Error('Database not initialized');
      
      const pred = new relationalStore.RdbPredicates(AppConstants.TABLE_NOTES);
      pred.equalTo('isDeleted', 0);
      pred.orderByDesc('createTime');
      pred.limitAs(limit);
      pred.offsetAs(offset);
      
      const resultSet = await this.store.query(pred);
      const notes: NoteModel[] = [];
      
      if (resultSet.goToFirstRow()) {
        do {
          const note = this.resultSetToNote(resultSet);
          notes.push(note);
        } while (resultSet.goToNextRow());
      }
      
      resultSet.close();
      return notes;
    });
    
    if (result.success) {
      // 缓存结果
      this.setCache(cacheKey, result.data);
    }
    
    return result;
  }
  
  /**
   * 搜索笔记（带缓存）
   */
  async searchNotes(keyword: string): Promise<DatabaseResult<NoteModel[]>> {
    if (!keyword.trim()) {
      return {
        success: true,
        data: [],
        timestamp: new Date().toISOString()
      };
    }
    
    const cacheKey = `notes_search_${keyword}`;
    
    // 尝试从缓存获取
    const cached = this.getFromCache<NoteModel[]>(cacheKey);
    if (cached) {
      return {
        success: true,
        data: cached,
        timestamp: new Date().toISOString()
      };
    }
    
    const result = await this.withRetry(async () => {
      if (!this.store) throw new Error('Database not initialized');
      
      // 使用参数化查询防止SQL注入
      const sql = `
        SELECT * FROM ${AppConstants.TABLE_NOTES} 
        WHERE isDeleted = 0 
        AND (title LIKE ? OR content LIKE ? OR tags LIKE ?)
        ORDER BY 
          CASE 
            WHEN title LIKE ? THEN 1
            WHEN content LIKE ? THEN 2
            ELSE 3
          END,
          createTime DESC
      `;
      
      const searchPattern = `%${keyword}%`;
      const resultSet = await this.store.querySql(sql, [
        searchPattern, searchPattern, searchPattern,
        searchPattern, searchPattern
      ]);
      
      const notes: NoteModel[] = [];
      
      if (resultSet.goToFirstRow()) {
        do {
          const note = this.resultSetToNote(resultSet);
          notes.push(note);
        } while (resultSet.goToNextRow());
      }
      
      resultSet.close();
      return notes;
    });
    
    if (result.success) {
      // 缓存搜索结果
      this.setCache(cacheKey, result.data, 2 * 60 * 1000); // 搜索结果缓存2分钟
    }
    
    return result;
  }
  
  /**
   * 将ResultSet转换为Note对象
   */
  private resultSetToNote(resultSet: relationalStore.ResultSet): NoteModel {
    return {
      id: resultSet.getLong(resultSet.getColumnIndex('id')),
      title: resultSet.getString(resultSet.getColumnIndex('title')),
      content: resultSet.getString(resultSet.getColumnIndex('content')),
      categoryId: resultSet.getLong(resultSet.getColumnIndex('categoryId')),
      tags: resultSet.getString(resultSet.getColumnIndex('tags')),
      mood: resultSet.getString(resultSet.getColumnIndex('mood')),
      weather: resultSet.getString(resultSet.getColumnIndex('weather')),
      location: resultSet.getString(resultSet.getColumnIndex('location')),
      images: resultSet.getString(resultSet.getColumnIndex('images')),
      voiceNote: resultSet.getString(resultSet.getColumnIndex('voiceNote')),
      createTime: resultSet.getString(resultSet.getColumnIndex('createTime')),
      updateTime: resultSet.getString(resultSet.getColumnIndex('updateTime')),
      isDeleted: resultSet.getLong(resultSet.getColumnIndex('isDeleted'))
    };
  }
  
  /**
   * 获取设置（带缓存）
   */
  async getSettings(): Promise<DatabaseResult<SettingsModel | null>> {
    const cacheKey = 'settings';
    
    // 尝试从缓存获取
    const cached = this.getFromCache<SettingsModel>(cacheKey);
    if (cached) {
      return {
        success: true,
        data: cached,
        timestamp: new Date().toISOString()
      };
    }
    
    const result = await this.withRetry(async () => {
      if (!this.store) throw new Error('Database not initialized');
      
      const resultSet = await this.store.querySql('SELECT * FROM settings LIMIT 1');
      let settings: SettingsModel | null = null;
      
      if (resultSet.goToFirstRow()) {
        settings = {
          id: resultSet.getLong(resultSet.getColumnIndex('id')),
          darkMode: resultSet.getLong(resultSet.getColumnIndex('darkMode')),
          reminderTime: resultSet.getString(resultSet.getColumnIndex('reminderTime')),
          reminderEnabled: resultSet.getLong(resultSet.getColumnIndex('reminderEnabled')),
          syncEnabled: resultSet.getLong(resultSet.getColumnIndex('syncEnabled')),
          backupEnabled: resultSet.getLong(resultSet.getColumnIndex('backupEnabled')),
          fontSize: resultSet.getLong(resultSet.getColumnIndex('fontSize'))
        };
      }
      
      resultSet.close();
      return settings;
    });
    
    if (result.success) {
      // 缓存设置
      this.setCache(cacheKey, result.data, 10 * 60 * 1000); // 设置缓存10分钟
    }
    
    return result;
  }
  
  /**
   * 更新设置
   */
  async updateSettings(settings: Partial<SettingsModel>): Promise<DatabaseResult<void>> {
    const result = await this.withRetry(async () => {
      if (!this.store) throw new Error('Database not initialized');
      
      const pred = new relationalStore.RdbPredicates('settings');
      pred.equalTo('id', 1);
      
      await this.store.update(settings, pred);
      
      // 清除设置缓存
      this.clearCache('settings');
      
      return;
    });
    
    if (result.success) {
      this.emitEvent({
        type: 'UPDATE',
        table: 'settings',
        timestamp: new Date().toISOString(),
        details: { settings }
      });
    }
    
    return result;
  }
  
  /**
   * 获取时间胶囊列表（带缓存）
   */
  async getCapsules(): Promise<DatabaseResult<CapsuleModel[]>> {
    const cacheKey = 'capsules_list';
    
    // 尝试从缓存获取
    const cached = this.getFromCache<CapsuleModel[]>(cacheKey);
    if (cached) {
      return {
        success: true,
        data: cached,
        timestamp: new Date().toISOString()
      };
    }
    
    const result = await this.withRetry(async () => {
      if (!this.store) throw new Error('Database not initialized');
      
      const pred = new relationalStore.RdbPredicates(AppConstants.TABLE_CAPSULES);
      pred.orderByDesc('createTime');
      
      const resultSet = await this.store.query(pred);
      const capsules: CapsuleModel[] = [];
      
      if (resultSet.goToFirstRow()) {
        do {
          capsules.push({
            id: resultSet.getLong(resultSet.getColumnIndex('id')),
            title: resultSet.getString(resultSet.getColumnIndex('title')),
            content: resultSet.getString(resultSet.getColumnIndex('content')),
            openDate: resultSet.getString(resultSet.getColumnIndex('openDate')),
            createTime: resultSet.getString(resultSet.getColumnIndex('createTime')),
            isOpened: resultSet.getLong(resultSet.getColumnIndex('isOpened')),
            isOpenTime: resultSet.getLong(resultSet.getColumnIndex('isOpenTime')) === 1
          });
        } while (resultSet.goToNextRow());
      }
      
      resultSet.close();
      return capsules;
    });
    
    if (result.success) {
      // 缓存结果
      this.setCache(cacheKey, result.data, 3 * 60 * 1000); // 胶囊缓存3分钟
    }
    
    return result;
  }
  
  /**
   * 创建时间胶囊
   */
  async createCapsule(capsule: CapsuleModel): Promise<DatabaseResult<number>> {
    const result = await this.withRetry(async () => {
      if (!this.store) throw new Error('Database not initialized');
      
      const values = {
        title: capsule.title,
        content: capsule.content,
        openDate: capsule.openDate,
        createTime: DateUtils.now(),
        isOpened: 0,
        isOpenTime: capsule.isOpenTime ? 1 : 0
      };
      
      const rowId = await this.store.insert(AppConstants.TABLE_CAPSULES, values);
      
      // 清除胶囊缓存
      this.clearCache('capsules_');
      
      return rowId;
    });
    
    if (result.success) {
      this.emitEvent({
        type: 'INSERT',
        table: AppConstants.TABLE_CAPSULES,
        timestamp: new Date().toISOString(),
        details: { id: result.data }
      });
    }
    
    return result;
  }
  
  /**
   * 数据库维护操作
   */
  async performMaintenance(): Promise<DatabaseResult<void>> {
    const result = await this.withRetry(async () => {
      if (!this.store) throw new Error('Database not initialized');
      
      // 清理过期缓存
      this.clearCache();
      
      // 分析数据库
      await this.store.executeSql('ANALYZE');
      
      // 清理操作日志（保留最近30天）
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      const cutoffDate = DateUtils.formatDate(thirtyDaysAgo, 'YYYY-MM-DD HH:mm:ss');
      
      await this.store.executeSql(
        `DELETE FROM operation_logs WHERE timestamp < ?`,
        [cutoffDate]
      );
      
      return;
    });
    
    return result;
  }
  
  /**
   * 获取数据库统计信息
   */
  async getDatabaseStats(): Promise<DatabaseResult<{
    notesCount: number;
    capsulesCount: number;
    cacheSize: number;
    cacheHitRate: number;
  }>> {
    const result = await this.withRetry(async () => {
      if (!this.store) throw new Error('Database not initialized');
      
      // 获取笔记数量
      const notesResult = await this.store.querySql(
        `SELECT COUNT(*) as count FROM ${AppConstants.TABLE_NOTES} WHERE isDeleted = 0`
      );
      const notesCount = notesResult.goToFirstRow() ? 
        notesResult.getLong(notesResult.getColumnIndex('count')) : 0;
      notesResult.close();
      
      // 获取胶囊数量
      const capsulesResult = await this.store.querySql(
        `SELECT COUNT(*) as count FROM ${AppConstants.TABLE_CAPSULES}`
      );
      const capsulesCount = capsulesResult.goToFirstRow() ? 
        capsulesResult.getLong(capsulesResult.getColumnIndex('count')) : 0;
      capsulesResult.close();
      
      return {
        notesCount,
        capsulesCount,
        cacheSize: this.cache.size,
        cacheHitRate: 0 // 需要实现缓存命中率统计
      };
    });
    
    return result;
  }
  
  /**
   * 获取原始数据库服务实例（用于向后兼容）
   */
  public getOriginalService(): any {
    // 返回原始的DatabaseService方法
    return {
      saveNote: (note: NoteModel) => this.saveNote(note).then(r => r.data),
      getNotes: (offset?: number, limit?: number) => this.getNotes(offset, limit).then(r => r.data),
      deleteNote: (id: number) => this.deleteNote(id),
      searchNotes: (keyword: string) => this.searchNotes(keyword).then(r => r.data),
      getNotesByDate: (date: string) => this.getNotesByDate(date),
      getNoteDates: () => this.getNoteDates(),
      getNoteCountByDate: (date: string) => this.getNoteCountByDate(date),
      getMonthlyNoteStats: (year: number, month: number) => this.getMonthlyNoteStats(year, month),
      createCapsule: (capsule: CapsuleModel) => this.createCapsule(capsule).then(r => r.data),
      getCapsules: () => this.getCapsules().then(r => r.data),
      updateCapsule: (id: number, updates: Partial<CapsuleModel>) => this.updateCapsule(id, updates),
      deleteCapsule: (id: number) => this.deleteCapsule(id),
      getExpiredCapsules: () => this.getExpiredCapsules(),
      getUpcomingCapsules: (days?: number) => this.getUpcomingCapsules(days),
      getCapsuleStats: () => this.getCapsuleStats(),
      getSettings: () => this.getSettings().then(r => r.data),
      updateSettings: (settings: Partial<SettingsModel>) => this.updateSettings(settings)
    };
  }
  
  // 以下是原始方法的兼容性实现
  private async deleteNote(id: number): Promise<void> {
    const result = await this.withRetry(async () => {
      if (!this.store) throw new Error('Database not initialized');
      
      const values = { isDeleted: 1 };
      const pred = new relationalStore.RdbPredicates(AppConstants.TABLE_NOTES);
      pred.equalTo('id', id);
      
      await this.store.update(values, pred);
      
      // 清除相关缓存
      this.clearCache('notes_');
    });
    
    if (result.success) {
      this.emitEvent({
        type: 'DELETE',
        table: AppConstants.TABLE_NOTES,
        timestamp: new Date().toISOString(),
        details: { id }
      });
    }
  }
  
  private async getNotesByDate(date: string): Promise<NoteModel[]> {
    const cacheKey = `notes_by_date_${date}`;
    
    // 尝试从缓存获取
    const cached = this.getFromCache<NoteModel[]>(cacheKey);
    if (cached) {
      return cached;
    }
    
    const result = await this.withRetry(async () => {
      if (!this.store) throw new Error('Database not initialized');
      
      const pred = new relationalStore.RdbPredicates(AppConstants.TABLE_NOTES);
      pred.equalTo('isDeleted', 0);
      pred.like('createTime', `${date}%`);
      pred.orderByDesc('createTime');
      
      const resultSet = await this.store.query(pred);
      const notes: NoteModel[] = [];
      
      if (resultSet.goToFirstRow()) {
        do {
          const note = this.resultSetToNote(resultSet);
          notes.push(note);
        } while (resultSet.goToNextRow());
      }
      
      resultSet.close();
      return notes;
    });
    
    if (result.success) {
      // 缓存结果
      this.setCache(cacheKey, result.data);
      return result.data;
    }
    
    return [];
  }
  
  private async getNoteDates(): Promise<string[]> {
    const cacheKey = 'note_dates';
    
    // 尝试从缓存获取
    const cached = this.getFromCache<string[]>(cacheKey);
    if (cached) {
      return cached;
    }
    
    const result = await this.withRetry(async () => {
      if (!this.store) throw new Error('Database not initialized');
      
      const resultSet = await this.store.querySql(`
        SELECT DISTINCT SUBSTR(createTime, 1, 10) as date 
        FROM ${AppConstants.TABLE_NOTES} 
        WHERE isDeleted = 0 
        ORDER BY date DESC
      `);
      
      const dates: string[] = [];
      
      if (resultSet.goToFirstRow()) {
        do {
          const date = resultSet.getString(resultSet.getColumnIndex('date'));
          dates.push(date);
        } while (resultSet.goToNextRow());
      }
      
      resultSet.close();
      return dates;
    });
    
    if (result.success) {
      // 缓存结果
      this.setCache(cacheKey, result.data, 5 * 60 * 1000); // 日期列表缓存5分钟
      return result.data;
    }
    
    return [];
  }
  
  private async getNoteCountByDate(date: string): Promise<number> {
    const cacheKey = `note_count_${date}`;
    
    // 尝试从缓存获取
    const cached = this.getFromCache<number>(cacheKey);
    if (cached !== null) {
      return cached;
    }
    
    const result = await this.withRetry(async () => {
      if (!this.store) throw new Error('Database not initialized');
      
      const resultSet = await this.store.querySql(`
        SELECT COUNT(*) as count 
        FROM ${AppConstants.TABLE_NOTES} 
        WHERE isDeleted = 0 AND createTime LIKE ?
      `, [`${date}%`]);
      
      let count = 0;
      if (resultSet.goToFirstRow()) {
        count = resultSet.getLong(resultSet.getColumnIndex('count'));
      }
      
      resultSet.close();
      return count;
    });
    
    if (result.success) {
      // 缓存结果
      this.setCache(cacheKey, result.data, 3 * 60 * 1000); // 计数缓存3分钟
      return result.data;
    }
    
    return 0;
  }
  
  private async getMonthlyNoteStats(year: number, month: number): Promise<{ date: string; count: number }[]> {
    const cacheKey = `monthly_stats_${year}_${month}`;
    
    // 尝试从缓存获取
    const cached = this.getFromCache<{ date: string; count: number }[]>(cacheKey);
    if (cached) {
      return cached;
    }
    
    const result = await this.withRetry(async () => {
      if (!this.store) throw new Error('Database not initialized');
      
      const monthStr = month < 10 ? `0${month}` : `${month}`;
      const resultSet = await this.store.querySql(`
        SELECT SUBSTR(createTime, 1, 10) as date, COUNT(*) as count 
        FROM ${AppConstants.TABLE_NOTES} 
        WHERE isDeleted = 0 AND createTime LIKE ?
        GROUP BY SUBSTR(createTime, 1, 10)
        ORDER BY date
      `, [`${year}-${monthStr}-%`]);
      
      const stats: { date: string; count: number }[] = [];
      
      if (resultSet.goToFirstRow()) {
        do {
          const date = resultSet.getString(resultSet.getColumnIndex('date'));
          const count = resultSet.getLong(resultSet.getColumnIndex('count'));
          stats.push({ date, count });
        } while (resultSet.goToNextRow());
      }
      
      resultSet.close();
      return stats;
    });
    
    if (result.success) {
      // 缓存结果
      this.setCache(cacheKey, result.data, 10 * 60 * 1000); // 月度统计缓存10分钟
      return result.data;
    }
    
    return [];
  }
  
  private async updateCapsule(id: number, updates: Partial<CapsuleModel>): Promise<void> {
    const result = await this.withRetry(async () => {
      if (!this.store) throw new Error('Database not initialized');
      
      const values: any = {};
      if (updates.title !== undefined) values.title = updates.title;
      if (updates.content !== undefined) values.content = updates.content;
      if (updates.openDate !== undefined) values.openDate = updates.openDate;
      if (updates.isOpened !== undefined) values.isOpened = updates.isOpened;
      if (updates.isOpenTime !== undefined) values.isOpenTime = updates.isOpenTime ? 1 : 0;
      
      const pred = new relationalStore.RdbPredicates(AppConstants.TABLE_CAPSULES);
      pred.equalTo('id', id);
      
      await this.store.update(values, pred);
      
      // 清除胶囊缓存
      this.clearCache('capsules_');
    });
    
    if (result.success) {
      this.emitEvent({
        type: 'UPDATE',
        table: AppConstants.TABLE_CAPSULES,
        timestamp: new Date().toISOString(),
        details: { id, updates }
      });
    }
  }
  
  private async deleteCapsule(id: number): Promise<void> {
    const result = await this.withRetry(async () => {
      if (!this.store) throw new Error('Database not initialized');
      
      const pred = new relationalStore.RdbPredicates(AppConstants.TABLE_CAPSULES);
      pred.equalTo('id', id);
      
      await this.store.delete(pred);
      
      // 清除胶囊缓存
      this.clearCache('capsules_');
    });
    
    if (result.success) {
      this.emitEvent({
        type: 'DELETE',
        table: AppConstants.TABLE_CAPSULES,
        timestamp: new Date().toISOString(),
        details: { id }
      });
    }
  }
  
  private async getExpiredCapsules(): Promise<CapsuleModel[]> {
    const now = DateUtils.now();
    const cacheKey = `expired_capsules_${now.substring(0, 10)}`;
    
    // 尝试从缓存获取
    const cached = this.getFromCache<CapsuleModel[]>(cacheKey);
    if (cached) {
      return cached;
    }
    
    const result = await this.withRetry(async () => {
      if (!this.store) throw new Error('Database not initialized');
      
      const resultSet = await this.store.querySql(`
        SELECT * FROM ${AppConstants.TABLE_CAPSULES} 
        WHERE isOpened = 0 AND openDate <= ?
        ORDER BY openDate ASC
      `, [now]);
      
      const capsules: CapsuleModel[] = [];
      
      if (resultSet.goToFirstRow()) {
        do {
          capsules.push({
            id: resultSet.getLong(resultSet.getColumnIndex('id')),
            title: resultSet.getString(resultSet.getColumnIndex('title')),
            content: resultSet.getString(resultSet.getColumnIndex('content')),
            openDate: resultSet.getString(resultSet.getColumnIndex('openDate')),
            createTime: resultSet.getString(resultSet.getColumnIndex('createTime')),
            isOpened: resultSet.getLong(resultSet.getColumnIndex('isOpened')),
            isOpenTime: resultSet.getLong(resultSet.getColumnIndex('isOpenTime')) === 1
          });
        } while (resultSet.goToNextRow());
      }
      
      resultSet.close();
      return capsules;
    });
    
    if (result.success) {
      // 缓存结果
      this.setCache(cacheKey, result.data, 60 * 1000); // 过期胶囊缓存1分钟
      return result.data;
    }
    
    return [];
  }
  
  private async getUpcomingCapsules(days: number = 7): Promise<CapsuleModel[]> {
    const now = new Date();
    const futureDate = new Date(now.getTime() + days * 24 * 60 * 60 * 1000);
    const nowStr = DateUtils.formatDate(now, 'YYYY-MM-DD HH:mm:ss');
    const futureStr = DateUtils.formatDate(futureDate, 'YYYY-MM-DD HH:mm:ss');
    
    const cacheKey = `upcoming_capsules_${days}_${nowStr.substring(0, 10)}`;
    
    // 尝试从缓存获取
    const cached = this.getFromCache<CapsuleModel[]>(cacheKey);
    if (cached) {
      return cached;
    }
    
    const result = await this.withRetry(async () => {
      if (!this.store) throw new Error('Database not initialized');
      
      const resultSet = await this.store.querySql(`
        SELECT * FROM ${AppConstants.TABLE_CAPSULES} 
        WHERE isOpened = 0 AND openDate > ? AND openDate <= ?
        ORDER BY openDate ASC
      `, [nowStr, futureStr]);
      
      const capsules: CapsuleModel[] = [];
      
      if (resultSet.goToFirstRow()) {
        do {
          capsules.push({
            id: resultSet.getLong(resultSet.getColumnIndex('id')),
            title: resultSet.getString(resultSet.getColumnIndex('title')),
            content: resultSet.getString(resultSet.getColumnIndex('content')),
            openDate: resultSet.getString(resultSet.getColumnIndex('openDate')),
            createTime: resultSet.getString(resultSet.getColumnIndex('createTime')),
            isOpened: resultSet.getLong(resultSet.getColumnIndex('isOpened')),
            isOpenTime: resultSet.getLong(resultSet.getColumnIndex('isOpenTime')) === 1
          });
        } while (resultSet.goToNextRow());
      }
      
      resultSet.close();
      return capsules;
    });
    
    if (result.success) {
      // 缓存结果
      this.setCache(cacheKey, result.data, 5 * 60 * 1000); // 即将到期胶囊缓存5分钟
      return result.data;
    }
    
    return [];
  }
  
  private async getCapsuleStats(): Promise<{
    total: number;
    opened: number;
    locked: number;
    expired: number;
  }> {
    const cacheKey = 'capsule_stats';
    
    // 尝试从缓存获取
    const cached = this.getFromCache(cacheKey);
    if (cached) {
      return cached;
    }
    
    const result = await this.withRetry(async () => {
      if (!this.store) throw new Error('Database not initialized');
      
      const now = DateUtils.now();
      
      // 获取总数
      const totalResult = await this.store.querySql(`SELECT COUNT(*) as count FROM ${AppConstants.TABLE_CAPSULES}`);
      const total = totalResult.goToFirstRow() ? totalResult.getLong(totalResult.getColumnIndex('count')) : 0;
      totalResult.close();
      
      // 获取已开启数量
      const openedResult = await this.store.querySql(`SELECT COUNT(*) as count FROM ${AppConstants.TABLE_CAPSULES} WHERE isOpened = 1`);
      const opened = openedResult.goToFirstRow() ? openedResult.getLong(openedResult.getColumnIndex('count')) : 0;
      openedResult.close();
      
      // 获取已过期可开启数量
      const expiredResult = await this.store.querySql(`SELECT COUNT(*) as count FROM ${AppConstants.TABLE_CAPSULES} WHERE isOpened = 0 AND openDate <= ?`, [now]);
      const expired = expiredResult.goToFirstRow() ? expiredResult.getLong(expiredResult.getColumnIndex('count')) : 0;
      expiredResult.close();
      
      // 计算锁定数量
      const locked = total - opened - expired;
      
      return {
        total,
        opened,
        locked,
        expired
      };
    });
    
    if (result.success) {
      // 缓存结果
      this.setCache(cacheKey, result.data, 3 * 60 * 1000); // 统计信息缓存3分钟
      return result.data;
    }
    
    return { total: 0, opened: 0, locked: 0, expired: 0 };
  }
}