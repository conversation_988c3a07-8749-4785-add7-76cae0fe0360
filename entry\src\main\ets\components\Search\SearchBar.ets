import { MorandiColors } from '../../constants/MorandiColors';

@Component
export struct SearchBar {
  @Prop placeholder: string = '搜索笔记...';
  @Prop value: string = '';
  @Prop showFilter: boolean = true;
  @Prop onSearch?: (keyword: string) => void;
  @Prop onFilter?: () => void;
  
  @State private searchValue: string = '';
  
  aboutToAppear() {
    this.searchValue = this.value;
  }
  
  build() {
    Row() {
      // 搜索输入框
      Row() {
        Image($r('app.media.search_icon'))
          .width(20)
          .height(20)
          .fillColor(MorandiColors.textHint)
          .margin({ left: 16, right: 8 });
        
        TextInput({ placeholder: this.placeholder })
          .width('100%')
          .height(48)
          .backgroundColor(Color.Transparent)
          .fontColor(MorandiColors.textPrimary)
          .fontSize(16)
          .borderRadius(0)
          .border({ width: 0 })
          .caretColor(MorandiColors.accent)
          .onChange((value: string) => {
            this.searchValue = value;
          })
          .onSubmit(() => {
            if (this.onSearch) {
              this.onSearch(this.searchValue);
            }
          });
      }
      .layoutWeight(1)
      .height(48)
      .backgroundColor(MorandiColors.cardBackground)
      .borderRadius(24)
      .border({ width: 1, color: MorandiColors.border });
      
      // 筛选按钮
      if (this.showFilter) {
        Button() {
          Image($r('app.media.filter_icon'))
            .width(20)
            .height(20)
            .fillColor(MorandiColors.textHint);
        }
        .width(48)
        .height(48)
        .backgroundColor(MorandiColors.cardBackground)
        .borderRadius(24)
        .border({ width: 1, color: MorandiColors.border })
        .margin({ left: 12 })
        .onClick(() => {
          if (this.onFilter) {
            this.onFilter();
          }
        });
      }
    }
    .width('100%')
    .height(48)
    .alignItems(VerticalAlign.Center);
  }
}