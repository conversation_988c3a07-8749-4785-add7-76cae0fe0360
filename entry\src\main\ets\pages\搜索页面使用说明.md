# TimeNotes搜索页面使用说明

## 功能特性

### 1. 搜索功能
- **实时搜索**：输入关键词时自动触发搜索，支持防抖处理
- **全文搜索**：搜索范围包括笔记标题、内容和标签
- **智能排序**：根据相关性分数自动排序搜索结果
- **关键词高亮**：搜索结果中关键词以不同颜色和字体粗细显示

### 2. 筛选功能
- **分类筛选**：支持按笔记类型筛选
  - 全部：显示所有笔记
  - 生活感悟：categoryId = 1
  - 工作：categoryId = 2
  - 学习：categoryId = 3
  - 计划：categoryId = 4
- **选中状态**：选中的筛选标签背景色会变化

### 3. 搜索历史
- **自动记录**：每次搜索都会自动记录到搜索历史
- **快速重搜**：点击历史记录可以快速重新搜索
- **历史管理**：支持清空所有搜索历史
- **数量限制**：最多保存10条搜索历史记录

### 4. 搜索结果
- **结果显示**：显示匹配的笔记数量
- **相关性标识**：高匹配度的笔记会显示"高匹配"标签
- **相关性分数**：每个搜索结果显示相关性分数
- **完整信息**：显示笔记的创建时间、标题、内容片段和标签

### 5. 空状态处理
- **无搜索结果**：当搜索不到结果时显示友好的提示信息
- **搜索建议**：提供搜索建议帮助用户改进搜索
- **无历史记录**：当没有搜索历史时显示相应提示

## 设计特色

### 1. 莫兰迪色系
使用精心挑选的莫兰迪色系，营造温暖舒适的视觉体验：
- 主色调：`#7D6B83` (深紫灰色)
- 辅助色：`#9B8A7E` (米褐色)
- 强调色：`#D4A5A5` (淡粉色)
- 背景色：`#F8F6F2` (米白色)

### 2. 响应式设计
- 适配不同屏幕尺寸
- 支持横竖屏切换
- 灵活的布局系统

### 3. 用户体验
- **流畅的交互动画**
- **直观的操作反馈**
- **清晰的信息层级**
- **一致的设计语言**

## 技术实现

### 1. 数据库搜索
```typescript
// 使用 SQL 查询进行全文搜索
const sql = `
  SELECT * FROM notes 
  WHERE isDeleted = 0 
  AND (title LIKE '%${keyword}%' OR content LIKE '%${keyword}%' OR tags LIKE '%${keyword}%')
  ORDER BY 
    CASE 
      WHEN title LIKE '%${keyword}%' THEN 1
      WHEN content LIKE '%${keyword}%' THEN 2
      ELSE 3
    END,
    createTime DESC
`;
```

### 2. 相关性计算
- 标题匹配：10分
- 内容匹配：5分
- 标签匹配：3分
- 匹配次数：每次匹配额外加分

### 3. 关键词高亮
```typescript
// 将文本分割为普通和高亮部分
private highlightKeyword(text: string, keyword: string): { text: string; isHighlighted: boolean }[] {
  // 使用正则表达式分割文本
  // 返回包含高亮信息的文本片段数组
}
```

### 4. 搜索历史管理
- 使用 localStorage 存储搜索历史
- 自动去重和数量限制
- 支持一键清空功能

## 文件结构

```
pages/
├── SearchPage.ets           # 搜索页面主要组件
├── NoteDetailPage.ets       # 笔记详情页面
├── Main.ets                 # 主页面（已集成搜索页面）
└── ...

utils/
├── TestDataGenerator.ets    # 测试数据生成器
└── ...

services/
└── DatabaseService.ets       # 数据库服务（已增强搜索功能）
```

## 使用方法

### 1. 集成到应用
搜索页面已经集成到主应用的底部导航栏中，用户可以点击"搜索"图标进入搜索页面。

### 2. 生成测试数据
```typescript
import { TestDataGenerator } from '../utils/TestDataGenerator';

const generator = new TestDataGenerator();
await generator.generateTestData(); // 生成测试数据
await generator.clearTestData();   // 清空测试数据
```

### 3. 自定义搜索逻辑
可以通过修改 `DatabaseService.ets` 中的 `searchNotes` 方法来自定义搜索逻辑。

## 注意事项

1. **数据库初始化**：确保在使用搜索功能前数据库已正确初始化
2. **权限管理**：应用需要有相应的数据库访问权限
3. **性能优化**：大量数据时考虑添加搜索结果的分页功能
4. **用户体验**：搜索操作较耗时时应显示加载状态

## 扩展功能

### 可以考虑添加的功能：
1. **搜索建议**：输入时显示搜索建议
2. **高级搜索**：支持多条件组合搜索
3. **搜索结果分页**：大量数据时分页显示
4. **搜索统计**：显示搜索统计信息
5. **语音搜索**：支持语音输入搜索
6. **图片搜索**：支持图片内容搜索

## 问题排查

### 常见问题：
1. **搜索无结果**：检查数据库中是否有数据，搜索关键词是否正确
2. **历史记录不显示**：检查 localStorage 是否可用
3. **高亮显示异常**：检查关键词高亮逻辑是否正确
4. **筛选功能失效**：检查分类ID映射是否正确

## 版本历史

- v1.0.0：基础搜索功能实现
- v1.1.0：添加搜索历史和筛选功能
- v1.2.0：优化搜索算法和用户体验
- v1.3.0：完善莫兰迪色系设计