import { MorandiColors } from '../../constants/MorandiColors';

@Component
export struct MyCard {
  @Prop title?: string;
  @Prop subtitle?: string;
  @Prop padding: number | string = 16;
  @Prop margin: number | string = 0;
  @Prop clickable: boolean = false;
  @Prop showShadow: boolean = true;
  @State isHovered: boolean = false;
  @State isPressed: boolean = false;
  onClick?: () => void;
  
  @Builder content: () => void;
  
  build() {
    Column() {
      if (this.title || this.subtitle) {
        Column() {
          if (this.title) {
            Text(this.title)
              .fontSize(18)
              .fontWeight(500)
              .fontColor(MorandiColors.textPrimary)
              .alignSelf(ItemAlign.Start);
          }
          
          if (this.subtitle) {
            Text(this.subtitle)
              .fontSize(14)
              .fontColor(MorandiColors.textTertiary)
              .margin({ top: this.title ? 4 : 0 })
              .alignSelf(ItemAlign.Start);
          }
        }
        .width('100%')
        .margin({ bottom: 12 });
      }
      
      this.content();
    }
    .width('100%')
    .padding(this.padding)
    .backgroundColor(MorandiColors.cardBackground)
    .borderRadius(16)
    .border({ width: 1, color: MorandiColors.border })
    .margin(this.margin)
    .shadow(this.showShadow ? {
      radius: this.isHovered ? 12 : 8,
      color: MorandiColors.shadow,
      offsetX: 0,
      offsetY: this.isHovered ? 4 : 2
    } : { radius: 0, color: Color.Transparent, offsetX: 0, offsetY: 0 })
    .scale({ x: this.isHovered ? 1.02 : 1, y: this.isHovered ? 1.02 : 1 })
    .translate({ y: this.isHovered ? -2 : 0 })
    .onClick(() => {
      if (this.clickable && this.onClick) {
        this.onClick();
      }
    })
    .onHover((isHover: boolean) => {
      this.isHovered = isHover;
    })
    .onTouch((event) => {
      if (event.type === TouchType.Down) {
        this.isPressed = true;
      } else if (event.type === TouchType.Up || event.type === TouchType.Cancel) {
        this.isPressed = false;
      }
    })
    .animation({
      duration: 200,
      curve: Curve.EaseInOut,
      delay: 0,
      iterations: 1,
      playMode: PlayMode.Normal
    });
  }
}