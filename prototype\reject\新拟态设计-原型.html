<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时光拾光 - 新拟态设计原型</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: #E0E5EC;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .phone-container {
            width: 375px;
            height: 812px;
            background: #E0E5EC;
            border-radius: 30px;
            overflow: hidden;
            box-shadow: 
                20px 20px 60px #bec3c9,
                -20px -20px 60px #ffffff;
            position: relative;
        }

        .status-bar {
            height: 44px;
            background: #E0E5EC;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 24px;
            font-size: 14px;
            color: #6B7280;
        }

        .content {
            height: calc(100% - 44px);
            display: flex;
            flex-direction: column;
        }

        .header {
            padding: 20px 24px;
            background: #E0E5EC;
        }

        .header h1 {
            font-size: 32px;
            font-weight: 300;
            color: #2D3748;
            margin-bottom: 8px;
            text-shadow: 2px 2px 4px rgba(176, 190, 197, 0.6);
        }

        .header .date {
            font-size: 14px;
            color: #718096;
        }

        .timeline {
            flex: 1;
            overflow-y: auto;
            padding: 0 24px;
            padding-bottom: 100px;
        }

        .note-card {
            background: #E0E5EC;
            border-radius: 20px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 
                inset 5px 5px 10px #a3b1c6,
                inset -5px -5px 10px #ffffff;
            transition: all 0.3s ease;
        }

        .note-card:hover {
            box-shadow: 
                inset 3px 3px 6px #a3b1c6,
                inset -3px -3px 6px #ffffff;
        }

        .note-time {
            font-size: 12px;
            color: #718096;
            margin-bottom: 12px;
        }

        .note-title {
            font-size: 20px;
            font-weight: 400;
            color: #2D3748;
            margin-bottom: 12px;
            line-height: 1.4;
        }

        .note-content {
            font-size: 15px;
            color: #4A5568;
            line-height: 1.6;
            margin-bottom: 16px;
        }

        .note-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .tag {
            font-size: 12px;
            color: #4A5568;
            background: #E0E5EC;
            padding: 6px 16px;
            border-radius: 20px;
            box-shadow: 
                3px 3px 6px #a3b1c6,
                -3px -3px 6px #ffffff;
        }

        .bottom-nav {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: #E0E5EC;
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding-bottom: 20px;
            box-shadow: 
                0 -5px 10px #a3b1c6,
                0 5px 10px #ffffff;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 6px;
            color: #718096;
            transition: all 0.3s ease;
            cursor: pointer;
            padding: 8px 16px;
            border-radius: 15px;
        }

        .nav-item.active {
            color: #667EEA;
            box-shadow: 
                inset 2px 2px 5px #a3b1c6,
                inset -2px -2px 5px #ffffff;
        }

        .nav-icon {
            width: 26px;
            height: 26px;
            fill: currentColor;
        }

        .nav-text {
            font-size: 11px;
        }

        .fab {
            position: absolute;
            bottom: 100px;
            right: 24px;
            width: 60px;
            height: 60px;
            background: #E0E5EC;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 
                8px 8px 16px #a3b1c6,
                -8px -8px 16px #ffffff;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .fab:hover {
            box-shadow: 
                5px 5px 10px #a3b1c6,
                -5px -5px 10px #ffffff;
        }

        .fab:active {
            box-shadow: 
                inset 3px 3px 6px #a3b1c6,
                inset -3px -3px 6px #ffffff;
        }

        .fab-icon {
            width: 28px;
            height: 28px;
            fill: #667EEA;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #718096;
        }

        .empty-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            opacity: 0.5;
        }

        .empty-text {
            font-size: 14px;
            line-height: 1.6;
        }

        /* 滚动条样式 */
        .timeline::-webkit-scrollbar {
            width: 6px;
        }

        .timeline::-webkit-scrollbar-track {
            background: #E0E5EC;
        }

        .timeline::-webkit-scrollbar-thumb {
            background: #CBD5E0;
            border-radius: 3px;
        }

        .search-bar {
            margin: 0 24px 20px;
            height: 48px;
            background: #E0E5EC;
            border-radius: 25px;
            display: flex;
            align-items: center;
            padding: 0 20px;
            box-shadow: 
                inset 3px 3px 6px #a3b1c6,
                inset -3px -3px 6px #ffffff;
        }

        .search-icon {
            width: 20px;
            height: 20px;
            fill: #718096;
            margin-right: 12px;
        }

        .search-input {
            flex: 1;
            border: none;
            background: none;
            outline: none;
            color: #2D3748;
            font-size: 15px;
        }

        .search-input::placeholder {
            color: #A0AEC0;
        }

        .category-dot {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .category-work { background: #F687B3; }
        .category-life { background: #667EEA; }
        .category-study { background: #48BB78; }
        .category-plan { background: #ED8936; }
    </style>
</head>
<body>
    <div class="phone-container">
        <div class="status-bar">
            <span>9:41</span>
            <span>●●●●●</span>
            <span>100%</span>
        </div>
        
        <div class="content">
            <div class="header">
                <h1>时光拾光</h1>
                <div class="date">2024年3月15日 星期五</div>
            </div>
            
            <div class="search-bar">
                <svg class="search-icon" viewBox="0 0 24 24">
                    <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                </svg>
                <input type="text" class="search-input" placeholder="搜索笔记...">
            </div>
            
            <div class="timeline">
                <div class="note-card">
                    <div class="note-time">下午 2:30</div>
                    <div class="note-title">
                        <span class="category-dot category-life"></span>
                        春日随笔
                    </div>
                    <div class="note-content">今天在公园里看到了第一朵樱花，春天真的来了。阳光透过花瓣洒在地面上，形成斑驳的光影。</div>
                    <div class="note-tags">
                        <span class="tag">生活感悟</span>
                        <span class="tag">春天</span>
                    </div>
                </div>
                
                <div class="note-card">
                    <div class="note-time">上午 10:15</div>
                    <div class="note-title">
                        <span class="category-dot category-work"></span>
                        项目会议纪要
                    </div>
                    <div class="note-content">讨论了新产品的设计方案，确定了以用户体验为核心的设计理念。需要在下周完成原型设计。</div>
                    <div class="note-tags">
                        <span class="tag">工作</span>
                        <span class="tag">会议</span>
                    </div>
                </div>
                
                <div class="note-card">
                    <div class="note-time">昨天 晚上 8:45</div>
                    <div class="note-title">
                        <span class="category-dot category-study"></span>
                        读书笔记
                    </div>
                    <div class="note-content">《设计心理学》第三章：好的设计应该是直观的，用户不需要思考就能知道如何使用。</div>
                    <div class="note-tags">
                        <span class="tag">学习</span>
                        <span class="tag">设计</span>
                    </div>
                </div>
                
                <div class="note-card">
                    <div class="note-time">3月13日 下午 4:20</div>
                    <div class="note-title">
                        <span class="category-dot category-plan"></span>
                        周末计划
                    </div>
                    <div class="note-content">1. 去美术馆看展 2. 整理房间 3. 给父母打电话 4. 准备下周的演讲稿</div>
                    <div class="note-tags">
                        <span class="tag">计划</span>
                        <span class="tag">周末</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="bottom-nav">
            <div class="nav-item active">
                <svg class="nav-icon" viewBox="0 0 24 24">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
                <span class="nav-text">时间轴</span>
            </div>
            <div class="nav-item">
                <svg class="nav-icon" viewBox="0 0 24 24">
                    <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"/>
                </svg>
                <span class="nav-text">日历</span>
            </div>
            <div class="nav-item">
                <svg class="nav-icon" viewBox="0 0 24 24">
                    <path d="M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6z"/>
                </svg>
                <span class="nav-text">统计</span>
            </div>
            <div class="nav-item">
                <svg class="nav-icon" viewBox="0 0 24 24">
                    <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                </svg>
                <span class="nav-text">我的</span>
            </div>
        </div>
        
        <div class="fab">
            <svg class="fab-icon" viewBox="0 0 24 24">
                <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
            </svg>
        </div>
    </div>
</body>
</html>