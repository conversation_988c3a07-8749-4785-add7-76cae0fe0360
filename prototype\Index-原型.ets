// TimeNotes主页面 - ArkUI原型
// 文件路径: TimeNotes/entry/src/main/ets/pages/Index.ets

import router from '@ohos.router'

@Entry
@Component
struct Index {
  @State currentCategory: string = '全部时光'
  @State notes: NoteModel[] = [
    {
      id: 1,
      title: '今天的好心情',
      content: '今天天气真好，阳光明媚。早上起床后看到窗外的阳光，心情一下子就变好了。决定去公园散步，享受这美好的时光...',
      category: '生活时光',
      mood: '😊',
      time: '10:30',
      categoryColor: '#e8f5e9'
    },
    {
      id: 2,
      title: '项目会议纪要',
      content: '今天的项目进展会议讨论了以下几个要点：\n1. 前端开发进度需要加快\n2. 后端API接口已经完成80%\n3. 下周开始进行联调测试',
      category: '工作时光',
      mood: '💼',
      time: '14:15',
      categoryColor: '#fce4ec'
    },
    {
      id: 3,
      title: '学习笔记 - ArkUI',
      content: '今天学习了ArkUI的状态管理：\n- @State: 组件内部状态\n- @Prop: 父子组件传值\n- @Link: 双向数据绑定\n- @Observed: 对象状态监听',
      category: '学习时光',
      mood: '📚',
      time: '20:45',
      categoryColor: '#fff3e0'
    }
  ]

  categories: CategoryModel[] = [
    { name: '全部时光', count: 128, color: '#667eea' },
    { name: '工作时光', count: 45, color: '#fce4ec' },
    { name: '生活时光', count: 38, color: '#e8f5e9' },
    { name: '学习时光', count: 32, color: '#fff3e0' },
    { name: '心情日记', count: 13, color: '#f3e5f5' }
  ]

  build() {
    Column() {
      // 顶部栏
      Row() {
        Text('TimeNotes')
          .fontSize(24)
          .fontWeight(FontWeight.Bold)
          .fontColor(Color.White)
        
        Blank()
        
        Row({ space: 16 }) {
          Image($r('app.media.search'))
            .width(24)
            .height(24)
            .fillColor(Color.White)
            .onClick(() => {
              // 跳转到搜索页
            })
          
          Image($r('app.media.calendar'))
            .width(24)
            .height(24)
            .fillColor(Color.White)
            .onClick(() => {
              // 切换到日历视图
            })
          
          Image($r('app.media.more'))
            .width(24)
            .height(24)
            .fillColor(Color.White)
            .onClick(() => {
              // 显示菜单
            })
        }
      }
      .width('100%')
      .padding({ left: 20, right: 20, top: 16, bottom: 16 })
      .linearGradient({
        direction: GradientDirection.Right,
        colors: [['#667eea', 0.0], ['#764ba2', 1.0]]
      })

      // 分类标签
      Scroll() {
        Row({ space: 8 }) {
          ForEach(this.categories, (category: CategoryModel) => {
            Text(`${category.name} (${category.count})`)
              .fontSize(14)
              .padding({ left: 16, right: 16, top: 8, bottom: 8 })
              .backgroundColor(this.currentCategory === category.name ? '#667eea' : '#f0f0f0')
              .fontColor(this.currentCategory === category.name ? Color.White : Color.Black)
              .borderRadius(20)
              .onClick(() => {
                this.currentCategory = category.name
              })
          })
        }
        .padding({ left: 16, right: 16 })
      }
      .scrollable(ScrollDirection.Horizontal)
      .scrollBar(BarState.Off)
      .width('100%')
      .backgroundColor(Color.White)

      // 时光流列表
      List({ space: 12 }) {
        ForEach(this.notes, (note: NoteModel) => {
          ListItem() {
            NoteCard({ note: note })
          }
          .onClick(() => {
            // 跳转到编辑页
            router.pushUrl({
              url: 'pages/NoteEdit',
              params: { noteId: note.id }
            })
          })
        })
      }
      .layoutWeight(1)
      .padding({ left: 16, right: 16, top: 12 })
      .backgroundColor('#f5f5f5')

      // 底部导航
      Row() {
        ForEach([
          { icon: $r('app.media.timeline'), text: '时光流', active: true },
          { icon: $r('app.media.calendar'), text: '日历' },
          { icon: $r('app.media.stats'), text: '统计' },
          { icon: $r('app.media.profile'), text: '我的' }
        ], (item: any) => {
          Column() {
            Image(item.icon)
              .width(24)
              .height(24)
              .fillColor(item.active ? '#667eea' : '#999')
            
            Text(item.text)
              .fontSize(10)
              .fontColor(item.active ? '#667eea' : '#999')
              .margin({ top: 4 })
          }
          .width('25%')
          .height(60)
          .justifyContent(FlexAlign.Center)
          .onClick(() => {
            // 切换页面
          })
        })
      }
      .width('100%')
      .backgroundColor(Color.White)
      .border({ width: { top: 1 }, color: '#f0f0f0' })
    }
    .height('100%')
  }
}

// 笔记卡片组件
@Component
struct NoteCard {
  @Prop note: NoteModel

  build() {
    Column() {
      Row() {
        Text(this.note.title)
          .fontSize(18)
          .fontWeight(FontWeight.Medium)
          .maxLines(1)
          .textOverflow({ overflow: TextOverflow.Ellipsis })
          .layoutWeight(1)
        
        Text(this.note.time)
          .fontSize(12)
          .fontColor('#999')
      }
      .width('100%')
      .margin({ bottom: 8 })

      Text(this.note.content)
        .fontSize(14)
        .fontColor('#666')
        .lineHeight(20)
        .maxLines(3)
        .textOverflow({ overflow: TextOverflow.Ellipsis })
        .margin({ bottom: 12 })

      Row() {
        Text(this.note.category)
          .fontSize(12)
          .padding({ left: 8, right: 8, top: 4, bottom: 4 })
          .backgroundColor(this.note.categoryColor + '40')
          .fontColor(this.note.categoryColor)
          .borderRadius(12)
        
        Blank()
        
        Text(this.note.mood)
          .fontSize(16)
      }
      .width('100%')
    }
    .width('100%')
    .padding(16)
    .backgroundColor(Color.White)
    .borderRadius(12)
    .shadow({ radius: 6, color: '#000000', offsetX: 0, offsetY: 2 })
  }
}

// 数据模型定义
class NoteModel {
  id: number
  title: string
  content: string
  category: string
  mood: string
  time: string
  categoryColor: string

  constructor(id: number, title: string, content: string, category: string, 
              mood: string, time: string, categoryColor: string) {
    this.id = id
    this.title = title
    this.content = content
    this.category = category
    this.mood = mood
    this.time = time
    this.categoryColor = categoryColor
  }
}

class CategoryModel {
  name: string
  count: number
  color: string

  constructor(name: string, count: number, color: string) {
    this.name = name
    this.count = count
    this.color = color
  }
}