// 日期时间工具类
export class DateUtils {
  /**
   * 格式化日期
   * @param date 日期对象
   * @param format 格式化字符串
   */
  static formatDate(date: Date, format: string = 'YYYY-MM-DD'): string {
    const year = date.getFullYear();
    const month = this.padZero(date.getMonth() + 1);
    const day = this.padZero(date.getDate());
    const hours = this.padZero(date.getHours());
    const minutes = this.padZero(date.getMinutes());
    const seconds = this.padZero(date.getSeconds());
    
    return format
      .replace('YYYY', year.toString())
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hours)
      .replace('mm', minutes)
      .replace('ss', seconds);
  }
  
  /**
   * 获取当前日期时间字符串
   */
  static now(): string {
    return this.formatDate(new Date(), 'YYYY-MM-DD HH:mm:ss');
  }
  
  /**
   * 获取当前日期字符串
   */
  static today(): string {
    return this.formatDate(new Date(), 'YYYY-MM-DD');
  }
  
  /**
   * 数字补零
   */
  static padZero(num: number): string {
    return num < 10 ? `0${num}` : `${num}`;
  }
  
  /**
   * 计算两个日期之间的天数
   */
  static daysBetween(startDate: string, endDate: string): number {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }
  
  /**
   * 获取友好的时间显示
   */
  static getFriendlyTime(timeStr: string): string {
    const date = new Date(timeStr);
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (minutes < 1) return '刚刚';
    if (minutes < 60) return `${minutes}分钟前`;
    if (hours < 24) return `${hours}小时前`;
    if (days < 7) return `${days}天前`;
    
    return this.formatDate(date, 'MM月DD日');
  }
  
  /**
   * 获取星期几
   */
  static getWeekday(date: string): string {
    const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
    return weekdays[new Date(date).getDay()];
  }

  /**
   * 获取月份的第一天
   */
  static getFirstDayOfMonth(date: Date): Date {
    return new Date(date.getFullYear(), date.getMonth(), 1);
  }

  /**
   * 获取月份的最后一天
   */
  static getLastDayOfMonth(date: Date): Date {
    return new Date(date.getFullYear(), date.getMonth() + 1, 0);
  }

  /**
   * 获取月份的天数
   */
  static getDaysInMonth(date: Date): number {
    return this.getLastDayOfMonth(date).getDate();
  }

  /**
   * 获取月份的第一天是星期几（0=星期日，1=星期一，...）
   */
  static getFirstDayOfWeek(date: Date): number {
    return this.getFirstDayOfMonth(date).getDay();
  }

  /**
   * 获取月份的日历数组（包含上个月和下个月的部分日期）
   */
  static getMonthCalendar(date: Date): Array<{ date: Date; isCurrentMonth: boolean; isToday: boolean }> {
    const firstDay = this.getFirstDayOfMonth(date);
    const lastDay = this.getLastDayOfMonth(date);
    const firstDayOfWeek = firstDay.getDay();
    
    const calendar = [];
    
    // 添加上个月的日期
    const prevMonth = new Date(date.getFullYear(), date.getMonth() - 1, 0);
    for (let i = firstDayOfWeek - 1; i >= 0; i--) {
      const day = prevMonth.getDate() - i;
      const prevDate = new Date(date.getFullYear(), date.getMonth() - 1, day);
      calendar.push({
        date: prevDate,
        isCurrentMonth: false,
        isToday: this.isToday(prevDate)
      });
    }
    
    // 添加当前月的日期
    const today = new Date();
    for (let i = 1; i <= lastDay.getDate(); i++) {
      const currentDate = new Date(date.getFullYear(), date.getMonth(), i);
      calendar.push({
        date: currentDate,
        isCurrentMonth: true,
        isToday: this.isToday(currentDate)
      });
    }
    
    // 添加下个月的日期
    const totalCells = 42; // 6行 x 7列
    const remaining = totalCells - calendar.length;
    for (let i = 1; i <= remaining; i++) {
      const nextDate = new Date(date.getFullYear(), date.getMonth() + 1, i);
      calendar.push({
        date: nextDate,
        isCurrentMonth: false,
        isToday: this.isToday(nextDate)
      });
    }
    
    return calendar;
  }

  /**
   * 判断是否为今天
   */
  static isToday(date: Date): boolean {
    const today = new Date();
    return date.getFullYear() === today.getFullYear() &&
           date.getMonth() === today.getMonth() &&
           date.getDate() === today.getDate();
  }

  /**
   * 月份加减
   */
  static addMonths(date: Date, months: number): Date {
    return new Date(date.getFullYear(), date.getMonth() + months, date.getDate());
  }

  /**
   * 格式化月份显示
   */
  static formatMonthYear(date: Date): string {
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    return `${year}年${month}月`;
  }

  /**
   * 格式化时间显示
   */
  static formatTime(date: Date): string {
    const hours = this.padZero(date.getHours());
    const minutes = this.padZero(date.getMinutes());
    const seconds = this.padZero(date.getSeconds());
    return `${hours}:${minutes}:${seconds}`;
  }
  
  /**
   * 格式化日期时间显示
   */
  static formatDateTime(date: Date): string {
    return this.formatDate(date, 'YYYY-MM-DD HH:mm:ss');
  }
  
  /**
   * 获取倒计时文本
   */
  static getCountdownText(targetDate: string): string {
    const now = new Date();
    const target = new Date(targetDate);
    const diffTime = target.getTime() - now.getTime();
    
    if (diffTime <= 0) {
      return '已到期';
    }
    
    const days = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diffTime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diffTime % (1000 * 60 * 60)) / (1000 * 60));
    
    if (days > 0) {
      return `${days}天${hours}小时`;
    } else if (hours > 0) {
      return `${hours}小时${minutes}分钟`;
    } else {
      return `${minutes}分钟`;
    }
  }
  
  /**
   * 检查日期是否已过期
   */
  static isExpired(targetDate: string): boolean {
    const now = new Date();
    const target = new Date(targetDate);
    return now.getTime() >= target.getTime();
  }
  
  /**
   * 获取剩余天数
   */
  static getRemainingDays(targetDate: string): number {
    const now = new Date();
    const target = new Date(targetDate);
    const diffTime = target.getTime() - now.getTime();
    return Math.max(0, Math.ceil(diffTime / (1000 * 60 * 60 * 24)));
  }
}