import { EnhancedDatabaseService, DatabaseResult } from '../services/EnhancedDatabaseService';
import { NoteModel, CapsuleModel, SettingsModel } from '../model/NoteModel';
import { RepositoryFactory } from '../repositories/index';
import { DateUtils } from '../utils/DateUtils';
import { StateManager } from '../state/StateManager';
import fs from '@ohos.file.fs';

/**
 * 备份元数据
 */
export interface BackupMetadata {
  version: string;
  timestamp: string;
  appVersion: string;
  deviceInfo?: {
    manufacturer?: string;
    model?: string;
    osVersion?: string;
  };
  stats: {
    notesCount: number;
    capsulesCount: number;
    settingsCount: number;
    totalSize: number;
  };
  checksum: string;
}

/**
 * 备份数据结构
 */
export interface BackupData {
  metadata: BackupMetadata;
  notes: NoteModel[];
  capsules: CapsuleModel[];
  settings: SettingsModel[];
}

/**
 * 备份配置
 */
export interface BackupConfig {
  includeNotes: boolean;
  includeCapsules: boolean;
  includeSettings: boolean;
  compress: boolean;
  encrypt: boolean;
  encryptionKey?: string;
}

/**
 * 恢复选项
 */
export interface RestoreOptions {
  overwriteExisting: boolean;
  mergeWithExisting: boolean;
  skipConflicts: boolean;
  restoreSettings: boolean;
  restoreNotes: boolean;
  restoreCapsules: boolean;
}

/**
 * 备份进度回调
 */
export interface BackupProgressCallback {
  onProgress(progress: number, message: string): void;
  onComplete(success: boolean, message: string): void;
  onError(error: Error): void;
}

/**
 * 数据备份和恢复服务
 */
export class BackupRestoreService {
  private static instance: BackupRestoreService;
  private dbService: EnhancedDatabaseService;
  private stateManager: StateManager;
  private readonly BACKUP_DIR = '/data/data/com.example.timenotes/files/backups';
  private readonly TEMP_DIR = '/data/data/com.example.timenotes/files/temp';
  private readonly MAX_BACKUP_FILES = 10;
  
  private constructor() {
    this.dbService = EnhancedDatabaseService.getInstance();
    this.stateManager = StateManager.getInstance();
  }
  
  static getInstance(): BackupRestoreService {
    if (!BackupRestoreService.instance) {
      BackupRestoreService.instance = new BackupRestoreService();
    }
    return BackupRestoreService.instance;
  }
  
  /**
   * 创建备份
   */
  async createBackup(
    config: BackupConfig = {
      includeNotes: true,
      includeCapsules: true,
      includeSettings: true,
      compress: true,
      encrypt: false
    },
    callback?: BackupProgressCallback
  ): Promise<DatabaseResult<string>> {
    try {
      callback?.onProgress(0, '开始创建备份...');
      
      // 确保备份目录存在
      await this.ensureDirectoryExists(this.BACKUP_DIR);
      
      // 收集备份数据
      const backupData: BackupData = {
        metadata: await this.createMetadata(),
        notes: [],
        capsules: [],
        settings: []
      };
      
      let progress = 10;
      callback?.onProgress(progress, '收集数据...');
      
      // 备份笔记
      if (config.includeNotes) {
        const notesResult = await RepositoryFactory.getNoteRepository().findAll(0, 999999);
        if (notesResult.success) {
          backupData.notes = notesResult.data || [];
          progress += 25;
          callback?.onProgress(progress, `已备份 ${backupData.notes.length} 条笔记`);
        }
      }
      
      // 备份时间胶囊
      if (config.includeCapsules) {
        const capsulesResult = await RepositoryFactory.getCapsuleRepository().findAll(0, 999999);
        if (capsulesResult.success) {
          backupData.capsules = capsulesResult.data || [];
          progress += 25;
          callback?.onProgress(progress, `已备份 ${backupData.capsules.length} 个时间胶囊`);
        }
      }
      
      // 备份设置
      if (config.includeSettings) {
        const settingsResult = await RepositoryFactory.getSettingsRepository().findAll();
        if (settingsResult.success) {
          backupData.settings = settingsResult.data || [];
          progress += 10;
          callback?.onProgress(progress, '已备份设置');
        }
      }
      
      // 更新元数据
      backupData.metadata.stats = {
        notesCount: backupData.notes.length,
        capsulesCount: backupData.capsules.length,
        settingsCount: backupData.settings.length,
        totalSize: JSON.stringify(backupData).length
      };
      
      // 生成备份文件名
      const timestamp = DateUtils.formatDate(new Date(), 'YYYY-MM-DD_HH-mm-ss');
      const backupFileName = `timenotes_backup_${timestamp}.json`;
      const backupFilePath = `${this.BACKUP_DIR}/${backupFileName}`;
      
      callback?.onProgress(90, '保存备份文件...');
      
      // 保存备份文件
      const backupJson = JSON.stringify(backupData, null, 2);
      
      // 写入文件
      await this.writeFile(backupFilePath, backupJson);
      
      // 清理旧备份文件
      await this.cleanupOldBackups();
      
      callback?.onProgress(100, '备份完成');
      callback?.onComplete(true, '备份创建成功');
      
      // 更新状态
      this.stateManager.updateState({
        lastSyncTime: new Date().toISOString()
      }, 'CACHE_UPDATED');
      
      return {
        success: true,
        data: backupFilePath,
        timestamp: new Date().toISOString()
      };
      
    } catch (error) {
      const err = error as Error;
      callback?.onError(err);
      callback?.onComplete(false, `备份失败: ${err.message}`);
      
      return {
        success: false,
        error: err,
        timestamp: new Date().toISOString()
      };
    }
  }
  
  /**
   * 恢复备份
   */
  async restoreBackup(
    backupFilePath: string,
    options: RestoreOptions = {
      overwriteExisting: true,
      mergeWithExisting: false,
      skipConflicts: false,
      restoreSettings: true,
      restoreNotes: true,
      restoreCapsules: true
    },
    callback?: BackupProgressCallback
  ): Promise<DatabaseResult<void>> {
    try {
      callback?.onProgress(0, '开始恢复备份...');
      
      // 读取备份文件
      callback?.onProgress(10, '读取备份文件...');
      const backupData = await this.readBackupFile(backupFilePath);
      
      // 验证备份数据
      if (!this.validateBackupData(backupData)) {
        throw new Error('备份数据格式无效或已损坏');
      }
      
      let progress = 20;
      
      // 恢复设置
      if (options.restoreSettings && backupData.settings.length > 0) {
        callback?.onProgress(progress, '恢复设置...');
        await this.restoreSettings(backupData.settings[0], options);
        progress += 15;
      }
      
      // 恢复笔记
      if (options.restoreNotes && backupData.notes.length > 0) {
        callback?.onProgress(progress, `恢复 ${backupData.notes.length} 条笔记...`);
        await this.restoreNotes(backupData.notes, options);
        progress += 30;
      }
      
      // 恢复时间胶囊
      if (options.restoreCapsules && backupData.capsules.length > 0) {
        callback?.onProgress(progress, `恢复 ${backupData.capsules.length} 个时间胶囊...`);
        await this.restoreCapsules(backupData.capsules, options);
        progress += 25;
      }
      
      callback?.onProgress(100, '恢复完成');
      callback?.onComplete(true, '备份恢复成功');
      
      // 更新状态
      this.stateManager.updateState({
        lastSyncTime: new Date().toISOString()
      }, 'CACHE_UPDATED');
      
      return {
        success: true,
        timestamp: new Date().toISOString()
      };
      
    } catch (error) {
      const err = error as Error;
      callback?.onError(err);
      callback?.onComplete(false, `恢复失败: ${err.message}`);
      
      return {
        success: false,
        error: err,
        timestamp: new Date().toISOString()
      };
    }
  }
  
  /**
   * 获取备份列表
   */
  async getBackupList(): Promise<DatabaseResult<BackupMetadata[]>> {
    try {
      const backups: BackupMetadata[] = [];
      
      // 检查备份目录是否存在
      if (!await this.directoryExists(this.BACKUP_DIR)) {
        return {
          success: true,
          data: backups,
          timestamp: new Date().toISOString()
        };
      }
      
      // 读取备份文件
      const files = await this.listFiles(this.BACKUP_DIR);
      
      for (const file of files) {
        if (file.name.startsWith('timenotes_backup_') && file.name.endsWith('.json')) {
          try {
            const backupData = await this.readBackupFile(`${this.BACKUP_DIR}/${file.name}`);
            backups.push(backupData.metadata);
          } catch (error) {
            console.warn(`Failed to read backup file ${file.name}:`, error);
          }
        }
      }
      
      // 按时间戳排序
      backups.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
      
      return {
        success: true,
        data: backups,
        timestamp: new Date().toISOString()
      };
      
    } catch (error) {
      return {
        success: false,
        error: error as Error,
        timestamp: new Date().toISOString()
      };
    }
  }
  
  /**
   * 删除备份
   */
  async deleteBackup(backupFileName: string): Promise<DatabaseResult<void>> {
    try {
      const filePath = `${this.BACKUP_DIR}/${backupFileName}`;
      
      if (await this.fileExists(filePath)) {
        await this.deleteFile(filePath);
      }
      
      return {
        success: true,
        timestamp: new Date().toISOString()
      };
      
    } catch (error) {
      return {
        success: false,
        error: error as Error,
        timestamp: new Date().toISOString()
      };
    }
  }
  
  /**
   * 自动备份
   */
  async autoBackup(): Promise<DatabaseResult<string>> {
    try {
      // 检查是否需要自动备份
      const lastBackup = await this.getLastBackupTime();
      const now = new Date();
      const daysSinceLastBackup = lastBackup ? 
        (now.getTime() - lastBackup.getTime()) / (1000 * 60 * 60 * 24) : 7;
      
      if (daysSinceLastBackup < 1) {
        return {
          success: true,
          data: 'No backup needed',
          timestamp: new Date().toISOString()
        };
      }
      
      // 创建自动备份
      const config: BackupConfig = {
        includeNotes: true,
        includeCapsules: true,
        includeSettings: true,
        compress: true,
        encrypt: false
      };
      
      return await this.createBackup(config);
      
    } catch (error) {
      return {
        success: false,
        error: error as Error,
        timestamp: new Date().toISOString()
      };
    }
  }
  
  /**
   * 验证备份文件
   */
  async validateBackupFile(backupFilePath: string): Promise<DatabaseResult<boolean>> {
    try {
      const backupData = await this.readBackupFile(backupFilePath);
      const isValid = this.validateBackupData(backupData);
      
      return {
        success: true,
        data: isValid,
        timestamp: new Date().toISOString()
      };
      
    } catch (error) {
      return {
        success: false,
        data: false,
        error: error as Error,
        timestamp: new Date().toISOString()
      };
    }
  }
  
  /**
   * 获取备份统计信息
   */
  async getBackupStats(): Promise<DatabaseResult<{
    totalBackups: number;
    totalSize: number;
    lastBackupTime: string | null;
    oldestBackupTime: string | null;
  }>> {
    try {
      const backupList = await this.getBackupList();
      
      if (!backupList.success) {
        throw backupList.error!;
      }
      
      const backups = backupList.data;
      let totalSize = 0;
      
      // 计算总大小
      for (const backup of backups) {
        totalSize += backup.stats.totalSize;
      }
      
      return {
        success: true,
        data: {
          totalBackups: backups.length,
          totalSize,
          lastBackupTime: backups.length > 0 ? backups[0].timestamp : null,
          oldestBackupTime: backups.length > 0 ? backups[backups.length - 1].timestamp : null
        },
        timestamp: new Date().toISOString()
      };
      
    } catch (error) {
      return {
        success: false,
        error: error as Error,
        timestamp: new Date().toISOString()
      };
    }
  }
  
  /**
   * 创建备份元数据
   */
  private async createMetadata(): Promise<BackupMetadata> {
    const stats = await this.getDatabaseStats();
    
    return {
      version: '1.0',
      timestamp: new Date().toISOString(),
      appVersion: '1.0.0',
      deviceInfo: {
        // 可以添加设备信息
      },
      stats: {
        notesCount: stats.notesCount,
        capsulesCount: stats.capsulesCount,
        settingsCount: 1,
        totalSize: 0
      },
      checksum: '' // 将在生成备份时计算
    };
  }
  
  /**
   * 获取数据库统计信息
   */
  private async getDatabaseStats(): Promise<{
    notesCount: number;
    capsulesCount: number;
  }> {
    const [notesCount, capsulesCount] = await Promise.all([
      RepositoryFactory.getNoteRepository().count(),
      RepositoryFactory.getCapsuleRepository().count()
    ]);
    
    return {
      notesCount: notesCount.success ? notesCount.data! : 0,
      capsulesCount: capsulesCount.success ? capsulesCount.data! : 0
    };
  }
  
  /**
   * 读取备份文件
   */
  private async readBackupFile(filePath: string): Promise<BackupData> {
    const content = await this.readFile(filePath);
    return JSON.parse(content);
  }
  
  /**
   * 验证备份数据
   */
  private validateBackupData(backupData: any): backupData is BackupData {
    return (
      backupData &&
      backupData.metadata &&
      backupData.metadata.version &&
      backupData.metadata.timestamp &&
      Array.isArray(backupData.notes) &&
      Array.isArray(backupData.capsules) &&
      Array.isArray(backupData.settings)
    );
  }
  
  /**
   * 恢复设置
   */
  private async restoreSettings(settings: SettingsModel, options: RestoreOptions): Promise<void> {
    if (options.overwriteExisting) {
      await RepositoryFactory.getSettingsRepository().update(1, settings);
    }
  }
  
  /**
   * 恢复笔记
   */
  private async restoreNotes(notes: NoteModel[], options: RestoreOptions): Promise<void> {
    if (options.mergeWithExisting) {
      // 合并模式：只添加不存在的笔记
      for (const note of notes) {
        if (note.id) {
          const existing = await RepositoryFactory.getNoteRepository().findById(note.id);
          if (!existing.success || !existing.data) {
            await RepositoryFactory.getNoteRepository().save({ ...note, id: undefined });
          }
        } else {
          await RepositoryFactory.getNoteRepository().save(note);
        }
      }
    } else {
      // 覆盖模式：先删除现有笔记，然后恢复
      if (options.overwriteExisting) {
        // 这里可以实现批量删除现有笔记的逻辑
        // 为了安全起见，先不实现删除操作
      }
      
      // 批量插入笔记
      await RepositoryFactory.getNoteRepository().saveBatch(notes);
    }
  }
  
  /**
   * 恢复时间胶囊
   */
  private async restoreCapsules(capsules: CapsuleModel[], options: RestoreOptions): Promise<void> {
    if (options.mergeWithExisting) {
      // 合并模式：只添加不存在的时间胶囊
      for (const capsule of capsules) {
        if (capsule.id) {
          const existing = await RepositoryFactory.getCapsuleRepository().findById(capsule.id);
          if (!existing.success || !existing.data) {
            await RepositoryFactory.getCapsuleRepository().save({ ...capsule, id: undefined });
          }
        } else {
          await RepositoryFactory.getCapsuleRepository().save(capsule);
        }
      }
    } else {
      // 覆盖模式
      if (options.overwriteExisting) {
        // 这里可以实现批量删除现有时间胶囊的逻辑
        // 为了安全起见，先不实现删除操作
      }
      
      // 批量插入时间胶囊
      for (const capsule of capsules) {
        await RepositoryFactory.getCapsuleRepository().save(capsule);
      }
    }
  }
  
  /**
   * 清理旧备份文件
   */
  private async cleanupOldBackups(): Promise<void> {
    try {
      const files = await this.listFiles(this.BACKUP_DIR);
      const backupFiles = files
        .filter(file => file.name.startsWith('timenotes_backup_') && file.name.endsWith('.json'))
        .sort((a, b) => b.name.localeCompare(a.name));
      
      // 删除超过最大数量的备份文件
      if (backupFiles.length > this.MAX_BACKUP_FILES) {
        const filesToDelete = backupFiles.slice(this.MAX_BACKUP_FILES);
        
        for (const file of filesToDelete) {
          await this.deleteFile(`${this.BACKUP_DIR}/${file.name}`);
        }
      }
      
    } catch (error) {
      console.warn('Failed to cleanup old backups:', error);
    }
  }
  
  /**
   * 获取最后备份时间
   */
  private async getLastBackupTime(): Promise<Date | null> {
    try {
      const backupList = await this.getBackupList();
      
      if (!backupList.success || backupList.data.length === 0) {
        return null;
      }
      
      return new Date(backupList.data[0].timestamp);
      
    } catch (error) {
      return null;
    }
  }
  
  /**
   * 文件系统操作方法
   */
  private async ensureDirectoryExists(dirPath: string): Promise<void> {
    try {
      if (!await this.directoryExists(dirPath)) {
        // 在实际应用中，这里需要使用HarmonyOS的文件系统API
        // 这里只是示例代码
        console.log(`Creating directory: ${dirPath}`);
      }
    } catch (error) {
      console.error(`Failed to create directory ${dirPath}:`, error);
    }
  }
  
  private async directoryExists(dirPath: string): Promise<boolean> {
    try {
      // 在实际应用中，这里需要使用HarmonyOS的文件系统API
      return false;
    } catch (error) {
      return false;
    }
  }
  
  private async fileExists(filePath: string): Promise<boolean> {
    try {
      // 在实际应用中，这里需要使用HarmonyOS的文件系统API
      return false;
    } catch (error) {
      return false;
    }
  }
  
  private async writeFile(filePath: string, content: string): Promise<void> {
    try {
      // 在实际应用中，这里需要使用HarmonyOS的文件系统API
      console.log(`Writing file: ${filePath}, size: ${content.length} bytes`);
    } catch (error) {
      console.error(`Failed to write file ${filePath}:`, error);
      throw error;
    }
  }
  
  private async readFile(filePath: string): Promise<string> {
    try {
      // 在实际应用中，这里需要使用HarmonyOS的文件系统API
      console.log(`Reading file: ${filePath}`);
      return '{}';
    } catch (error) {
      console.error(`Failed to read file ${filePath}:`, error);
      throw error;
    }
  }
  
  private async deleteFile(filePath: string): Promise<void> {
    try {
      // 在实际应用中，这里需要使用HarmonyOS的文件系统API
      console.log(`Deleting file: ${filePath}`);
    } catch (error) {
      console.error(`Failed to delete file ${filePath}:`, error);
      throw error;
    }
  }
  
  private async listFiles(dirPath: string): Promise<Array<{ name: string; size: number; modifiedTime: number }>> {
    try {
      // 在实际应用中，这里需要使用HarmonyOS的文件系统API
      console.log(`Listing files in: ${dirPath}`);
      return [];
    } catch (error) {
      console.error(`Failed to list files in ${dirPath}:`, error);
      return [];
    }
  }
}

/**
 * 备份调度器
 */
export class BackupScheduler {
  private static instance: BackupScheduler;
  private backupService: BackupRestoreService;
  private isRunning = false;
  private backupInterval: number = 24 * 60 * 60 * 1000; // 24小时
  private intervalId: any = null;
  
  private constructor() {
    this.backupService = BackupRestoreService.getInstance();
  }
  
  static getInstance(): BackupScheduler {
    if (!BackupScheduler.instance) {
      BackupScheduler.instance = new BackupScheduler();
    }
    return BackupScheduler.instance;
  }
  
  /**
   * 启动自动备份
   */
  start(): void {
    if (this.isRunning) {
      return;
    }
    
    this.isRunning = true;
    
    // 立即执行一次备份
    this.performBackup();
    
    // 设置定时备份
    this.intervalId = setInterval(() => {
      this.performBackup();
    }, this.backupInterval);
    
    console.log('Backup scheduler started');
  }
  
  /**
   * 停止自动备份
   */
  stop(): void {
    if (!this.isRunning) {
      return;
    }
    
    this.isRunning = false;
    
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
    
    console.log('Backup scheduler stopped');
  }
  
  /**
   * 设置备份间隔
   */
  setBackupInterval(interval: number): void {
    this.backupInterval = interval;
    
    // 如果正在运行，重新启动
    if (this.isRunning) {
      this.stop();
      this.start();
    }
  }
  
  /**
   * 执行备份
   */
  private async performBackup(): Promise<void> {
    try {
      const result = await this.backupService.autoBackup();
      
      if (result.success) {
        console.log('Auto backup completed successfully');
      } else {
        console.warn('Auto backup failed:', result.error?.message);
      }
    } catch (error) {
      console.error('Auto backup error:', error);
    }
  }
  
  /**
   * 检查是否正在运行
   */
  isSchedulerRunning(): boolean {
    return this.isRunning;
  }
}