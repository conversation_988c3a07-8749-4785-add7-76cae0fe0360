// 测试运行器
import { hilog } from '@kit.PerformanceAnalysisKit';
import { TestSuite, TestResult, TestReportGenerator } from '../utils/TestFramework';
import { DatabaseServiceTest } from './unit/DatabaseServiceTest';
import { UtilsTest } from './unit/UtilsTest';
import { NavigationIntegrationTest } from './integration/NavigationIntegrationTest';
import { UIInteractionTest } from './integration/UIInteractionTest';

export class TestRunner {
  private suites: TestSuite[] = [];
  private totalDuration: number = 0;

  async runAllTests(): Promise<void> {
    hilog.info(0x0000, 'Test', '🚀 开始运行所有测试...');
    const startTime = Date.now();

    try {
      // 运行单元测试
      await this.runUnitTests();
      
      // 运行集成测试
      await this.runIntegrationTests();
      
      // 生成测试报告
      this.generateTestReport();
      
      // 性能报告
      this.generatePerformanceReport();
      
    } catch (error) {
      hilog.error(0x0000, 'Test', '测试运行失败:', error);
    }

    this.totalDuration = Date.now() - startTime;
    hilog.info(0x0000, 'Test', `✅ 所有测试完成，总耗时: ${this.totalDuration}ms`);
  }

  private async runUnitTests(): Promise<void> {
    hilog.info(0x0000, 'Test', '📋 运行单元测试...');
    
    // 数据库服务测试
    const dbTest = new DatabaseServiceTest();
    const dbResults = await dbTest.run();
    const dbSuite = this.createTestSuite('DatabaseService', dbResults);
    this.suites.push(dbSuite);
    await dbTest.cleanup();

    // 工具类测试
    const utilsTest = new UtilsTest();
    const utilsResults = await utilsTest.run();
    const utilsSuite = this.createTestSuite('Utils', utilsResults);
    this.suites.push(utilsSuite);
  }

  private async runIntegrationTests(): Promise<void> {
    hilog.info(0x0000, 'Test', '🔗 运行集成测试...');
    
    // 导航集成测试
    const navTest = new NavigationIntegrationTest();
    const navResults = await navTest.run();
    const navSuite = this.createTestSuite('NavigationIntegration', navResults);
    this.suites.push(navSuite);

    // UI交互测试
    const uiTest = new UIInteractionTest();
    const uiResults = await uiTest.run();
    const uiSuite = this.createTestSuite('UIInteraction', uiResults);
    this.suites.push(uiSuite);
  }

  private createTestSuite(name: string, results: TestResult[]): TestSuite {
    const totalTests = results.length;
    const passedTests = results.filter(r => r.status === 'passed').length;
    const failedTests = results.filter(r => r.status === 'failed').length;
    const skippedTests = results.filter(r => r.status === 'skipped').length;
    const duration = results.reduce((sum, r) => sum + r.duration, 0);

    return {
      name,
      tests: results,
      totalTests,
      passedTests,
      failedTests,
      skippedTests,
      duration
    };
  }

  private generateTestReport(): void {
    const report = TestReportGenerator.generateReport(this.suites);
    hilog.info(0x0000, 'Test', '\n' + report);
    
    // 保存测试报告到文件
    this.saveReportToFile(report, 'test-report.txt');
  }

  private generatePerformanceReport(): void {
    const { TestUtils } = require('../utils/TestFramework');
    const performanceData = TestUtils.getPerformanceData();
    const report = TestReportGenerator.generatePerformanceReport(performanceData);
    hilog.info(0x0000, 'Test', '\n' + report);
    
    // 保存性能报告到文件
    this.saveReportToFile(report, 'performance-report.txt');
  }

  private saveReportToFile(report: string, filename: string): void {
    try {
      // 在实际环境中，这里会将报告保存到文件系统
      hilog.info(0x0000, 'Test', `📄 测试报告已保存到: ${filename}`);
      hilog.info(0x0000, 'Test', `报告内容长度: ${report.length} 字符`);
    } catch (error) {
      hilog.error(0x0000, 'Test', '保存测试报告失败:', error);
    }
  }

  // 获取测试结果摘要
  getTestSummary(): {
    totalSuites: number;
    totalTests: number;
    totalPassed: number;
    totalFailed: number;
    totalSkipped: number;
    totalDuration: number;
    successRate: number;
  } {
    const totalSuites = this.suites.length;
    const totalTests = this.suites.reduce((sum, suite) => sum + suite.totalTests, 0);
    const totalPassed = this.suites.reduce((sum, suite) => sum + suite.passedTests, 0);
    const totalFailed = this.suites.reduce((sum, suite) => sum + suite.failedTests, 0);
    const totalSkipped = this.suites.reduce((sum, suite) => sum + suite.skippedTests, 0);
    const totalDuration = this.suites.reduce((sum, suite) => sum + suite.duration, 0);
    const successRate = totalTests > 0 ? (totalPassed / totalTests) * 100 : 0;

    return {
      totalSuites,
      totalTests,
      totalPassed,
      totalFailed,
      totalSkipped,
      totalDuration,
      successRate
    };
  }

  // 获取失败的测试详情
  getFailedTests(): TestResult[] {
    const failedTests: TestResult[] = [];
    this.suites.forEach(suite => {
      suite.tests.forEach(test => {
        if (test.status === 'failed') {
          failedTests.push({
            ...test,
            name: `${suite.name}.${test.name}`
          });
        }
      });
    });
    return failedTests;
  }

  // 获取性能基准
  getPerformanceBenchmark(): {
    slowestTests: Array<{ name: string; duration: number }>;
    averageDuration: number;
    fastestSuite: string;
    slowestSuite: string;
  } {
    const allTests: Array<{ name: string; duration: number; suite: string }> = [];
    
    this.suites.forEach(suite => {
      suite.tests.forEach(test => {
        allTests.push({
          name: test.name,
          duration: test.duration,
          suite: suite.name
        });
      });
    });

    const slowestTests = allTests
      .sort((a, b) => b.duration - a.duration)
      .slice(0, 10)
      .map(test => ({ name: `${test.suite}.${test.name}`, duration: test.duration }));

    const averageDuration = allTests.reduce((sum, test) => sum + test.duration, 0) / allTests.length;
    
    const suiteDurations = this.suites.map(suite => ({
      name: suite.name,
      duration: suite.duration
    }));
    
    const fastestSuite = suiteDurations.reduce((min, suite) => 
      suite.duration < min.duration ? suite : min
    ).name;
    
    const slowestSuite = suiteDurations.reduce((max, suite) => 
      suite.duration > max.duration ? suite : max
    ).name;

    return {
      slowestTests,
      averageDuration,
      fastestSuite,
      slowestSuite
    };
  }
}

// 导出测试运行器
export default TestRunner;

// 如果直接运行此文件，执行测试
if (typeof process !== 'undefined' && process.env.NODE_ENV === 'test') {
  const runner = new TestRunner();
  runner.runAllTests().then(() => {
    const summary = runner.getTestSummary();
    console.log('\n=== 测试摘要 ===');
    console.log(`总测试套件: ${summary.totalSuites}`);
    console.log(`总测试数: ${summary.totalTests}`);
    console.log(`通过: ${summary.totalPassed}`);
    console.log(`失败: ${summary.totalFailed}`);
    console.log(`跳过: ${summary.totalSkipped}`);
    console.log(`通过率: ${summary.successRate.toFixed(2)}%`);
    console.log(`总耗时: ${summary.totalDuration}ms`);
    
    if (summary.totalFailed > 0) {
      console.log('\n=== 失败的测试 ===');
      const failedTests = runner.getFailedTests();
      failedTests.forEach(test => {
        console.log(`✗ ${test.name} (${test.duration}ms)`);
        if (test.error) {
          console.log(`  错误: ${test.error}`);
        }
      });
    }
    
    const benchmark = runner.getPerformanceBenchmark();
    console.log('\n=== 性能基准 ===');
    console.log(`平均测试耗时: ${benchmark.averageDuration.toFixed(2)}ms`);
    console.log(`最快的测试套件: ${benchmark.fastestSuite}`);
    console.log(`最慢的测试套件: ${benchmark.slowestSuite}`);
    console.log('\n最慢的10个测试:');
    benchmark.slowestTests.forEach(test => {
      console.log(`  ${test.name}: ${test.duration}ms`);
    });
  });
}