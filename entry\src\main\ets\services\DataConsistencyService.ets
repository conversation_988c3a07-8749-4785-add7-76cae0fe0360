import { EnhancedDatabaseService, DatabaseResult } from '../services/EnhancedDatabaseService';
import { RepositoryFactory } from '../repositories/index';
import { StateManager } from '../state/StateManager';
import { NoteModel, CapsuleModel, SettingsModel } from '../model/NoteModel';
import { DateUtils } from '../utils/DateUtils';

/**
 * 一致性检查结果
 */
export interface ConsistencyCheckResult {
  checkId: string;
  checkName: string;
  passed: boolean;
  severity: 'info' | 'warning' | 'error' | 'critical';
  details: any;
  timestamp: string;
  suggestions?: string[];
  autoFixable: boolean;
}

/**
 * 一致性检查报告
 */
export interface ConsistencyReport {
  reportId: string;
  timestamp: string;
  totalChecks: number;
  passedChecks: number;
  failedChecks: number;
  warnings: number;
  errors: number;
  critical: number;
  results: ConsistencyCheckResult[];
  summary: {
    overallHealth: 'excellent' | 'good' | 'fair' | 'poor' | 'critical';
    recommendations: string[];
    estimatedRepairTime: string;
  };
}

/**
 * 自动修复选项
 */
export interface AutoFixOptions {
  enabled: boolean;
  dryRun: boolean;
  backupBeforeRepair: boolean;
  repairStrategies: {
    orphanedRecords: boolean;
    inconsistentDates: boolean;
    missingReferences: boolean;
    corruptedData: boolean;
    duplicateRecords: boolean;
  };
}

/**
 * 数据一致性检查服务
 */
export class DataConsistencyService {
  private static instance: DataConsistencyService;
  private dbService: EnhancedDatabaseService;
  private stateManager: StateManager;
  private isRunningCheck = false;
  private lastCheckTime: string | null = null;
  
  private constructor() {
    this.dbService = EnhancedDatabaseService.getInstance();
    this.stateManager = StateManager.getInstance();
  }
  
  static getInstance(): DataConsistencyService {
    if (!DataConsistencyService.instance) {
      DataConsistencyService.instance = new DataConsistencyService();
    }
    return DataConsistencyService.instance;
  }
  
  /**
   * 执行完整的一致性检查
   */
  async performFullCheck(options?: AutoFixOptions): Promise<ConsistencyReport> {
    if (this.isRunningCheck) {
      throw new Error('Consistency check is already running');
    }
    
    this.isRunningCheck = true;
    
    try {
      const reportId = `consistency_${Date.now()}`;
      const results: ConsistencyCheckResult[] = [];
      
      console.log('Starting full consistency check...');
      
      // 基础检查
      results.push(await this.checkDatabaseStructure());
      results.push(await this.checkDataIntegrity());
      results.push(await this.checkForeignKeyConstraints());
      results.push(await this.checkOrphanedRecords());
      
      // 数据一致性检查
      results.push(await this.checkNoteDatesConsistency());
      results.push(await this.checkCapsuleDatesConsistency());
      results.push(await this.checkDuplicateRecords());
      results.push(await this.checkMissingData());
      
      // 性能相关检查
      results.push(await this.checkIndexUsage());
      results.push(await this.checkTableSizes());
      
      // 业务逻辑检查
      results.push(await this.checkBusinessRules());
      
      // 生成报告
      const report = this.generateReport(reportId, results);
      
      // 自动修复
      if (options?.enabled && report.failedChecks > 0) {
        await this.performAutoRepair(report, options);
      }
      
      // 更新状态
      this.lastCheckTime = report.timestamp;
      this.stateManager.updateState({
        lastSyncTime: report.timestamp
      }, 'CACHE_UPDATED');
      
      console.log(`Consistency check completed: ${report.passedChecks}/${report.totalChecks} checks passed`);
      
      return report;
      
    } finally {
      this.isRunningCheck = false;
    }
  }
  
  /**
   * 执行快速检查
   */
  async performQuickCheck(): Promise<ConsistencyReport> {
    const results: ConsistencyCheckResult[] = [];
    
    // 只执行关键检查
    results.push(await this.checkDatabaseStructure());
    results.push(await this.checkDataIntegrity());
    results.push(await this.checkNoteDatesConsistency());
    
    const report = this.generateReport(`quick_check_${Date.now()}`, results);
    
    console.log(`Quick consistency check completed: ${report.passedChecks}/${report.totalChecks} checks passed`);
    
    return report;
  }
  
  /**
   * 检查数据库结构
   */
  private async checkDatabaseStructure(): Promise<ConsistencyCheckResult> {
    try {
      const result = await this.dbService['withRetry'](async () => {
        const store = this.dbService['store'];
        if (!store) throw new Error('Database not initialized');
        
        // 检查表是否存在
        const tables = ['notes', 'capsules', 'settings', 'operation_logs'];
        const missingTables: string[] = [];
        
        for (const table of tables) {
          try {
            await store.querySql(`SELECT 1 FROM ${table} LIMIT 1`);
          } catch (error) {
            missingTables.push(table);
          }
        }
        
        return { missingTables };
      });
      
      if (!result.success) {
        return {
          checkId: 'db_structure',
          checkName: '数据库结构检查',
          passed: false,
          severity: 'critical',
          details: { error: result.error?.message },
          timestamp: new Date().toISOString(),
          suggestions: ['重新初始化数据库'],
          autoFixable: true
        };
      }
      
      const passed = result.data.missingTables.length === 0;
      
      return {
        checkId: 'db_structure',
        checkName: '数据库结构检查',
        passed,
        severity: passed ? 'info' : 'critical',
        details: result.data,
        timestamp: new Date().toISOString(),
        suggestions: passed ? [] : ['重新创建缺失的表'],
        autoFixable: true
      };
      
    } catch (error) {
      return {
        checkId: 'db_structure',
        checkName: '数据库结构检查',
        passed: false,
        severity: 'critical',
        details: { error: (error as Error).message },
        timestamp: new Date().toISOString(),
        suggestions: ['检查数据库连接'],
        autoFixable: false
      };
    }
  }
  
  /**
   * 检查数据完整性
   */
  private async checkDataIntegrity(): Promise<ConsistencyCheckResult> {
    try {
      const result = await this.dbService['withRetry'](async () => {
        const store = this.dbService['store'];
        if (!store) throw new Error('Database not initialized');
        
        // 检查笔记表的数据完整性
        const notesResult = await store.querySql(`
          SELECT COUNT(*) as total, 
                 SUM(CASE WHEN title IS NULL OR title = '' THEN 1 ELSE 0 END) as empty_title,
                 SUM(CASE WHEN createTime IS NULL THEN 1 ELSE 0 END) as invalid_create_time
          FROM notes WHERE isDeleted = 0
        `);
        
        const capsulesResult = await store.querySql(`
          SELECT COUNT(*) as total,
                 SUM(CASE WHEN title IS NULL OR title = '' THEN 1 ELSE 0 END) as empty_title,
                 SUM(CASE WHEN content IS NULL OR content = '' THEN 1 ELSE 0 END) as empty_content,
                 SUM(CASE WHEN openDate IS NULL THEN 1 ELSE 0 END) as invalid_open_date
          FROM capsules
        `);
        
        return {
          notes: this.parseResultSet(notesResult),
          capsules: this.parseResultSet(capsulesResult)
        };
      });
      
      if (!result.success) {
        return {
          checkId: 'data_integrity',
          checkName: '数据完整性检查',
          passed: false,
          severity: 'error',
          details: { error: result.error?.message },
          timestamp: new Date().toISOString(),
          autoFixable: false
        };
      }
      
      const issues: string[] = [];
      if (result.data.notes.empty_title > 0) {
        issues.push(`${result.data.notes.empty_title} 条笔记标题为空`);
      }
      if (result.data.notes.invalid_create_time > 0) {
        issues.push(`${result.data.notes.invalid_create_time} 条笔记创建时间无效`);
      }
      if (result.data.capsules.empty_title > 0) {
        issues.push(`${result.data.capsules.empty_title} 个时间胶囊标题为空`);
      }
      if (result.data.capsules.empty_content > 0) {
        issues.push(`${result.data.capsules.empty_content} 个时间胶囊内容为空`);
      }
      
      const passed = issues.length === 0;
      
      return {
        checkId: 'data_integrity',
        checkName: '数据完整性检查',
        passed,
        severity: passed ? 'info' : 'warning',
        details: { ...result.data, issues },
        timestamp: new Date().toISOString(),
        suggestions: passed ? [] : ['手动修复无效数据', '设置数据验证规则'],
        autoFixable: true
      };
      
    } catch (error) {
      return {
        checkId: 'data_integrity',
        checkName: '数据完整性检查',
        passed: false,
        severity: 'error',
        details: { error: (error as Error).message },
        timestamp: new Date().toISOString(),
        autoFixable: false
      };
    }
  }
  
  /**
   * 检查外键约束
   */
  private async checkForeignKeyConstraints(): Promise<ConsistencyCheckResult> {
    try {
      const result = await this.dbService['withRetry'](async () => {
        const store = this.dbService['store'];
        if (!store) throw new Error('Database not initialized');
        
        // 检查笔记分类ID的有效性
        const invalidCategories = await store.querySql(`
          SELECT COUNT(*) as count FROM notes 
          WHERE isDeleted = 0 AND categoryId NOT IN (1, 2, 3, 4, 5)
        `);
        
        return { invalidCategories: this.parseResultSet(invalidCategories).count };
      });
      
      if (!result.success) {
        return {
          checkId: 'foreign_key_constraints',
          checkName: '外键约束检查',
          passed: false,
          severity: 'error',
          details: { error: result.error?.message },
          timestamp: new Date().toISOString(),
          autoFixable: false
        };
      }
      
      const passed = result.data.invalidCategories === 0;
      
      return {
        checkId: 'foreign_key_constraints',
        checkName: '外键约束检查',
        passed,
        severity: passed ? 'info' : 'warning',
        details: result.data,
        timestamp: new Date().toISOString(),
        suggestions: passed ? [] : ['修复无效的分类ID', '添加分类验证'],
        autoFixable: true
      };
      
    } catch (error) {
      return {
        checkId: 'foreign_key_constraints',
        checkName: '外键约束检查',
        passed: false,
        severity: 'error',
        details: { error: (error as Error).message },
        timestamp: new Date().toISOString(),
        autoFixable: false
      };
    }
  }
  
  /**
   * 检查孤立记录
   */
  private async checkOrphanedRecords(): Promise<ConsistencyCheckResult> {
    try {
      const result = await this.dbService['withRetry'](async () => {
        const store = this.dbService['store'];
        if (!store) throw new Error('Database not initialized');
        
        // 检查是否有孤立的操作日志
        const orphanedLogs = await store.querySql(`
          SELECT COUNT(*) as count FROM operation_logs 
          WHERE timestamp > datetime('now', '-30 days')
          AND record_id IS NOT NULL
          AND record_id NOT IN (
            SELECT id FROM notes 
            UNION SELECT id FROM capsules 
            UNION SELECT id FROM settings
          )
        `);
        
        return { orphanedLogs: this.parseResultSet(orphanedLogs).count };
      });
      
      if (!result.success) {
        return {
          checkId: 'orphaned_records',
          checkName: '孤立记录检查',
          passed: false,
          severity: 'error',
          details: { error: result.error?.message },
          timestamp: new Date().toISOString(),
          autoFixable: false
        };
      }
      
      const passed = result.data.orphanedLogs === 0;
      
      return {
        checkId: 'orphaned_records',
        checkName: '孤立记录检查',
        passed,
        severity: passed ? 'info' : 'warning',
        details: result.data,
        timestamp: new Date().toISOString(),
        suggestions: passed ? [] : ['清理孤立的操作日志'],
        autoFixable: true
      };
      
    } catch (error) {
      return {
        checkId: 'orphaned_records',
        checkName: '孤立记录检查',
        passed: false,
        severity: 'error',
        details: { error: (error as Error).message },
        timestamp: new Date().toISOString(),
        autoFixable: false
      };
    }
  }
  
  /**
   * 检查笔记日期一致性
   */
  private async checkNoteDatesConsistency(): Promise<ConsistencyCheckResult> {
    try {
      const result = await this.dbService['withRetry'](async () => {
        const store = this.dbService['store'];
        if (!store) throw new Error('Database not initialized');
        
        // 检查创建时间是否大于更新时间
        const invalidDateOrder = await store.querySql(`
          SELECT COUNT(*) as count FROM notes 
          WHERE isDeleted = 0 AND createTime > updateTime
        `);
        
        // 检查未来的日期
        const futureDates = await store.querySql(`
          SELECT COUNT(*) as count FROM notes 
          WHERE isDeleted = 0 AND createTime > datetime('now', '+1 day')
        `);
        
        // 检查极早的日期（1970年之前）
        const ancientDates = await store.querySql(`
          SELECT COUNT(*) as count FROM notes 
          WHERE isDeleted = 0 AND createTime < '1970-01-01'
        `);
        
        return {
          invalidDateOrder: this.parseResultSet(invalidDateOrder).count,
          futureDates: this.parseResultSet(futureDates).count,
          ancientDates: this.parseResultSet(ancientDates).count
        };
      });
      
      if (!result.success) {
        return {
          checkId: 'note_dates_consistency',
          checkName: '笔记日期一致性检查',
          passed: false,
          severity: 'error',
          details: { error: result.error?.message },
          timestamp: new Date().toISOString(),
          autoFixable: false
        };
      }
      
      const issues: string[] = [];
      if (result.data.invalidDateOrder > 0) {
        issues.push(`${result.data.invalidDateOrder} 条笔记的创建时间大于更新时间`);
      }
      if (result.data.futureDates > 0) {
        issues.push(`${result.data.futureDates} 条笔记的创建时间是未来时间`);
      }
      if (result.data.ancientDates > 0) {
        issues.push(`${result.data.ancientDates} 条笔记的创建时间过于久远`);
      }
      
      const passed = issues.length === 0;
      
      return {
        checkId: 'note_dates_consistency',
        checkName: '笔记日期一致性检查',
        passed,
        severity: passed ? 'info' : 'warning',
        details: { ...result.data, issues },
        timestamp: new Date().toISOString(),
        suggestions: passed ? [] : ['修复日期顺序', '验证时间戳格式'],
        autoFixable: true
      };
      
    } catch (error) {
      return {
        checkId: 'note_dates_consistency',
        checkName: '笔记日期一致性检查',
        passed: false,
        severity: 'error',
        details: { error: (error as Error).message },
        timestamp: new Date().toISOString(),
        autoFixable: false
      };
    }
  }
  
  /**
   * 检查时间胶囊日期一致性
   */
  private async checkCapsuleDatesConsistency(): Promise<ConsistencyCheckResult> {
    try {
      const result = await this.dbService['withRetry'](async () => {
        const store = this.dbService['store'];
        if (!store) throw new Error('Database not initialized');
        
        // 检查开启时间是否小于创建时间
        const invalidDateOrder = await store.querySql(`
          SELECT COUNT(*) as count FROM capsules 
          WHERE openDate < createTime
        `);
        
        // 检查已开启但开启时间为未来的胶囊
        const openedFuture = await store.querySql(`
          SELECT COUNT(*) as count FROM capsules 
          WHERE isOpened = 1 AND openDate > datetime('now')
        `);
        
        return {
          invalidDateOrder: this.parseResultSet(invalidDateOrder).count,
          openedFuture: this.parseResultSet(openedFuture).count
        };
      });
      
      if (!result.success) {
        return {
          checkId: 'capsule_dates_consistency',
          checkName: '时间胶囊日期一致性检查',
          passed: false,
          severity: 'error',
          details: { error: result.error?.message },
          timestamp: new Date().toISOString(),
          autoFixable: false
        };
      }
      
      const issues: string[] = [];
      if (result.data.invalidDateOrder > 0) {
        issues.push(`${result.data.invalidDateOrder} 个时间胶囊的开启时间小于创建时间`);
      }
      if (result.data.openedFuture > 0) {
        issues.push(`${result.data.openedFuture} 个已开启的时间胶囊的开启时间是未来时间`);
      }
      
      const passed = issues.length === 0;
      
      return {
        checkId: 'capsule_dates_consistency',
        checkName: '时间胶囊日期一致性检查',
        passed,
        severity: passed ? 'info' : 'warning',
        details: { ...result.data, issues },
        timestamp: new Date().toISOString(),
        suggestions: passed ? [] : ['修复日期逻辑', '验证时间胶囊状态'],
        autoFixable: true
      };
      
    } catch (error) {
      return {
        checkId: 'capsule_dates_consistency',
        checkName: '时间胶囊日期一致性检查',
        passed: false,
        severity: 'error',
        details: { error: (error as Error).message },
        timestamp: new Date().toISOString(),
        autoFixable: false
      };
    }
  }
  
  /**
   * 检查重复记录
   */
  private async checkDuplicateRecords(): Promise<ConsistencyCheckResult> {
    try {
      const result = await this.dbService['withRetry'](async () => {
        const store = this.dbService['store'];
        if (!store) throw new Error('Database not initialized');
        
        // 检查重复的笔记标题（相同时间）
        const duplicateNotes = await store.querySql(`
          SELECT COUNT(*) as count FROM (
            SELECT title, createTime, COUNT(*) as dup_count 
            FROM notes 
            WHERE isDeleted = 0 
            GROUP BY title, createTime 
            HAVING COUNT(*) > 1
          )
        `);
        
        // 检查重复的时间胶囊标题（相同时间）
        const duplicateCapsules = await store.querySql(`
          SELECT COUNT(*) as count FROM (
            SELECT title, createTime, COUNT(*) as dup_count 
            FROM capsules 
            GROUP BY title, createTime 
            HAVING COUNT(*) > 1
          )
        `);
        
        return {
          duplicateNotes: this.parseResultSet(duplicateNotes).count,
          duplicateCapsules: this.parseResultSet(duplicateCapsules).count
        };
      });
      
      if (!result.success) {
        return {
          checkId: 'duplicate_records',
          checkName: '重复记录检查',
          passed: false,
          severity: 'error',
          details: { error: result.error?.message },
          timestamp: new Date().toISOString(),
          autoFixable: false
        };
      }
      
      const totalDuplicates = result.data.duplicateNotes + result.data.duplicateCapsules;
      const passed = totalDuplicates === 0;
      
      return {
        checkId: 'duplicate_records',
        checkName: '重复记录检查',
        passed,
        severity: passed ? 'info' : 'warning',
        details: result.data,
        timestamp: new Date().toISOString(),
        suggestions: passed ? [] : ['合并重复记录', '添加唯一性约束'],
        autoFixable: true
      };
      
    } catch (error) {
      return {
        checkId: 'duplicate_records',
        checkName: '重复记录检查',
        passed: false,
        severity: 'error',
        details: { error: (error as Error).message },
        timestamp: new Date().toISOString(),
        autoFixable: false
      };
    }
  }
  
  /**
   * 检查缺失数据
   */
  private async checkMissingData(): Promise<ConsistencyCheckResult> {
    try {
      const result = await this.dbService['withRetry'](async () => {
        const store = this.dbService['store'];
        if (!store) throw new Error('Database not initialized');
        
        // 检查是否缺少设置记录
        const missingSettings = await store.querySql(`
          SELECT COUNT(*) as count FROM settings
        `);
        
        return {
          missingSettings: this.parseResultSet(missingSettings).count === 0 ? 1 : 0
        };
      });
      
      if (!result.success) {
        return {
          checkId: 'missing_data',
          checkName: '缺失数据检查',
          passed: false,
          severity: 'error',
          details: { error: result.error?.message },
          timestamp: new Date().toISOString(),
          autoFixable: false
        };
      }
      
      const passed = result.data.missingSettings === 0;
      
      return {
        checkId: 'missing_data',
        checkName: '缺失数据检查',
        passed,
        severity: passed ? 'info' : 'warning',
        details: result.data,
        timestamp: new Date().toISOString(),
        suggestions: passed ? [] : ['初始化默认设置'],
        autoFixable: true
      };
      
    } catch (error) {
      return {
        checkId: 'missing_data',
        checkName: '缺失数据检查',
        passed: false,
        severity: 'error',
        details: { error: (error as Error).message },
        timestamp: new Date().toISOString(),
        autoFixable: false
      };
    }
  }
  
  /**
   * 检查索引使用情况
   */
  private async checkIndexUsage(): Promise<ConsistencyCheckResult> {
    try {
      const result = await this.dbService['withRetry'](async () => {
        const store = this.dbService['store'];
        if (!store) throw new Error('Database not initialized');
        
        // 检查是否有必要的索引
        const indexes = await store.querySql(`
          SELECT name FROM sqlite_master 
          WHERE type='index' AND tbl_name IN ('notes', 'capsules', 'settings')
        `);
        
        const indexNames: string[] = [];
        if (indexes.goToFirstRow()) {
          do {
            indexNames.push(indexes.getString(indexes.getColumnIndex('name')));
          } while (indexes.goToNextRow());
        }
        indexes.close();
        
        const requiredIndexes = [
          'idx_notes_create_time',
          'idx_notes_category_id',
          'idx_capsules_open_date'
        ];
        
        const missingIndexes = requiredIndexes.filter(index => 
          !indexNames.some(name => name.includes(index.replace('_', '_')))
        );
        
        return { missingIndexes };
      });
      
      if (!result.success) {
        return {
          checkId: 'index_usage',
          checkName: '索引使用检查',
          passed: false,
          severity: 'error',
          details: { error: result.error?.message },
          timestamp: new Date().toISOString(),
          autoFixable: false
        };
      }
      
      const passed = result.data.missingIndexes.length === 0;
      
      return {
        checkId: 'index_usage',
        checkName: '索引使用检查',
        passed,
        severity: passed ? 'info' : 'warning',
        details: result.data,
        timestamp: new Date().toISOString(),
        suggestions: passed ? [] : ['创建缺失的索引'],
        autoFixable: true
      };
      
    } catch (error) {
      return {
        checkId: 'index_usage',
        checkName: '索引使用检查',
        passed: false,
        severity: 'error',
        details: { error: (error as Error).message },
        timestamp: new Date().toISOString(),
        autoFixable: false
      };
    }
  }
  
  /**
   * 检查表大小
   */
  private async checkTableSizes(): Promise<ConsistencyCheckResult> {
    try {
      const result = await this.dbService['withRetry'](async () => {
        const store = this.dbService['store'];
        if (!store) throw new Error('Database not initialized');
        
        // 获取表大小信息
        const tableStats = await store.querySql(`
          SELECT 
            'notes' as table_name,
            COUNT(*) as record_count,
            SUM(LENGTH(title) + LENGTH(content) + LENGTH(tags)) as total_size
          FROM notes WHERE isDeleted = 0
          UNION ALL
          SELECT 
            'capsules' as table_name,
            COUNT(*) as record_count,
            SUM(LENGTH(title) + LENGTH(content)) as total_size
          FROM capsules
          UNION ALL
          SELECT 
            'settings' as table_name,
            COUNT(*) as record_count,
            0 as total_size
          FROM settings
        `);
        
        const stats: any[] = [];
        if (tableStats.goToFirstRow()) {
          do {
            stats.push({
              tableName: tableStats.getString(tableStats.getColumnIndex('table_name')),
              recordCount: tableStats.getLong(tableStats.getColumnIndex('record_count')),
              totalSize: tableStats.getLong(tableStats.getColumnIndex('total_size'))
            });
          } while (tableStats.goToNextRow());
        }
        tableStats.close();
        
        return { tableStats: stats };
      });
      
      if (!result.success) {
        return {
          checkId: 'table_sizes',
          checkName: '表大小检查',
          passed: false,
          severity: 'error',
          details: { error: result.error?.message },
          timestamp: new Date().toISOString(),
          autoFixable: false
        };
      }
      
      // 检查是否有表过大
      const largeTables = result.data.tableStats.filter((stat: any) => stat.totalSize > 10 * 1024 * 1024); // 10MB
      const passed = largeTables.length === 0;
      
      return {
        checkId: 'table_sizes',
        checkName: '表大小检查',
        passed,
        severity: passed ? 'info' : 'warning',
        details: { ...result.data, largeTables },
        timestamp: new Date().toISOString(),
        suggestions: passed ? [] : ['考虑数据归档', '优化存储结构'],
        autoFixable: false
      };
      
    } catch (error) {
      return {
        checkId: 'table_sizes',
        checkName: '表大小检查',
        passed: false,
        severity: 'error',
        details: { error: (error as Error).message },
        timestamp: new Date().toISOString(),
        autoFixable: false
      };
    }
  }
  
  /**
   * 检查业务规则
   */
  private async checkBusinessRules(): Promise<ConsistencyCheckResult> {
    try {
      const result = await this.dbService['withRetry'](async () => {
        const store = this.dbService['store'];
        if (!store) throw new Error('Database not initialized');
        
        // 检查业务规则
        const violations: any[] = [];
        
        // 检查笔记长度限制
        const longNotes = await store.querySql(`
          SELECT COUNT(*) as count FROM notes 
          WHERE isDeleted = 0 AND LENGTH(content) > 100000
        `);
        const longNoteCount = this.parseResultSet(longNotes).count;
        if (longNoteCount > 0) {
          violations.push({ type: 'long_notes', count: longNoteCount });
        }
        
        // 检查时间胶囊开启时间合理性
        const unreasonableCapsules = await store.querySql(`
          SELECT COUNT(*) as count FROM capsules 
          WHERE openDate > datetime('now', '+10 years')
        `);
        const unreasonableCount = this.parseResultSet(unreasonableCapsules).count;
        if (unreasonableCount > 0) {
          violations.push({ type: 'unreasonable_capsules', count: unreasonableCount });
        }
        
        return { violations };
      });
      
      if (!result.success) {
        return {
          checkId: 'business_rules',
          checkName: '业务规则检查',
          passed: false,
          severity: 'error',
          details: { error: result.error?.message },
          timestamp: new Date().toISOString(),
          autoFixable: false
        };
      }
      
      const passed = result.data.violations.length === 0;
      
      return {
        checkId: 'business_rules',
        checkName: '业务规则检查',
        passed,
        severity: passed ? 'info' : 'warning',
        details: result.data,
        timestamp: new Date().toISOString(),
        suggestions: passed ? [] : ['检查业务逻辑', '设置数据验证'],
        autoFixable: false
      };
      
    } catch (error) {
      return {
        checkId: 'business_rules',
        checkName: '业务规则检查',
        passed: false,
        severity: 'error',
        details: { error: (error as Error).message },
        timestamp: new Date().toISOString(),
        autoFixable: false
      };
    }
  }
  
  /**
   * 生成一致性检查报告
   */
  private generateReport(reportId: string, results: ConsistencyCheckResult[]): ConsistencyReport {
    const totalChecks = results.length;
    const passedChecks = results.filter(r => r.passed).length;
    const failedChecks = totalChecks - passedChecks;
    
    const warnings = results.filter(r => !r.passed && r.severity === 'warning').length;
    const errors = results.filter(r => !r.passed && r.severity === 'error').length;
    const critical = results.filter(r => !r.passed && r.severity === 'critical').length;
    
    // 确定整体健康状态
    let overallHealth: 'excellent' | 'good' | 'fair' | 'poor' | 'critical' = 'excellent';
    if (critical > 0) {
      overallHealth = 'critical';
    } else if (errors > 0) {
      overallHealth = 'poor';
    } else if (warnings > failedChecks * 0.5) {
      overallHealth = 'fair';
    } else if (failedChecks > 0) {
      overallHealth = 'good';
    }
    
    // 生成建议
    const recommendations: string[] = [];
    if (critical > 0) {
      recommendations.push('立即修复关键问题');
    }
    if (errors > 0) {
      recommendations.push('修复错误级别问题');
    }
    if (warnings > 0) {
      recommendations.push('关注警告级别问题');
    }
    if (overallHealth === 'excellent') {
      recommendations.push('继续保持良好的数据质量');
    }
    
    // 估算修复时间
    let estimatedRepairTime = '不到1分钟';
    if (critical > 0) {
      estimatedRepairTime = '5-10分钟';
    } else if (errors > 3) {
      estimatedRepairTime = '3-5分钟';
    } else if (failedChecks > 5) {
      estimatedRepairTime = '2-3分钟';
    }
    
    return {
      reportId,
      timestamp: new Date().toISOString(),
      totalChecks,
      passedChecks,
      failedChecks,
      warnings,
      errors,
      critical,
      results,
      summary: {
        overallHealth,
        recommendations,
        estimatedRepairTime
      }
    };
  }
  
  /**
   * 执行自动修复
   */
  private async performAutoRepair(report: ConsistencyReport, options: AutoFixOptions): Promise<void> {
    if (options.dryRun) {
      console.log('Dry run: would perform auto-repair for', report.failedChecks, 'issues');
      return;
    }
    
    console.log('Starting auto-repair...');
    
    // 备份数据
    if (options.backupBeforeRepair) {
      console.log('Creating backup before repair...');
      // 这里应该调用备份服务
    }
    
    // 执行修复
    for (const result of report.results) {
      if (!result.passed && result.autoFixable) {
        await this.repairIssue(result, options);
      }
    }
    
    console.log('Auto-repair completed');
  }
  
  /**
   * 修复特定问题
   */
  private async repairIssue(issue: ConsistencyCheckResult, options: AutoFixOptions): Promise<void> {
    console.log(`Repairing issue: ${issue.checkName}`);
    
    switch (issue.checkId) {
      case 'db_structure':
        await this.repairDatabaseStructure();
        break;
      case 'data_integrity':
        await this.repairDataIntegrity();
        break;
      case 'foreign_key_constraints':
        await this.repairForeignKeyConstraints();
        break;
      case 'orphaned_records':
        await this.repairOrphanedRecords();
        break;
      case 'note_dates_consistency':
        await this.repairNoteDates();
        break;
      case 'capsule_dates_consistency':
        await this.repairCapsuleDates();
        break;
      case 'duplicate_records':
        await this.repairDuplicateRecords();
        break;
      case 'missing_data':
        await this.repairMissingData();
        break;
      case 'index_usage':
        await this.repairIndexes();
        break;
    }
  }
  
  /**
   * 修复数据库结构
   */
  private async repairDatabaseStructure(): Promise<void> {
    const result = await this.dbService['withRetry'](async () => {
      const store = this.dbService['store'];
      if (!store) throw new Error('Database not initialized');
      
      // 重新创建表结构
      await this.dbService['createTables']();
      await this.dbService['createIndexes']();
      
      return true;
    });
    
    console.log('Database structure repair:', result.success ? 'success' : 'failed');
  }
  
  /**
   * 修复数据完整性
   */
  private async repairDataIntegrity(): Promise<void> {
    const result = await this.dbService['withRetry'](async () => {
      const store = this.dbService['store'];
      if (!store) throw new Error('Database not initialized');
      
      // 修复空标题的笔记
      await store.executeSql(`
        UPDATE notes SET title = '无标题笔记' 
        WHERE isDeleted = 0 AND (title IS NULL OR title = '')
      `);
      
      // 修复无效的创建时间
      await store.executeSql(`
        UPDATE notes SET createTime = datetime('now'), updateTime = datetime('now')
        WHERE isDeleted = 0 AND createTime IS NULL
      `);
      
      return true;
    });
    
    console.log('Data integrity repair:', result.success ? 'success' : 'failed');
  }
  
  /**
   * 修复外键约束
   */
  private async repairForeignKeyConstraints(): Promise<void> {
    const result = await this.dbService['withRetry'](async () => {
      const store = this.dbService['store'];
      if (!store) throw new Error('Database not initialized');
      
      // 修复无效的分类ID
      await store.executeSql(`
        UPDATE notes SET categoryId = 1 
        WHERE isDeleted = 0 AND categoryId NOT IN (1, 2, 3, 4, 5)
      `);
      
      return true;
    });
    
    console.log('Foreign key constraints repair:', result.success ? 'success' : 'failed');
  }
  
  /**
   * 修复孤立记录
   */
  private async repairOrphanedRecords(): Promise<void> {
    const result = await this.dbService['withRetry'](async () => {
      const store = this.dbService['store'];
      if (!store) throw new Error('Database not initialized');
      
      // 清理孤立的操作日志
      await store.executeSql(`
        DELETE FROM operation_logs 
        WHERE timestamp > datetime('now', '-30 days')
        AND record_id IS NOT NULL
        AND record_id NOT IN (
          SELECT id FROM notes 
          UNION SELECT id FROM capsules 
          UNION SELECT id FROM settings
        )
      `);
      
      return true;
    });
    
    console.log('Orphaned records repair:', result.success ? 'success' : 'failed');
  }
  
  /**
   * 修复笔记日期
   */
  private async repairNoteDates(): Promise<void> {
    const result = await this.dbService['withRetry'](async () => {
      const store = this.dbService['store'];
      if (!store) throw new Error('Database not initialized');
      
      // 修复日期顺序
      await store.executeSql(`
        UPDATE notes SET updateTime = createTime 
        WHERE isDeleted = 0 AND createTime > updateTime
      `);
      
      // 修复未来日期
      await store.executeSql(`
        UPDATE notes SET createTime = datetime('now') 
        WHERE isDeleted = 0 AND createTime > datetime('now', '+1 day')
      `);
      
      return true;
    });
    
    console.log('Note dates repair:', result.success ? 'success' : 'failed');
  }
  
  /**
   * 修复时间胶囊日期
   */
  private async repairCapsuleDates(): Promise<void> {
    const result = await this.dbService['withRetry'](async () => {
      const store = this.dbService['store'];
      if (!store) throw new Error('Database not initialized');
      
      // 修复日期顺序
      await store.executeSql(`
        UPDATE capsules SET openDate = datetime(createTime, '+1 day') 
        WHERE openDate < createTime
      `);
      
      return true;
    });
    
    console.log('Capsule dates repair:', result.success ? 'success' : 'failed');
  }
  
  /**
   * 修复重复记录
   */
  private async repairDuplicateRecords(): Promise<void> {
    console.log('Duplicate records repair: manual intervention required');
    // 重复记录需要手动处理
  }
  
  /**
   * 修复缺失数据
   */
  private async repairMissingData(): Promise<void> {
    const result = await this.dbService['withRetry'](async () => {
      const store = this.dbService['store'];
      if (!store) throw new Error('Database not initialized');
      
      // 初始化默认设置
      await this.dbService['initDefaultSettings']();
      
      return true;
    });
    
    console.log('Missing data repair:', result.success ? 'success' : 'failed');
  }
  
  /**
   * 修复索引
   */
  private async repairIndexes(): Promise<void> {
    const result = await this.dbService['withRetry'](async () => {
      const store = this.dbService['store'];
      if (!store) throw new Error('Database not initialized');
      
      // 重新创建索引
      await this.dbService['createIndexes']();
      
      return true;
    });
    
    console.log('Indexes repair:', result.success ? 'success' : 'failed');
  }
  
  /**
   * 解析ResultSet
   */
  private parseResultSet(resultSet: any): any {
    if (resultSet.goToFirstRow()) {
      const result: any = {};
      const columnCount = resultSet.getColumnCount();
      
      for (let i = 0; i < columnCount; i++) {
        const columnName = resultSet.getColumnName(i);
        result[columnName] = resultSet.getString(resultSet.getColumnIndex(columnName));
      }
      
      resultSet.close();
      return result;
    }
    
    resultSet.close();
    return {};
  }
  
  /**
   * 获取最后检查时间
   */
  getLastCheckTime(): string | null {
    return this.lastCheckTime;
  }
  
  /**
   * 检查是否正在运行检查
   */
  isCheckRunning(): boolean {
    return this.isRunningCheck;
  }
}