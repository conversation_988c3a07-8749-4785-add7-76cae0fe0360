import relationalStore from '@ohos.data.relationalStore';
import { NoteModel, CapsuleModel, SettingsModel } from '../model/NoteModel';
import { AppConstants } from '../constants/AppConstants';
import { DateUtils } from '../utils/DateUtils';

// 数据库服务类
export class DatabaseService {
  private static instance: DatabaseService;
  private store: relationalStore.RdbStore | null = null;
  
  private constructor() {}
  
  static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService();
    }
    return DatabaseService.instance;
  }
  
  /**
   * 初始化数据库
   */
  async init(context: Context): Promise<void> {
    try {
      // 配置数据库信息
      const config: relationalStore.StoreConfig = {
        name: AppConstants.DATABASE_NAME,
        securityLevel: relationalStore.SecurityLevel.S1
      };
      
      // 获取RdbStore
      this.store = await relationalStore.getRdbStore(context, config);
      
      // 创建表
      await this.createTables();
      
      console.info('Database initialized successfully');
    } catch (error) {
      console.error('Failed to initialize database:', error);
      throw error;
    }
  }
  
  /**
   * 创建数据表
   */
  private async createTables(): Promise<void> {
    if (!this.store) return;
    
    // 创建笔记表
    await this.store.executeSql(`
      CREATE TABLE IF NOT EXISTS ${AppConstants.TABLE_NOTES} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        content TEXT,
        categoryId INTEGER DEFAULT 1,
        tags TEXT,
        mood TEXT DEFAULT '',
        weather TEXT DEFAULT '',
        location TEXT DEFAULT '',
        images TEXT DEFAULT '',
        voiceNote TEXT DEFAULT '',
        createTime TEXT NOT NULL,
        updateTime TEXT NOT NULL,
        isDeleted INTEGER DEFAULT 0
      )
    `);
    
    // 创建时间胶囊表
    await this.store.executeSql(`
      CREATE TABLE IF NOT EXISTS ${AppConstants.TABLE_CAPSULES} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        content TEXT NOT NULL,
        openDate TEXT NOT NULL,
        createTime TEXT NOT NULL,
        isOpened INTEGER DEFAULT 0,
        isOpenTime INTEGER DEFAULT 0
      )
    `);
    
    // 创建设置表
    await this.store.executeSql(`
      CREATE TABLE IF NOT EXISTS settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        darkMode INTEGER DEFAULT 2,
        reminderTime TEXT DEFAULT '21:00',
        reminderEnabled INTEGER DEFAULT 1,
        syncEnabled INTEGER DEFAULT 0,
        backupEnabled INTEGER DEFAULT 0,
        fontSize INTEGER DEFAULT 16
      )
    `);
    
    // 初始化默认设置
    await this.initDefaultSettings();
  }
  
  /**
   * 初始化默认设置
   */
  private async initDefaultSettings(): Promise<void> {
    if (!this.store) return;
    
    const resultSet = await this.store.querySql('SELECT COUNT(*) as count FROM settings');
    if (resultSet.goToFirstRow()) {
      const count = resultSet.getLong(resultSet.getColumnIndex('count'));
      if (count === 0) {
        await this.store.executeSql(`
          INSERT INTO settings (darkMode, reminderTime, reminderEnabled, syncEnabled, backupEnabled, fontSize)
          VALUES (2, '21:00', 1, 0, 0, 16)
        `);
      }
    }
    resultSet.close();
  }
  
  // ===== 笔记相关操作 =====
  
  /**
   * 插入或更新笔记
   */
  async saveNote(note: NoteModel): Promise<number> {
    if (!this.store) throw new Error('Database not initialized');
    
    if (note.id) {
      // 更新
      const updateTime = DateUtils.now();
      const values = {
        title: note.title,
        content: note.content,
        categoryId: note.categoryId,
        tags: note.tags,
        mood: note.mood || '',
        weather: note.weather || '',
        location: note.location || '',
        images: note.images || '',
        voiceNote: note.voiceNote || '',
        updateTime: updateTime
      };
      
      const pred = new relationalStore.RdbPredicates(AppConstants.TABLE_NOTES);
      pred.equalTo('id', note.id);
      const rows = await this.store.update(values, pred);
      return note.id;
    } else {
      // 插入
      const now = DateUtils.now();
      const values = {
        title: note.title,
        content: note.content,
        categoryId: note.categoryId || 1,
        tags: note.tags || '',
        mood: note.mood || '',
        weather: note.weather || '',
        location: note.location || '',
        images: note.images || '',
        voiceNote: note.voiceNote || '',
        createTime: now,
        updateTime: now,
        isDeleted: 0
      };
      
      const rowId = await this.store.insert(AppConstants.TABLE_NOTES, values);
      return rowId;
    }
  }
  
  /**
   * 获取笔记列表
   */
  async getNotes(offset: number = 0, limit: number = AppConstants.PAGE_SIZE): Promise<NoteModel[]> {
    if (!this.store) throw new Error('Database not initialized');
    
    const pred = new relationalStore.RdbPredicates(AppConstants.TABLE_NOTES);
    pred.equalTo('isDeleted', 0);
    pred.orderByDesc('createTime');
    pred.limitAs(limit);
    pred.offsetAs(offset);
    
    const resultSet = await this.store.query(pred);
    const notes: NoteModel[] = [];
    
    if (resultSet.goToFirstRow()) {
      do {
        const note = this.resultSetToNote(resultSet);
        notes.push(note);
      } while (resultSet.goToNextRow());
    }
    
    resultSet.close();
    return notes;
  }
  
  /**
   * 删除笔记（软删除）
   */
  async deleteNote(id: number): Promise<void> {
    if (!this.store) throw new Error('Database not initialized');
    
    const values = { isDeleted: 1 };
    const pred = new relationalStore.RdbPredicates(AppConstants.TABLE_NOTES);
    pred.equalTo('id', id);
    
    await this.store.update(values, pred);
  }
  
  /**
   * 搜索笔记
   */
  async searchNotes(keyword: string): Promise<NoteModel[]> {
    if (!this.store) throw new Error('Database not initialized');
    
    if (!keyword.trim()) {
      return [];
    }
    
    // 使用 SQL 查询进行全文搜索
    const sql = `
      SELECT * FROM ${AppConstants.TABLE_NOTES} 
      WHERE isDeleted = 0 
      AND (title LIKE '%${keyword}%' OR content LIKE '%${keyword}%' OR tags LIKE '%${keyword}%')
      ORDER BY 
        CASE 
          WHEN title LIKE '%${keyword}%' THEN 1
          WHEN content LIKE '%${keyword}%' THEN 2
          ELSE 3
        END,
        createTime DESC
    `;
    
    const resultSet = await this.store.querySql(sql);
    const notes: NoteModel[] = [];
    
    if (resultSet.goToFirstRow()) {
      do {
        const note = this.resultSetToNote(resultSet);
        notes.push(note);
      } while (resultSet.goToNextRow());
    }
    
    resultSet.close();
    return notes;
  }
  
  /**
   * 将ResultSet转换为Note对象
   */
  private resultSetToNote(resultSet: relationalStore.ResultSet): NoteModel {
    return {
      id: resultSet.getLong(resultSet.getColumnIndex('id')),
      title: resultSet.getString(resultSet.getColumnIndex('title')),
      content: resultSet.getString(resultSet.getColumnIndex('content')),
      categoryId: resultSet.getLong(resultSet.getColumnIndex('categoryId')),
      tags: resultSet.getString(resultSet.getColumnIndex('tags')),
      mood: resultSet.getString(resultSet.getColumnIndex('mood')),
      weather: resultSet.getString(resultSet.getColumnIndex('weather')),
      location: resultSet.getString(resultSet.getColumnIndex('location')),
      images: resultSet.getString(resultSet.getColumnIndex('images')),
      voiceNote: resultSet.getString(resultSet.getColumnIndex('voiceNote')),
      createTime: resultSet.getString(resultSet.getColumnIndex('createTime')),
      updateTime: resultSet.getString(resultSet.getColumnIndex('updateTime')),
      isDeleted: resultSet.getLong(resultSet.getColumnIndex('isDeleted'))
    };
  }

  /**
   * 获取特定日期的笔记
   */
  async getNotesByDate(date: string): Promise<NoteModel[]> {
    if (!this.store) throw new Error('Database not initialized');
    
    const pred = new relationalStore.RdbPredicates(AppConstants.TABLE_NOTES);
    pred.equalTo('isDeleted', 0);
    pred.like('createTime', `${date}%`);
    pred.orderByDesc('createTime');
    
    const resultSet = await this.store.query(pred);
    const notes: NoteModel[] = [];
    
    if (resultSet.goToFirstRow()) {
      do {
        const note = this.resultSetToNote(resultSet);
        notes.push(note);
      } while (resultSet.goToNextRow());
    }
    
    resultSet.close();
    return notes;
  }

  /**
   * 获取有笔记的日期列表
   */
  async getNoteDates(): Promise<string[]> {
    if (!this.store) throw new Error('Database not initialized');
    
    const resultSet = await this.store.querySql(`
      SELECT DISTINCT SUBSTR(createTime, 1, 10) as date 
      FROM ${AppConstants.TABLE_NOTES} 
      WHERE isDeleted = 0 
      ORDER BY date DESC
    `);
    
    const dates: string[] = [];
    
    if (resultSet.goToFirstRow()) {
      do {
        const date = resultSet.getString(resultSet.getColumnIndex('date'));
        dates.push(date);
      } while (resultSet.goToNextRow());
    }
    
    resultSet.close();
    return dates;
  }

  /**
   * 获取某日期的笔记数量
   */
  async getNoteCountByDate(date: string): Promise<number> {
    if (!this.store) throw new Error('Database not initialized');
    
    const resultSet = await this.store.querySql(`
      SELECT COUNT(*) as count 
      FROM ${AppConstants.TABLE_NOTES} 
      WHERE isDeleted = 0 AND createTime LIKE '${date}%'
    `);
    
    let count = 0;
    if (resultSet.goToFirstRow()) {
      count = resultSet.getLong(resultSet.getColumnIndex('count'));
    }
    
    resultSet.close();
    return count;
  }

  /**
   * 获取月份的笔记统计
   */
  async getMonthlyNoteStats(year: number, month: number): Promise<{ date: string; count: number }[]> {
    if (!this.store) throw new Error('Database not initialized');
    
    const monthStr = this.padZero(month);
    const resultSet = await this.store.querySql(`
      SELECT SUBSTR(createTime, 1, 10) as date, COUNT(*) as count 
      FROM ${AppConstants.TABLE_NOTES} 
      WHERE isDeleted = 0 AND createTime LIKE '${year}-${monthStr}-%'
      GROUP BY SUBSTR(createTime, 1, 10)
      ORDER BY date
    `);
    
    const stats: { date: string; count: number }[] = [];
    
    if (resultSet.goToFirstRow()) {
      do {
        const date = resultSet.getString(resultSet.getColumnIndex('date'));
        const count = resultSet.getLong(resultSet.getColumnIndex('count'));
        stats.push({ date, count });
      } while (resultSet.goToNextRow());
    }
    
    resultSet.close();
    return stats;
  }

  /**
   * 数字补零
   */
  private padZero(num: number): string {
    return num < 10 ? `0${num}` : `${num}`;
  }
  
  // ===== 时间胶囊相关操作 =====
  
  /**
   * 创建时间胶囊
   */
  async createCapsule(capsule: CapsuleModel): Promise<number> {
    if (!this.store) throw new Error('Database not initialized');
    
    const values = {
      title: capsule.title,
      content: capsule.content,
      openDate: capsule.openDate,
      createTime: DateUtils.now(),
      isOpened: 0,
      isOpenTime: capsule.isOpenTime ? 1 : 0
    };
    
    return await this.store.insert(AppConstants.TABLE_CAPSULES, values);
  }
  
  /**
   * 获取时间胶囊列表
   */
  async getCapsules(): Promise<CapsuleModel[]> {
    if (!this.store) throw new Error('Database not initialized');
    
    const pred = new relationalStore.RdbPredicates(AppConstants.TABLE_CAPSULES);
    pred.orderByDesc('createTime');
    
    const resultSet = await this.store.query(pred);
    const capsules: CapsuleModel[] = [];
    
    if (resultSet.goToFirstRow()) {
      do {
        capsules.push({
          id: resultSet.getLong(resultSet.getColumnIndex('id')),
          title: resultSet.getString(resultSet.getColumnIndex('title')),
          content: resultSet.getString(resultSet.getColumnIndex('content')),
          openDate: resultSet.getString(resultSet.getColumnIndex('openDate')),
          createTime: resultSet.getString(resultSet.getColumnIndex('createTime')),
          isOpened: resultSet.getLong(resultSet.getColumnIndex('isOpened')),
          isOpenTime: resultSet.getLong(resultSet.getColumnIndex('isOpenTime')) === 1
        });
      } while (resultSet.goToNextRow());
    }
    
    resultSet.close();
    return capsules;
  }
  
  /**
   * 更新时间胶囊
   */
  async updateCapsule(id: number, updates: Partial<CapsuleModel>): Promise<void> {
    if (!this.store) throw new Error('Database not initialized');
    
    const values: any = {};
    if (updates.title !== undefined) values.title = updates.title;
    if (updates.content !== undefined) values.content = updates.content;
    if (updates.openDate !== undefined) values.openDate = updates.openDate;
    if (updates.isOpened !== undefined) values.isOpened = updates.isOpened;
    if (updates.isOpenTime !== undefined) values.isOpenTime = updates.isOpenTime ? 1 : 0;
    
    const pred = new relationalStore.RdbPredicates(AppConstants.TABLE_CAPSULES);
    pred.equalTo('id', id);
    
    await this.store.update(values, pred);
  }
  
  /**
   * 删除时间胶囊
   */
  async deleteCapsule(id: number): Promise<void> {
    if (!this.store) throw new Error('Database not initialized');
    
    const pred = new relationalStore.RdbPredicates(AppConstants.TABLE_CAPSULES);
    pred.equalTo('id', id);
    
    await this.store.delete(pred);
  }
  
  /**
   * 获取已过期可开启的时间胶囊
   */
  async getExpiredCapsules(): Promise<CapsuleModel[]> {
    if (!this.store) throw new Error('Database not initialized');
    
    const now = DateUtils.now();
    const resultSet = await this.store.querySql(`
      SELECT * FROM ${AppConstants.TABLE_CAPSULES} 
      WHERE isOpened = 0 AND openDate <= '${now}'
      ORDER BY openDate ASC
    `);
    
    const capsules: CapsuleModel[] = [];
    
    if (resultSet.goToFirstRow()) {
      do {
        capsules.push({
          id: resultSet.getLong(resultSet.getColumnIndex('id')),
          title: resultSet.getString(resultSet.getColumnIndex('title')),
          content: resultSet.getString(resultSet.getColumnIndex('content')),
          openDate: resultSet.getString(resultSet.getColumnIndex('openDate')),
          createTime: resultSet.getString(resultSet.getColumnIndex('createTime')),
          isOpened: resultSet.getLong(resultSet.getColumnIndex('isOpened')),
          isOpenTime: resultSet.getLong(resultSet.getColumnIndex('isOpenTime')) === 1
        });
      } while (resultSet.goToNextRow());
    }
    
    resultSet.close();
    return capsules;
  }
  
  /**
   * 获取即将到期的时间胶囊
   */
  async getUpcomingCapsules(days: number = 7): Promise<CapsuleModel[]> {
    if (!this.store) throw new Error('Database not initialized');
    
    const now = new Date();
    const futureDate = new Date(now.getTime() + days * 24 * 60 * 60 * 1000);
    const nowStr = DateUtils.formatDate(now, 'YYYY-MM-DD HH:mm:ss');
    const futureStr = DateUtils.formatDate(futureDate, 'YYYY-MM-DD HH:mm:ss');
    
    const resultSet = await this.store.querySql(`
      SELECT * FROM ${AppConstants.TABLE_CAPSULES} 
      WHERE isOpened = 0 AND openDate > '${nowStr}' AND openDate <= '${futureStr}'
      ORDER BY openDate ASC
    `);
    
    const capsules: CapsuleModel[] = [];
    
    if (resultSet.goToFirstRow()) {
      do {
        capsules.push({
          id: resultSet.getLong(resultSet.getColumnIndex('id')),
          title: resultSet.getString(resultSet.getColumnIndex('title')),
          content: resultSet.getString(resultSet.getColumnIndex('content')),
          openDate: resultSet.getString(resultSet.getColumnIndex('openDate')),
          createTime: resultSet.getString(resultSet.getColumnIndex('createTime')),
          isOpened: resultSet.getLong(resultSet.getColumnIndex('isOpened')),
          isOpenTime: resultSet.getLong(resultSet.getColumnIndex('isOpenTime')) === 1
        });
      } while (resultSet.goToNextRow());
    }
    
    resultSet.close();
    return capsules;
  }
  
  /**
   * 获取时间胶囊统计信息
   */
  async getCapsuleStats(): Promise<{
    total: number;
    opened: number;
    locked: number;
    expired: number;
  }> {
    if (!this.store) throw new Error('Database not initialized');
    
    const now = DateUtils.now();
    
    // 获取总数
    const totalResult = await this.store.querySql(`SELECT COUNT(*) as count FROM ${AppConstants.TABLE_CAPSULES}`);
    const total = totalResult.goToFirstRow() ? totalResult.getLong(totalResult.getColumnIndex('count')) : 0;
    totalResult.close();
    
    // 获取已开启数量
    const openedResult = await this.store.querySql(`SELECT COUNT(*) as count FROM ${AppConstants.TABLE_CAPSULES} WHERE isOpened = 1`);
    const opened = openedResult.goToFirstRow() ? openedResult.getLong(openedResult.getColumnIndex('count')) : 0;
    openedResult.close();
    
    // 获取已过期可开启数量
    const expiredResult = await this.store.querySql(`SELECT COUNT(*) as count FROM ${AppConstants.TABLE_CAPSULES} WHERE isOpened = 0 AND openDate <= '${now}'`);
    const expired = expiredResult.goToFirstRow() ? expiredResult.getLong(expiredResult.getColumnIndex('count')) : 0;
    expiredResult.close();
    
    // 计算锁定数量
    const locked = total - opened - expired;
    
    return {
      total,
      opened,
      locked,
      expired
    };
  }
  
  // ===== 设置相关操作 =====
  
  /**
   * 获取设置
   */
  async getSettings(): Promise<SettingsModel | null> {
    if (!this.store) throw new Error('Database not initialized');
    
    const resultSet = await this.store.querySql('SELECT * FROM settings LIMIT 1');
    let settings: SettingsModel | null = null;
    
    if (resultSet.goToFirstRow()) {
      settings = {
        id: resultSet.getLong(resultSet.getColumnIndex('id')),
        darkMode: resultSet.getLong(resultSet.getColumnIndex('darkMode')),
        reminderTime: resultSet.getString(resultSet.getColumnIndex('reminderTime')),
        reminderEnabled: resultSet.getLong(resultSet.getColumnIndex('reminderEnabled')),
        syncEnabled: resultSet.getLong(resultSet.getColumnIndex('syncEnabled')),
        backupEnabled: resultSet.getLong(resultSet.getColumnIndex('backupEnabled')),
        fontSize: resultSet.getLong(resultSet.getColumnIndex('fontSize'))
      };
    }
    
    resultSet.close();
    return settings;
  }
  
  /**
   * 更新设置
   */
  async updateSettings(settings: Partial<SettingsModel>): Promise<void> {
    if (!this.store) throw new Error('Database not initialized');
    
    const pred = new relationalStore.RdbPredicates('settings');
    pred.equalTo('id', 1);
    
    await this.store.update(settings, pred);
  }
}