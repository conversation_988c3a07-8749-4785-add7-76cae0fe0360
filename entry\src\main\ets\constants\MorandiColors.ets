// 莫兰迪色系颜色定义
export class MorandiColors {
  // 主色调
  static readonly primary = '#7D6B83';
  static readonly secondary = '#9B8A7E';
  static readonly accent = '#D4A5A5';
  
  // 背景色
  static readonly background = '#F8F6F2';
  static readonly cardBackground = '#F0E6DC';
  static readonly surface = '#FAFAFA';
  
  // 文字颜色
  static readonly textPrimary = '#7D6B83';
  static readonly textSecondary = '#9B8A7E';
  static readonly textTertiary = '#B8A391';
  static readonly textHint = '#C4B5A0';
  
  // 边框颜色
  static readonly border = '#E6DDD3';
  static readonly divider = '#D4C4B0';
  
  // 分类颜色
  static readonly categoryWork = '#A8B8A0';
  static readonly categoryLife = '#D4A5A5';
  static readonly categoryStudy = '#A5A5D4';
  static readonly categoryPlan = '#D4C4A0';
  
  // 状态颜色
  static readonly success = '#A8B8A0';
  static readonly warning = '#D4C4A0';
  static readonly error = '#E6A5A5';
  static readonly info = '#A5A5D4';
  
  // 阴影颜色
  static readonly shadow = 'rgba(176, 154, 133, 0.2)';
}