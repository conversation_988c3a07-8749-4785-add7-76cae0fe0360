# TimeNotes PRD

## 1. 项目背景

### 1.1 项目概述
TimeNotes是一款基于鸿蒙系统的优雅笔记应用，专注于记录生活点滴，捕捉每一个灵感瞬间。该应用将充分利用鸿蒙系统的分布式能力，实现跨设备无缝同步，让回忆和创意随时可得。

### 1.2 目标用户
- **主要用户**：学生、职场人士、需要快速记录信息的用户
- **用户特征**：
  - 经常在手机、平板、电脑间切换
  - 需要快速记录灵感、待办事项
  - 注重数据安全和隐私

### 1.3 应用场景
- 记录生活中的美好瞬间和感悟
- 捕捉突如其来的灵感和创意
- 整理会议要点和学习心得
- 制定购物清单和待办事项
- 跨设备继续编辑，时光不中断

### 1.4 产品价值
- 优雅简洁的界面设计，让记录成为一种享受
- 实时跨设备同步，灵感不会丢失
- 智能分类和搜索，快速找到所需内容
- 离线可用，联网自动同步
- 数据安全加密存储，保护隐私

## 2. 功能清单

### 2.1 核心功能（MVP版本）

#### 2.1.1 时光记录
- **创建时光记录**
  - 文本输入支持
  - 自动保存草稿
  - 创建时间记录
  - 心情标签
- **编辑时光记录**
  - 富文本编辑（基础格式）
  - 字数统计
  - 最后修改时间
  - 图片插入
- **删除时光记录**
  - 软删除（时光回收站）
  - 彻底删除
  - 批量操作支持
- **查看时光记录**
  - 时间轴视图
  - 日历视图
  - 搜索功能
  - 排序方式（时间、标题、心情）

#### 2.1.2 时光分类
- **默认分类**
  - 全部时光
  - 工作时光
  - 生活时光
  - 学习时光
  - 心情日记
- **自定义分类**
  - 创建分类
  - 编辑分类名称
  - 删除分类
  - 分类颜色标记
  - 分类图标

#### 2.1.3 数据同步
- **本地存储**
  - RDB数据库存储
  - 自动备份机制
- **跨设备同步**
  - 同一华为账号自动同步
  - 冲突解决策略
  - 同步状态显示

### 2.2 扩展功能（V2.0版本）

#### 2.2.1 高级编辑
- **富文本增强**
  - 字体样式（加粗、斜体、下划线）
  - 列表支持（有序、无序）
  - 插入图片和视频
  - 语音转文字
  - 手写输入
- **协作功能**
  - 时光分享
  - 协同编辑
  - 评论互动
  - 时光相册

#### 2.2.2 AI管理系统
- **AI配置中心**
  - AI功能总开关
  - API密钥配置（支持OpenAI、百度文心、阿里通义千问等）
  - 模型选择（GPT-3.5/4.0、文心一言、通义千问等）
  - Token使用限额设置
- **Token监控**
  - 实时Token消耗显示
  - 每日/每月使用统计
  - 超额提醒功能
  - 使用报告生成
- **AI时光助手**
  - 智能分类建议
  - 内容摘要生成
  - 关键词提取
  - 情感分析
  - 智能写作建议
- **时光提醒**
  - 定时提醒
  - 位置提醒
  - 重复提醒
  - 时光胶囊（定时开启）

#### 2.2.3 时光管理
- **多格式支持**
  - 导出为PDF时光册
  - 导出为TXT
  - 导入TXT文件
  - 时光备份与恢复
- **时光回顾**
  - 时光相册生成
  - 年度时光报告
  - 时光线回顾
  - 批量分享
  - 打印时光册

## 3. 技术架构

### 3.1 系统架构图
```
┌─────────────────────────────────────────────────────┐
│                    Presentation Layer                 │
├─────────────────────┬───────────────────────────────┤
│   Main Page        │   Note Edit Page               │
│   Category Page    │   Search Page                  │
│   Settings Page    │   Sync Status Page             │
└─────────────────────┴───────────────────────────────┘
┌─────────────────────────────────────────────────────┐
│                    Business Layer                   │
├─────────────────────┬───────────────────────────────┤
│   Note Manager     │   Category Manager             │
│   Sync Manager     │   Search Manager               │
│   Backup Manager   │   Settings Manager             │
│   AI Manager       │   Token Monitor                │
└─────────────────────┴───────────────────────────────┘
┌─────────────────────────────────────────────────────┐
│                    Data Layer                       │
├─────────────────────┬───────────────────────────────┤
│   Local Database   │   Distributed Data Manager     │
│   File Storage     │   Network Request             │
│   Preferences      │   Encryption Manager          │
└─────────────────────┴───────────────────────────────┘
┌─────────────────────────────────────────────────────┐
│                 System Services                    │
├─────────────────────┬───────────────────────────────┤
│   Device Manager   │   Account Service              │
│   Network Monitor  │   Background Service          │
│   Notification     │   File System                 │
└─────────────────────┴───────────────────────────────┘
```

### 3.2 核心技术栈

#### 3.2.1 前端技术
- **UI框架**：ArkUI（声明式UI）
- **开发语言**：ArkTS
- **状态管理**：
  - @State：组件内部状态
  - @Prop：父子组件传值
  - @Link：双向数据绑定
  - @Observed：对象状态监听
  - @Watch：状态变化监听

#### 3.2.2 数据存储
- **关系型数据库**：RDB（Relational Database）
- **分布式数据**：Distributed Data
- **首选项**：Preferences（用户设置）
- **文件存储**：Internal Storage（图片附件）

#### 3.2.3 网络通信
- **HTTP请求**：@ohos.net.http
- **WebSocket**：实时同步（可选）
- **数据格式**：JSON

### 3.3 鸿蒙API使用清单

#### 3.3.1 基础API
- `@ohos.app.ability.UIAbility`：应用生命周期管理
- `@ohos.window`：窗口管理
- `@ohos.router`：页面路由导航

#### 3.3.2 数据存储API
- `@ohos.data.relationalStore`：RDB数据库操作
- `@ohos.data.distributedData`：分布式数据管理
- `@ohos.security.cryptoFramework`：数据加密

#### 3.3.3 系统能力API
- `@ohos.account.osAccount`：账号管理
- `@ohos.net.connection`：网络状态监控
- `@ohos.backgroundTaskManager`：后台任务管理
- `@ohos.notification`：通知服务

#### 3.3.4 媒体API
- `@ohos.multimedia.mediaLibrary`：媒体文件访问
- `@ohos.multimedia.image`：图片处理

### 3.4 第三方依赖
- **日志框架**：hi-log（鸿蒙原生）
- **网络请求封装**：自行封装基于@ohos.net.http
- **日期处理**：dayjs（轻量级日期库）

## 4. 数据模型设计

### 4.1 实体关系图
```
┌─────────────┐       ┌─────────────┐
│   Category  │◄─────►│    Note     │
├─────────────┤       ├─────────────┤
│ id: Long    │       │ id: Long    │
│ name: String│       │ title: String│
│ color: String│      │ content: Text│
│ userId: Long│       │ categoryId:Long│
│ createTime:Date│    │ userId: Long │
│ updateTime:Date│    │ createTime:Date│
│ isDefault:Boolean │ │ updateTime:Date│
│             │       │ status: Int   │
│             │       │ isSynced:Boolean│
└─────────────┘       │ syncTime:Date │
                      └─────────────┘
                              ▲
                              │
                     ┌─────────────┐
                     │   SyncLog   │
                     ├─────────────┤
                     │ id: Long    │
                     │ noteId: Long│
                     │ action: String│
                     │ deviceId: String│
                     │ syncTime:Date│
                     └─────────────┘
```

### 4.2 数据表结构

#### 4.2.1 时光记录表（time_notes）
```sql
CREATE TABLE time_notes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    category_id INTEGER,
    mood_id INTEGER, -- 心情ID
    location VARCHAR(255), -- 位置信息
    weather VARCHAR(50), -- 天气
    images TEXT, -- 图片路径JSON
    tags TEXT, -- 标签JSON
    user_id INTEGER NOT NULL,
    create_time INTEGER NOT NULL,
    update_time INTEGER NOT NULL,
    status INTEGER DEFAULT 1, -- 1:正常 0:删除
    is_synced INTEGER DEFAULT 0, -- 0:未同步 1:已同步
    sync_time INTEGER,
    FOREIGN KEY (category_id) REFERENCES categories(id)
);
```

#### 4.2.2 分类表（categories）
```sql
CREATE TABLE categories (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL,
    color VARCHAR(7) DEFAULT '#007DFF',
    user_id INTEGER NOT NULL,
    create_time INTEGER NOT NULL,
    update_time INTEGER NOT NULL,
    is_default INTEGER DEFAULT 0 -- 1:默认分类 0:自定义分类
);
```

#### 4.2.3 心情表（moods）
```sql
CREATE TABLE moods (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(20) NOT NULL, -- 开心、悲伤、愤怒等
    icon VARCHAR(50), -- 表情图标
    color VARCHAR(7), -- 代表颜色
    sort INTEGER DEFAULT 0
);
```

#### 4.2.4 同步日志表（sync_logs）
```sql
CREATE TABLE sync_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    note_id INTEGER NOT NULL,
    action VARCHAR(50) NOT NULL, -- CREATE/UPDATE/DELETE
    device_id VARCHAR(100) NOT NULL,
    sync_time INTEGER NOT NULL,
    FOREIGN KEY (note_id) REFERENCES time_notes(id)
);
```

#### 4.2.5 AI配置表（ai_configs）
```sql
CREATE TABLE ai_configs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    provider VARCHAR(50) NOT NULL, -- OpenAI、Baidu、Alibaba等
    api_key TEXT NOT NULL,
    model VARCHAR(50) NOT NULL, -- gpt-3.5-turbo、gpt-4等
    daily_limit INTEGER DEFAULT 10000, -- 每日Token限制
    monthly_limit INTEGER DEFAULT 300000, -- 每月Token限制
    is_enabled INTEGER DEFAULT 1, -- 是否启用
    create_time INTEGER NOT NULL,
    update_time INTEGER NOT NULL
);
```

#### 4.2.6 Token使用记录表（token_usage）
```sql
CREATE TABLE token_usage (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    provider VARCHAR(50) NOT NULL,
    tokens_used INTEGER NOT NULL,
    function_type VARCHAR(50) NOT NULL, -- 功能类型：summary、classify等
    request_time INTEGER NOT NULL,
    cost DECIMAL(10,6) -- 花费金额（美元）
);
```

### 4.3 数据存储方案

#### 4.3.1 本地存储策略
- **便签内容**：存储在RDB数据库
- **图片附件**：存储在应用私有目录
- **用户设置**：使用Preferences存储
- **缓存数据**：使用内存缓存+LRU策略

#### 4.3.2 分布式同步策略
- **同步触发条件**：
  - 应用启动时
  - 便签内容变更
  - 网络状态恢复
  - 定时同步（每30分钟）
- **冲突解决策略**：
  - 最后修改时间优先
  - 手动合并选项
  - 版本历史记录

#### 4.3.3 数据加密方案
- **敏感数据**：AES-256加密
- **密钥管理**：基于鸿蒙Keystore系统
- **传输加密**：HTTPS + 数据加密

## 5. UI设计系统

### 5.1 设计理念
TimeNotes采用莫兰迪色系作为官方设计风格，通过柔和、低饱和度的色彩营造优雅、温暖的视觉体验。莫兰迪色系源自意大利画家乔治·莫兰迪的作品，特点是：
- 色彩柔和，不刺眼
- 带有灰调，高级感强
- 长时间使用不易疲劳
- 营造宁静、私密的记录氛围

### 5.2 色彩系统

#### 5.2.1 主色调
- **主色**: #7D6B83 (深灰紫)
- **辅色**: #9B8A7E (灰褐色)
- **强调色**: #D4A5A5 (灰粉色)

#### 5.2.2 背景色系
- **主背景**: #F8F6F2 (米白色)
- **卡片背景**: #F0E6DC (浅米色)
- **表面背景**: #FAFAFA (纯白色)

#### 5.2.3 文字色系
- **主要文字**: #7D6B83
- **次要文字**: #9B8A7E
- **提示文字**: #B8A391
- **禁用文字**: #C4B5A0

#### 5.2.4 分类颜色
- **生活感悟**: #D4A5A5 (灰粉)
- **工作**: #A8B8A0 (灰绿)
- **学习**: #A5A5D4 (灰紫)
- **计划**: #D4C4A0 (灰黄)

### 5.3 主要页面流程
```
启动页 → 主页面（时间轴） → 笔记列表
                    ↓
              日历视图
                    ↓
              统计页面
                    ↓
              个人中心
                    ↓
              搜索页面
                    ↓
              时间胶囊
                    ↓
              设置页面
```

### 5.4 核心界面描述

#### 5.4.1 主页面（时间轴）
- **顶部栏**：
  - 应用标题"TimeNotes"（使用主色 #7D6B83）
  - 当前日期和星期
  - 搜索入口
- **内容区域**：
  - 笔记卡片列表，按时间倒序排列
  - 每个卡片包含：时间、标题、内容预览、标签
  - 卡片背景使用 #F0E6DC，边框使用 #E6DDD3
  - 悬浮效果：hover时轻微上浮，阴影加深
- **底部导航**：
  - 四个主要入口：时间轴、日历、统计、我的
  - 选中状态使用强调色 #D4A5A5
  - 图标使用简单的符号或Emoji

#### 5.4.2 日历页面
- **月份导航**：
  - 左右箭头切换月份
  - 显示当前年月
- **日历网格**：
  - 7×6网格布局
  - 有笔记的日期显示小圆点
  - 今日使用强调色背景
- **底部列表**：
  - 显示选中日期的笔记列表
  - 点击日期切换显示内容

#### 5.4.3 统计页面
- **数据概览**：
  - 4个统计卡片：总笔记数、连续记录、分类数、完成率
  - 卡片使用莫兰迪色系的不同颜色
- **图表区域**：
  - 本月记录趋势图（折线图）
  - 分类占比图（饼图）
- **详细统计**：
  - 各分类的笔记数量
  - 使用分类颜色作为标识

#### 5.4.4 个人中心页面
- **用户信息**：
  - 头像、昵称、个性签名
  - 使用渐变背景，营造层次感
- **数据统计**：
  - 笔记、天数、胶囊数量
  - 横向排列，清晰展示
- **功能入口**：
  - 个人资料、设置等
  - 箭头指示，可点击跳转

#### 5.4.5 搜索页面
- **搜索框**：
  - 圆角设计，带搜索图标
  - 实时搜索建议
- **筛选标签**：
  - 全部、生活感悟、工作、学习、计划
  - 选中状态背景变色
- **搜索结果**：
  - 高亮关键词
  - 显示匹配度

#### 5.4.6 时间胶囊页面
- **创建按钮**：
  - 使用强调色，突出显示
- **胶囊列表**：
  - 锁定状态显示🔒图标
  - 倒计时显示剩余天数
  - 已过期显示"可开启"

#### 5.4.7 设置页面
- **分组设置**：
  - 通用、数据、关于三个分组
  - 分组标题使用小字号和浅色
- **设置项**：
  - 图标+标题+描述+箭头
  - 点击进入二级页面

#### 5.2.2 时光编辑页
- **顶部栏**：
  - 返回按钮
  - 标题输入框
  - 保存/更多选项
- **工具栏**：
  - 心情选择
  - 格式化工具（加粗、斜体、列表）
  - 插入图片/视频
  - 语音输入
  - 手写输入
  - 位置标记
  - 天气选择
  - 分类选择
- **编辑区**：
  - 富文本编辑器
  - 自动保存提示
  - 字数统计
  - 标签输入
- **底部栏**：
  - 创建时间
  - 修改时间
  - 同步状态
  - 心情图标

#### 5.2.3 时光分类管理页
- **分类列表**：
  - 显示所有分类
  - 图标和颜色标识
  - 时光记录数量统计
  - 编辑/删除操作
- **添加分类**：
  - 输入分类名称
  - 选择分类图标
  - 选择分类颜色
  - 设为默认分类选项

#### 5.2.4 时光回顾页
- **时间轴视图**：
  - 垂直时间轴展示
  - 按年月分组
  - 心情趋势图
  - 照片墙展示
- **统计信息**：
  - 时光记录总数
  - 分类占比
  - 常用心情统计
  - 活跃时间段
- **年度报告**：
  - 年度时光报告生成
  - 精彩瞬间回顾
  - 分享功能

### 5.3 交互设计说明

#### 5.3.1 手势操作
- **左滑**：删除/移动到分类
- **右滑**：标记/置顶
- **长按**：进入选择模式
- **双指缩放**：调整字体大小

#### 5.3.2 动画效果
- **页面转场**：淡入淡出
- **列表项**：滑动删除动画
- **按钮点击**：涟漪效果
- **加载状态**：进度条动画

#### 5.3.3 反馈机制
- **操作成功**：Toast提示
- **错误提示**：弹窗说明
- **同步状态**：状态图标变化
- **网络异常**：离线模式提示

## 6. 开发计划与进度

### 6.1 已完成工作

#### 6.1.1 项目基础架构 ✓
- **API版本**：降级至API 9，支持更多鸿蒙设备
- **设备支持**：手机、平板、手表、电视、车机
- **包名**：com.timenotes.app
- **权限配置**：网络、存储、分布式数据同步

#### 6.1.2 数据层实现 ✓
- **数据库服务**：DatabaseService.ets
  - RDB数据库操作封装
  - 笔记CRUD功能
  - 时间胶囊管理
  - 设置存储
- **数据模型**：
  - NoteModel：笔记数据结构
  - CapsuleModel：时间胶囊数据结构
  - SettingsModel：应用设置结构

#### 6.1.3 莫兰迪色系主题 ✓
- **颜色系统**：MorandiColors.ets
  - 主色调、背景色、文字色定义
  - 分类颜色规范
  - 统一的色彩管理

#### 6.1.4 通用组件库 ✓
- **基础组件**：
  - MyButton：按钮组件（主色、次要、文本）
  - MyCard：卡片组件（带阴影、圆角）
  - BottomNavBar：底部导航栏
- **工具类**：
  - DateUtils：日期时间处理

### 6.2 当前开发任务

#### 6.2.1 页面开发（进行中）
- [ ] 时间轴页面（主页面）
- [ ] 日历页面（月视图）
- [ ] 统计页面（数据可视化）
- [ ] 个人中心页面
- [ ] 搜索页面
- [ ] 时间胶囊页面
- [ ] 设置页面
- [ ] 笔记编辑页面

#### 6.2.2 功能集成
- [ ] 路由系统实现
- [ ] 页面间数据传递
- [ ] 状态管理优化
- [ ] 主题统一应用

#### 第二周：核心功能实现
- **Day 8-9**：便签编辑功能
  - 编辑页面UI
  - 富文本编辑器
  - 自动保存机制
- **Day 10-11**：分类管理
  - 分类CRUD操作
  - 分类颜色选择
  - 便签分类关联
- **Day 12-14**：搜索功能
  - 全文搜索实现
  - 搜索结果展示
  - 搜索历史记录

#### 第三周：数据同步
- **Day 15-16**：本地同步机制
  - 同步策略设计
  - 冲突检测解决
  - 同步日志记录
- **Day 17-18**：分布式同步
  - 鸿蒙账号集成
  - 跨设备数据同步
  - 离线同步处理
- **Day 19-21**：优化和测试
  - 性能优化
  - 单元测试编写
  - Bug修复

#### 第四周：完善和发布
- **Day 22-23**：UI/UX优化
  - 动画效果完善
  - 交互细节优化
  - 适配不同屏幕
- **Day 24-25**：设置和AI管理
  - 用户设置实现
  - AI配置中心开发
  - Token监控系统
  - 数据备份恢复
  - 关于页面
- **Day 26-28**：最终测试
  - 集成测试
  - 用户验收测试
  - 准备发布

### 6.2 里程碑设置

#### 里程碑1：第一周末
- [x] 完成基础UI框架
- [x] 实现便签列表展示
- [x] 完成数据模型设计

#### 里程碑2：第二周末
- [x] 实现便签编辑功能
- [x] 完成分类管理
- [x] 搜索功能可用

#### 里程碑3：第三周末
- [x] 本地数据同步正常
- [x] 跨设备同步功能
- [x] 核心功能稳定

#### 里程碑4：第四周末
- [x] 所有功能完成
- [x] 通过测试验证
- [x] 准备应用上架

## 7. 开发环境配置

### 7.1 DevEco Studio配置

#### 7.1.1 项目配置
```json
// build-profile.json5
{
  "app": {
    "signingConfigs": [],
    "compileSdkVersion": 12,
    "compatibleSdkVersion": 12,
    "products": [
      {
        "name": "default",
        "signingConfig": "default",
      }
    ]
  },
  "modules": [
    {
      "name": "entry",
      "srcPath": "./entry",
      "targets": [
        {
          "name": "default",
          "applyToProducts": [
            "default"
          ]
        }
      ]
    }
  ]
}
```

#### 7.1.2 模块配置
```json
// module.json5
{
  "module": {
    "name": "entry",
    "type": "entry",
    "description": "$string:module_desc",
    "mainElement": "EntryAbility",
    "deviceTypes": [
      "phone",
      "tablet"
    ],
    "deliveryWithInstall": true,
    "installationFree": false,
    "pages": "$profile:main_pages",
    "abilities": [
      {
        "name": "EntryAbility",
        "srcEntry": "./ets/entryability/EntryAbility.ets",
        "description": "$string:EntryAbility_desc",
        "icon": "$media:icon",
        "label": "$string:EntryAbility_label",
        "startWindowIcon": "$media:icon",
        "startWindowBackground": "$color:start_window_background",
        "exported": true,
        "skills": [
          {
            "entities": [
              "entity.system.home"
            ],
            "actions": [
              "action.system.home"
            ]
          }
        ]
      }
    ],
    "requestPermissions": [
      {
        "name": "ohos.permission.INTERNET"
      },
      {
        "name": "ohos.permission.WRITE_USER_STORAGE"
      },
      {
        "name": "ohos.permission.READ_USER_STORAGE"
      },
      {
        "name": "ohos.permission.DISTRIBUTED_DATASYNC"
      }
    ]
  }
}
```

### 7.2 SDK版本要求
- **最小SDK版本**：API 10 (4.0)
- **目标SDK版本**：API 12 (5.0)
- **编译SDK版本**：API 12 (5.0)

### 7.3 模拟器/真机调试

#### 7.3.1 模拟器配置
- **设备类型**：P50 Pro
- **系统版本**：HarmonyOS 5.0
- **内存配置**：6GB
- **存储配置**：64GB

#### 7.3.2 真机调试准备
1. **开发者选项**
   - 开启"开发者模式"
   - 启用"USB调试"
   - 开启"允许模拟位置"

2. **调试证书**
   - 申请调试证书
   - 配置应用签名
   - 安装调试HAP包

## 8. 测试计划

### 8.1 单元测试范围

#### 8.1.1 数据层测试
- **数据库操作测试**
  - 便签CRUD功能
  - 分类管理功能
  - 事务处理
- **同步逻辑测试**
  - 同步策略验证
  - 冲突解决测试
  - 网络异常处理

#### 8.1.2 业务逻辑测试
- **便签管理测试**
  - 创建、编辑、删除
  - 分类关联关系
  - 搜索功能验证
- **工具类测试**
  - 日期格式化
  - 加密解密
  - 数据验证

### 8.2 集成测试方案

#### 8.2.1 UI集成测试
- **页面跳转测试**
  - 主页面→编辑页
  - 主页面→分类页
  - 搜索流程测试
- **数据流测试**
  - UI→数据层→UI
  - 跨页面数据传递
  - 状态管理测试

#### 8.2.2 系统能力集成测试
- **权限申请测试**
  - 首次授权流程
  - 拒绝权限处理
  - 权限管理页面跳转
- **网络功能测试**
  - 在线同步功能
  - 离线模式测试
  - 网络切换处理

### 8.3 用户验收测试

#### 8.3.1 功能验收清单
- **基础功能**
  - [x] 可以创建便签
  - [x] 可以编辑便签内容
  - [x] 可以删除便签
  - [x] 可以创建分类
  - [x] 可以搜索便签
- **同步功能**
  - [x] 本地数据保存正常
  - [x] 跨设备同步成功
  - [x] 离线可正常使用
  - [x] 同步冲突可解决
- **性能要求**
  - [x] 启动时间<2秒
  - [x] 列表滑动流畅
  - [x] 搜索响应<500ms
  - [x] 同步速度可接受

#### 8.3.2 兼容性测试
- **设备兼容性**
  - 手机（4.5-7.0英寸）
  - 平板（8-13英寸）
  - 折叠屏设备
- **系统版本**
  - HarmonyOS 4.0
  - HarmonyOS 4.1
  - HarmonyOS 5.0

### 8.4 性能测试指标

#### 8.4.1 响应时间
- **应用启动**：<2秒
- **页面跳转**：<300ms
- **搜索响应**：<500ms
- **同步完成**：<3秒（100条便签）

#### 8.4.2 资源占用
- **内存占用**：<100MB（正常使用）
- **CPU使用**：<10%（后台）
- **存储空间**：<50MB（安装包）
- **网络流量**：<1MB/次同步

#### 8.4.3 稳定性要求
- **崩溃率**：<0.1%
- **ANR率**：<0.05%
- **成功率**：>99.9%
- **数据丢失**：0%

---

## 附录

### A. 参考文档
- [HarmonyOS开发指南](https://developer.harmonyos.com/cn/docs/documentation/doc-guides/)
- [ArkUI开发文档](https://developer.harmonyos.com/cn/docs/documentation/doc-guides/ui-arkui-overview-0000001199035624)
- [分布式数据服务开发指南](https://developer.harmonyos.com/cn/docs/documentation/doc-guides/database-distributed-guidelines-0000000000032574)

### B. 术语表
- **PRD**：Product Requirements Document（产品需求文档）
- **MVP**：Minimum Viable Product（最小可行产品）
- **RDB**：Relational Database（关系型数据库）
- **CRUD**：Create, Read, Update, Delete（增删改查）
- **API**：Application Programming Interface（应用程序接口）

### C. 更新日志
- **V1.0**：初始版本
- **V1.1**：添加扩展功能描述
- **V1.2**：完善测试计划
- **V2.0**：采用莫兰迪色系设计，更新UI原型

## 7. 项目文件结构

### 7.1 目录结构
```
TimeNotes/
├── AppScope/
│   ├── app.json5              # 应用配置
│   └── resources/             # 应用资源
├── entry/
│   ├── src/main/
│   │   ├── ets/
│   │   │   ├── components/     # 通用组件
│   │   │   │   ├── Basic/      # 基础组件
│   │   │   │   ├── Navigation/ # 导航组件
│   │   │   │   └── index.ets   # 组件导出
│   │   │   ├── constants/     # 常量定义
│   │   │   │   ├── MorandiColors.ets
│   │   │   │   └── AppConstants.ets
│   │   │   ├── model/         # 数据模型
│   │   │   │   └── NoteModel.ets
│   │   │   ├── services/      # 服务层
│   │   │   │   └── DatabaseService.ets
│   │   │   ├── utils/         # 工具类
│   │   │   │   └── DateUtils.ets
│   │   │   ├── entryability/  # 应用入口
│   │   │   │   └── EntryAbility.ets
│   │   │   └── pages/         # 页面
│   │   │       ├── Index.ets   # 启动页
│   │   │       ├── Main.ets    # 主框架页
│   │   │       └── [其他页面]
│   │   └── resources/          # 资源文件
│   └── build-profile.json5    # 模块配置
└── hvigorw.json5              # 构建配置
```

### 7.2 关键文件说明

#### 7.2.1 配置文件
- **app.json5**: 应用级配置，包含包名、版本、API版本
- **module.json5**: 模块配置，包含设备类型、权限声明
- **main_pages.json**: 页面路由配置

#### 7.2.2 核心代码
- **EntryAbility.ets**: 应用生命周期管理
- **DatabaseService.ets**: 数据库操作服务
- **MorandiColors.ets**: 莫兰迪色系定义

### 7.3 开发指南

#### 7.3.1 新建页面
1. 在 `pages/` 目录下创建 `.ets` 文件
2. 在 `main_pages.json` 中注册页面
3. 使用 `@Entry` 和 `@Component` 装饰器

#### 7.3.2 使用主题色
```typescript
import { MorandiColors } from '../constants/MorandiColors';

// 使用颜色
Text('标题')
  .fontColor(MorandiColors.textPrimary)
  .backgroundColor(MorandiColors.cardBackground)
```

#### 7.3.3 数据库操作
```typescript
import { DatabaseService } from '../services/DatabaseService';

// 获取实例
const db = DatabaseService.getInstance();

// 保存笔记
await db.saveNote(note);
```