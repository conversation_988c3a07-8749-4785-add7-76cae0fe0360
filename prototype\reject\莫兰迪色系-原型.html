<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时光拾光 - 莫兰迪色系原型</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: #F5F2ED;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .phone-container {
            width: 375px;
            height: 812px;
            background: #F8F6F2;
            border-radius: 30px;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(176, 166, 149, 0.2);
            position: relative;
        }

        .status-bar {
            height: 44px;
            background: #F8F6F2;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 24px;
            font-size: 14px;
            color: #A8998A;
        }

        .content {
            height: calc(100% - 44px);
            display: flex;
            flex-direction: column;
        }

        .header {
            padding: 20px 24px;
            background: #F8F6F2;
        }

        .header h1 {
            font-size: 28px;
            font-weight: 400;
            color: #7D6B83;
            letter-spacing: 1px;
            margin-bottom: 8px;
        }

        .header .date {
            font-size: 14px;
            color: #B8A391;
            letter-spacing: 0.5px;
        }

        .timeline {
            flex: 1;
            overflow-y: auto;
            padding: 0 24px;
            padding-bottom: 100px;
        }

        .note-card {
            background: #F0E6DC;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            border: 1px solid #E6DDD3;
            transition: all 0.3s ease;
        }

        .note-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(176, 154, 133, 0.15);
        }

        .note-time {
            font-size: 12px;
            color: #C4B5A0;
            margin-bottom: 8px;
            letter-spacing: 0.5px;
        }

        .note-title {
            font-size: 18px;
            font-weight: 400;
            color: #7D6B83;
            margin-bottom: 8px;
            line-height: 1.4;
        }

        .note-content {
            font-size: 14px;
            color: #9B8A7E;
            line-height: 1.6;
            margin-bottom: 12px;
        }

        .note-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .tag {
            font-size: 11px;
            color: #A8998A;
            background: #E6DDD3;
            padding: 4px 12px;
            border-radius: 16px;
            letter-spacing: 0.5px;
        }

        .bottom-nav {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: #F8F6F2;
            border-top: 1px solid #E6DDD3;
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding-bottom: 20px;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            color: #C4B5A0;
            transition: color 0.3s ease;
            cursor: pointer;
        }

        .nav-item.active {
            color: #D4A5A5;
        }

        .nav-icon {
            width: 24px;
            height: 24px;
            fill: currentColor;
        }

        .nav-text {
            font-size: 10px;
            letter-spacing: 0.5px;
        }

        .fab {
            position: absolute;
            bottom: 100px;
            right: 24px;
            width: 56px;
            height: 56px;
            background: #D4A5A5;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 8px 20px rgba(212, 165, 165, 0.3);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .fab:hover {
            transform: scale(1.05);
            box-shadow: 0 12px 28px rgba(212, 165, 165, 0.4);
        }

        .fab-icon {
            width: 24px;
            height: 24px;
            fill: #F8F6F2;
        }

        /* 滚动条样式 */
        .timeline::-webkit-scrollbar {
            width: 4px;
        }

        .timeline::-webkit-scrollbar-track {
            background: #F8F6F2;
        }

        .timeline::-webkit-scrollbar-thumb {
            background: #E6DDD3;
            border-radius: 2px;
        }

        .category-indicator {
            display: inline-block;
            width: 4px;
            height: 16px;
            border-radius: 2px;
            margin-right: 8px;
            vertical-align: middle;
        }

        .category-work { background: #A8B8A0; }
        .category-life { background: #D4A5A5; }
        .category-study { background: #A5A5D4; }
        .category-plan { background: #D4C4A0; }
    </style>
</head>
<body>
    <div class="phone-container">
        <div class="status-bar">
            <span>9:41</span>
            <span>●●●●●</span>
            <span>100%</span>
        </div>
        
        <div class="content">
            <div class="header">
                <h1>时光拾光</h1>
                <div class="date">2024年3月15日 星期五</div>
            </div>
            
            <div class="timeline">
                <div class="note-card">
                    <div class="note-time">下午 2:30</div>
                    <div class="note-title">
                        <span class="category-indicator category-life"></span>
                        春日随笔
                    </div>
                    <div class="note-content">今天在公园里看到了第一朵樱花，春天真的来了。阳光透过花瓣洒在地面上，形成斑驳的光影。</div>
                    <div class="note-tags">
                        <span class="tag">生活感悟</span>
                        <span class="tag">春天</span>
                    </div>
                </div>
                
                <div class="note-card">
                    <div class="note-time">上午 10:15</div>
                    <div class="note-title">
                        <span class="category-indicator category-work"></span>
                        项目会议纪要
                    </div>
                    <div class="note-content">讨论了新产品的设计方案，确定了以用户体验为核心的设计理念。需要在下周完成原型设计。</div>
                    <div class="note-tags">
                        <span class="tag">工作</span>
                        <span class="tag">会议</span>
                    </div>
                </div>
                
                <div class="note-card">
                    <div class="note-time">昨天 晚上 8:45</div>
                    <div class="note-title">
                        <span class="category-indicator category-study"></span>
                        读书笔记
                    </div>
                    <div class="note-content">《设计心理学》第三章：好的设计应该是直观的，用户不需要思考就能知道如何使用。</div>
                    <div class="note-tags">
                        <span class="tag">学习</span>
                        <span class="tag">设计</span>
                    </div>
                </div>
                
                <div class="note-card">
                    <div class="note-time">3月13日 下午 4:20</div>
                    <div class="note-title">
                        <span class="category-indicator category-plan"></span>
                        周末计划
                    </div>
                    <div class="note-content">1. 去美术馆看展 2. 整理房间 3. 给父母打电话 4. 准备下周的演讲稿</div>
                    <div class="note-tags">
                        <span class="tag">计划</span>
                        <span class="tag">周末</span>
                    </div>
                </div>
                
                <div class="note-card">
                    <div class="note-time">3月12日 上午 11:00</div>
                    <div class="note-title">
                        <span class="category-indicator category-life"></span>
                        美食探店
                    </div>
                    <div class="note-content">发现了一家很棒的咖啡馆，他们的手冲咖啡非常棒。店内装饰很有格调，适合一个人静静的看书。</div>
                    <div class="note-tags">
                        <span class="tag">美食</span>
                        <span class="tag">咖啡</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="bottom-nav">
            <div class="nav-item active">
                <svg class="nav-icon" viewBox="0 0 24 24">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
                <span class="nav-text">时间轴</span>
            </div>
            <div class="nav-item">
                <svg class="nav-icon" viewBox="0 0 24 24">
                    <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"/>
                </svg>
                <span class="nav-text">日历</span>
            </div>
            <div class="nav-item">
                <svg class="nav-icon" viewBox="0 0 24 24">
                    <path d="M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6z"/>
                </svg>
                <span class="nav-text">统计</span>
            </div>
            <div class="nav-item">
                <svg class="nav-icon" viewBox="0 0 24 24">
                    <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                </svg>
                <span class="nav-text">我的</span>
            </div>
        </div>
        
        <div class="fab">
            <svg class="fab-icon" viewBox="0 0 24 24">
                <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
            </svg>
        </div>
    </div>
</body>
</html>