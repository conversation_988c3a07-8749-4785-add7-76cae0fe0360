import { EnhancedDatabaseService, DatabaseResult, DatabaseObserver, DatabaseEvent } from '../services/EnhancedDatabaseService';
import { NoteModel, CapsuleModel, SettingsModel } from '../model/NoteModel';
import { StateManager } from '../state/StateManager';
import { DateUtils } from '../utils/DateUtils';
import { AppConstants } from '../constants/AppConstants';

/**
 * 仓库基础接口
 */
export interface IRepository<T> {
  findById(id: number): Promise<DatabaseResult<T | null>>;
  findAll(offset?: number, limit?: number): Promise<DatabaseResult<T[]>>;
  save(entity: T): Promise<DatabaseResult<number>>;
  update(id: number, updates: Partial<T>): Promise<DatabaseResult<void>>;
  delete(id: number): Promise<DatabaseResult<void>>;
  search(query: string): Promise<DatabaseResult<T[]>>;
  count(): Promise<DatabaseResult<number>>;
}

/**
 * 查询条件接口
 */
export interface QueryCondition {
  field: string;
  operator: 'eq' | 'ne' | 'gt' | 'lt' | 'gte' | 'lte' | 'like' | 'in' | 'notIn';
  value: any;
}

/**
 * 排序条件接口
 */
export interface SortCondition {
  field: string;
  direction: 'asc' | 'desc';
}

/**
 * 分页参数接口
 */
export interface PaginationParams {
  page: number;
  pageSize: number;
}

/**
 * 分页结果接口
 */
export interface PaginatedResult<T> {
  data: T[];
  total: number;
  page: number;
  pageSize: number;
  hasNext: boolean;
  hasPrev: boolean;
}

/**
 * 笔记仓库实现
 */
export class NoteRepository implements IRepository<NoteModel>, DatabaseObserver {
  private dbService: EnhancedDatabaseService;
  private stateManager: StateManager;
  private readonly tableName = AppConstants.TABLE_NOTES;
  
  constructor() {
    this.dbService = EnhancedDatabaseService.getInstance();
    this.stateManager = StateManager.getInstance();
    this.dbService.registerObserver('note_repository', this);
  }
  
  /**
   * 数据库事件监听
   */
  onDatabaseEvent(event: DatabaseEvent): void {
    if (event.table === this.tableName) {
      // 清除相关缓存
      this.clearCache();
      
      // 更新状态
      this.updateStateFromEvent(event);
    }
  }
  
  getObservedTables(): string[] {
    return [this.tableName];
  }
  
  getObservedEventTypes(): string[] {
    return ['INSERT', 'UPDATE', 'DELETE', 'BATCH_INSERT', 'BATCH_UPDATE', 'BATCH_DELETE'];
  }
  
  /**
   * 根据ID查找笔记
   */
  async findById(id: number): Promise<DatabaseResult<NoteModel | null>> {
    const cacheKey = `note_${id}`;
    
    // 尝试从缓存获取
    const cached = this.dbService['getFromCache']<NoteModel>(cacheKey);
    if (cached) {
      return {
        success: true,
        data: cached,
        timestamp: new Date().toISOString()
      };
    }
    
    const result = await this.dbService['withRetry'](async () => {
      const store = this.dbService['store'];
      if (!store) throw new Error('Database not initialized');
      
      const pred = new (globalThis as any).relationalStore.RdbPredicates(this.tableName);
      pred.equalTo('id', id);
      pred.equalTo('isDeleted', 0);
      
      const resultSet = await store.query(pred);
      let note: NoteModel | null = null;
      
      if (resultSet.goToFirstRow()) {
        note = this.resultSetToNote(resultSet);
      }
      
      resultSet.close();
      return note;
    });
    
    if (result.success && result.data) {
      // 缓存结果
      this.dbService['setCache'](cacheKey, result.data);
    }
    
    return result;
  }
  
  /**
   * 查找所有笔记
   */
  async findAll(offset: number = 0, limit: number = AppConstants.PAGE_SIZE): Promise<DatabaseResult<NoteModel[]>> {
    return this.dbService.getNotes(offset, limit);
  }
  
  /**
   * 分页查询笔记
   */
  async findPaginated(params: PaginationParams, conditions?: QueryCondition[], sorts?: SortCondition[]): Promise<DatabaseResult<PaginatedResult<NoteModel>>> {
    const cacheKey = `notes_paginated_${params.page}_${params.pageSize}_${JSON.stringify(conditions)}_${JSON.stringify(sorts)}`;
    
    // 尝试从缓存获取
    const cached = this.dbService['getFromCache']<PaginatedResult<NoteModel>>(cacheKey);
    if (cached) {
      return {
        success: true,
        data: cached,
        timestamp: new Date().toISOString()
      };
    }
    
    const result = await this.dbService['withRetry'](async () => {
      const store = this.dbService['store'];
      if (!store) throw new Error('Database not initialized');
      
      // 构建查询条件
      const pred = new (globalThis as any).relationalStore.RdbPredicates(this.tableName);
      pred.equalTo('isDeleted', 0);
      
      // 添加自定义条件
      if (conditions) {
        for (const condition of conditions) {
          switch (condition.operator) {
            case 'eq':
              pred.equalTo(condition.field, condition.value);
              break;
            case 'ne':
              pred.notEqualTo(condition.field, condition.value);
              break;
            case 'gt':
              pred.greaterThan(condition.field, condition.value);
              break;
            case 'lt':
              pred.lessThan(condition.field, condition.value);
              break;
            case 'gte':
              pred.greaterThanOrEqualTo(condition.field, condition.value);
              break;
            case 'lte':
              pred.lessThanOrEqualTo(condition.field, condition.value);
              break;
            case 'like':
              pred.like(condition.field, `%${condition.value}%`);
              break;
          }
        }
      }
      
      // 添加排序
      if (sorts) {
        for (const sort of sorts) {
          if (sort.direction === 'asc') {
            pred.orderByAsc(sort.field);
          } else {
            pred.orderByDesc(sort.field);
          }
        }
      } else {
        pred.orderByDesc('createTime');
      }
      
      // 获取总数
      const countPred = new (globalThis as any).relationalStore.RdbPredicates(this.tableName);
      countPred.equalTo('isDeleted', 0);
      const countResultSet = await store.query(countPred);
      const total = countResultSet.getRowCount();
      countResultSet.close();
      
      // 分页查询
      const offset = (params.page - 1) * params.pageSize;
      pred.limitAs(params.pageSize);
      pred.offsetAs(offset);
      
      const resultSet = await store.query(pred);
      const notes: NoteModel[] = [];
      
      if (resultSet.goToFirstRow()) {
        do {
          notes.push(this.resultSetToNote(resultSet));
        } while (resultSet.goToNextRow());
      }
      
      resultSet.close();
      
      return {
        data: notes,
        total,
        page: params.page,
        pageSize: params.pageSize,
        hasNext: offset + params.pageSize < total,
        hasPrev: params.page > 1
      };
    });
    
    if (result.success) {
      // 缓存结果
      this.dbService['setCache'](cacheKey, result.data, 2 * 60 * 1000); // 缓存2分钟
    }
    
    return result;
  }
  
  /**
   * 保存笔记
   */
  async save(note: NoteModel): Promise<DatabaseResult<number>> {
    const result = await this.dbService.saveNote(note);
    
    if (result.success) {
      // 更新状态
      this.stateManager.updateState({ hasUnsavedChanges: false }, 'NOTE_CREATED');
    }
    
    return result;
  }
  
  /**
   * 批量保存笔记
   */
  async saveBatch(notes: NoteModel[]): Promise<DatabaseResult<number[]>> {
    const transform = (note: NoteModel) => ({
      title: note.title,
      content: note.content,
      categoryId: note.categoryId || 1,
      tags: note.tags || '',
      mood: note.mood || '',
      weather: note.weather || '',
      location: note.location || '',
      images: note.images || '',
      voiceNote: note.voiceNote || '',
      createTime: note.createTime || DateUtils.now(),
      updateTime: note.updateTime || DateUtils.now(),
      isDeleted: note.isDeleted || 0
    });
    
    const result = await this.dbService.batchInsert(this.tableName, notes, transform);
    
    if (result.success) {
      // 更新状态
      this.stateManager.updateState({ hasUnsavedChanges: false }, 'NOTE_CREATED');
    }
    
    return result;
  }
  
  /**
   * 更新笔记
   */
  async update(id: number, updates: Partial<NoteModel>): Promise<DatabaseResult<void>> {
    const result = await this.dbService['withRetry'](async () => {
      const store = this.dbService['store'];
      if (!store) throw new Error('Database not initialized');
      
      const values: any = {};
      if (updates.title !== undefined) values.title = updates.title;
      if (updates.content !== undefined) values.content = updates.content;
      if (updates.categoryId !== undefined) values.categoryId = updates.categoryId;
      if (updates.tags !== undefined) values.tags = updates.tags;
      if (updates.mood !== undefined) values.mood = updates.mood;
      if (updates.weather !== undefined) values.weather = updates.weather;
      if (updates.location !== undefined) values.location = updates.location;
      if (updates.images !== undefined) values.images = updates.images;
      if (updates.voiceNote !== undefined) values.voiceNote = updates.voiceNote;
      
      values.updateTime = DateUtils.now();
      
      const pred = new (globalThis as any).relationalStore.RdbPredicates(this.tableName);
      pred.equalTo('id', id);
      
      await store.update(values, pred);
      
      // 清除缓存
      this.clearCache(`note_${id}`);
    });
    
    if (result.success) {
      // 更新状态
      this.stateManager.updateState({ hasUnsavedChanges: false }, 'NOTE_UPDATED');
    }
    
    return result;
  }
  
  /**
   * 删除笔记（软删除）
   */
  async delete(id: number): Promise<DatabaseResult<void>> {
    const result = await this.dbService['withRetry'](async () => {
      const store = this.dbService['store'];
      if (!store) throw new Error('Database not initialized');
      
      const values = { isDeleted: 1, updateTime: DateUtils.now() };
      const pred = new (globalThis as any).relationalStore.RdbPredicates(this.tableName);
      pred.equalTo('id', id);
      
      await store.update(values, pred);
      
      // 清除缓存
      this.clearCache(`note_${id}`);
      this.clearCache('notes_');
    });
    
    if (result.success) {
      // 更新状态
      this.stateManager.updateState({}, 'NOTE_DELETED');
    }
    
    return result;
  }
  
  /**
   * 搜索笔记
   */
  async search(query: string): Promise<DatabaseResult<NoteModel[]>> {
    return this.dbService.searchNotes(query);
  }
  
  /**
   * 高级搜索
   */
  async advancedSearch(options: {
    keyword?: string;
    category?: number;
    mood?: string;
    weather?: string;
    tags?: string[];
    startDate?: string;
    endDate?: string;
    pagination?: PaginationParams;
  }): Promise<DatabaseResult<PaginatedResult<NoteModel>>> {
    const conditions: QueryCondition[] = [];
    
    if (options.keyword) {
      conditions.push({
        field: 'title',
        operator: 'like',
        value: options.keyword
      });
    }
    
    if (options.category) {
      conditions.push({
        field: 'categoryId',
        operator: 'eq',
        value: options.category
      });
    }
    
    if (options.mood) {
      conditions.push({
        field: 'mood',
        operator: 'eq',
        value: options.mood
      });
    }
    
    if (options.weather) {
      conditions.push({
        field: 'weather',
        operator: 'eq',
        value: options.weather
      });
    }
    
    if (options.tags && options.tags.length > 0) {
      conditions.push({
        field: 'tags',
        operator: 'like',
        value: options.tags.join(',')
      });
    }
    
    if (options.startDate) {
      conditions.push({
        field: 'createTime',
        operator: 'gte',
        value: options.startDate
      });
    }
    
    if (options.endDate) {
      conditions.push({
        field: 'createTime',
        operator: 'lte',
        value: options.endDate
      });
    }
    
    const pagination = options.pagination || { page: 1, pageSize: 20 };
    
    return this.findPaginated(pagination, conditions);
  }
  
  /**
   * 获取笔记数量
   */
  async count(): Promise<DatabaseResult<number>> {
    const result = await this.dbService['withRetry'](async () => {
      const store = this.dbService['store'];
      if (!store) throw new Error('Database not initialized');
      
      const pred = new (globalThis as any).relationalStore.RdbPredicates(this.tableName);
      pred.equalTo('isDeleted', 0);
      
      const resultSet = await store.query(pred);
      const count = resultSet.getRowCount();
      resultSet.close();
      
      return count;
    });
    
    return result;
  }
  
  /**
   * 获取按日期分组的笔记统计
   */
  async getDailyStats(startDate: string, endDate: string): Promise<DatabaseResult<{ date: string; count: number }[]>> {
    const cacheKey = `notes_daily_stats_${startDate}_${endDate}`;
    
    // 尝试从缓存获取
    const cached = this.dbService['getFromCache']<{ date: string; count: number }[]>(cacheKey);
    if (cached) {
      return {
        success: true,
        data: cached,
        timestamp: new Date().toISOString()
      };
    }
    
    const result = await this.dbService['withRetry'](async () => {
      const store = this.dbService['store'];
      if (!store) throw new Error('Database not initialized');
      
      const resultSet = await store.querySql(`
        SELECT SUBSTR(createTime, 1, 10) as date, COUNT(*) as count 
        FROM ${this.tableName} 
        WHERE isDeleted = 0 AND createTime BETWEEN ? AND ?
        GROUP BY SUBSTR(createTime, 1, 10)
        ORDER BY date
      `, [startDate, endDate]);
      
      const stats: { date: string; count: number }[] = [];
      
      if (resultSet.goToFirstRow()) {
        do {
          const date = resultSet.getString(resultSet.getColumnIndex('date'));
          const count = resultSet.getLong(resultSet.getColumnIndex('count'));
          stats.push({ date, count });
        } while (resultSet.goToNextRow());
      }
      
      resultSet.close();
      return stats;
    });
    
    if (result.success) {
      // 缓存结果
      this.dbService['setCache'](cacheKey, result.data, 5 * 60 * 1000); // 缓存5分钟
    }
    
    return result;
  }
  
  /**
   * 获取心情统计
   */
  async getMoodStats(): Promise<DatabaseResult<{ mood: string; count: number }[]>> {
    const cacheKey = 'notes_mood_stats';
    
    // 尝试从缓存获取
    const cached = this.dbService['getFromCache']<{ mood: string; count: number }[]>(cacheKey);
    if (cached) {
      return {
        success: true,
        data: cached,
        timestamp: new Date().toISOString()
      };
    }
    
    const result = await this.dbService['withRetry'](async () => {
      const store = this.dbService['store'];
      if (!store) throw new Error('Database not initialized');
      
      const resultSet = await store.querySql(`
        SELECT mood, COUNT(*) as count 
        FROM ${this.tableName} 
        WHERE isDeleted = 0 AND mood != ''
        GROUP BY mood
        ORDER BY count DESC
      `);
      
      const stats: { mood: string; count: number }[] = [];
      
      if (resultSet.goToFirstRow()) {
        do {
          const mood = resultSet.getString(resultSet.getColumnIndex('mood'));
          const count = resultSet.getLong(resultSet.getColumnIndex('count'));
          stats.push({ mood, count });
        } while (resultSet.goToNextRow());
      }
      
      resultSet.close();
      return stats;
    });
    
    if (result.success) {
      // 缓存结果
      this.dbService['setCache'](cacheKey, result.data, 10 * 60 * 1000); // 缓存10分钟
    }
    
    return result;
  }
  
  /**
   * 清除缓存
   */
  private clearCache(pattern?: string): void {
    this.dbService['clearCache'](pattern);
  }
  
  /**
   * 从数据库事件更新状态
   */
  private updateStateFromEvent(event: DatabaseEvent): void {
    switch (event.type) {
      case 'INSERT':
      case 'UPDATE':
      case 'DELETE':
        // 触发笔记列表重新加载
        this.stateManager.updateState({}, 'NOTES_LOADED');
        break;
    }
  }
  
  /**
   * 将ResultSet转换为Note对象
   */
  private resultSetToNote(resultSet: any): NoteModel {
    return {
      id: resultSet.getLong(resultSet.getColumnIndex('id')),
      title: resultSet.getString(resultSet.getColumnIndex('title')),
      content: resultSet.getString(resultSet.getColumnIndex('content')),
      categoryId: resultSet.getLong(resultSet.getColumnIndex('categoryId')),
      tags: resultSet.getString(resultSet.getColumnIndex('tags')),
      mood: resultSet.getString(resultSet.getColumnIndex('mood')),
      weather: resultSet.getString(resultSet.getColumnIndex('weather')),
      location: resultSet.getString(resultSet.getColumnIndex('location')),
      images: resultSet.getString(resultSet.getColumnIndex('images')),
      voiceNote: resultSet.getString(resultSet.getColumnIndex('voiceNote')),
      createTime: resultSet.getString(resultSet.getColumnIndex('createTime')),
      updateTime: resultSet.getString(resultSet.getColumnIndex('updateTime')),
      isDeleted: resultSet.getLong(resultSet.getColumnIndex('isDeleted'))
    };
  }
}

/**
 * 时间胶囊仓库实现
 */
export class CapsuleRepository implements IRepository<CapsuleModel>, DatabaseObserver {
  private dbService: EnhancedDatabaseService;
  private stateManager: StateManager;
  private readonly tableName = AppConstants.TABLE_CAPSULES;
  
  constructor() {
    this.dbService = EnhancedDatabaseService.getInstance();
    this.stateManager = StateManager.getInstance();
    this.dbService.registerObserver('capsule_repository', this);
  }
  
  onDatabaseEvent(event: DatabaseEvent): void {
    if (event.table === this.tableName) {
      this.clearCache();
      this.updateStateFromEvent(event);
    }
  }
  
  getObservedTables(): string[] {
    return [this.tableName];
  }
  
  getObservedEventTypes(): string[] {
    return ['INSERT', 'UPDATE', 'DELETE'];
  }
  
  async findById(id: number): Promise<DatabaseResult<CapsuleModel | null>> {
    const cacheKey = `capsule_${id}`;
    
    const cached = this.dbService['getFromCache']<CapsuleModel>(cacheKey);
    if (cached) {
      return {
        success: true,
        data: cached,
        timestamp: new Date().toISOString()
      };
    }
    
    const result = await this.dbService['withRetry'](async () => {
      const store = this.dbService['store'];
      if (!store) throw new Error('Database not initialized');
      
      const pred = new (globalThis as any).relationalStore.RdbPredicates(this.tableName);
      pred.equalTo('id', id);
      
      const resultSet = await store.query(pred);
      let capsule: CapsuleModel | null = null;
      
      if (resultSet.goToFirstRow()) {
        capsule = this.resultSetToCapsule(resultSet);
      }
      
      resultSet.close();
      return capsule;
    });
    
    if (result.success && result.data) {
      this.dbService['setCache'](cacheKey, result.data);
    }
    
    return result;
  }
  
  async findAll(offset: number = 0, limit: number = 50): Promise<DatabaseResult<CapsuleModel[]>> {
    return this.dbService.getCapsules();
  }
  
  async save(capsule: CapsuleModel): Promise<DatabaseResult<number>> {
    return this.dbService.createCapsule(capsule);
  }
  
  async update(id: number, updates: Partial<CapsuleModel>): Promise<DatabaseResult<void>> {
    const result = await this.dbService['withRetry'](async () => {
      await this.dbService.getOriginalService().updateCapsule(id, updates);
      this.clearCache(`capsule_${id}`);
    });
    
    if (result.success) {
      this.stateManager.updateState({}, 'CAPSULE_UPDATED');
    }
    
    return result;
  }
  
  async delete(id: number): Promise<DatabaseResult<void>> {
    const result = await this.dbService['withRetry'](async () => {
      await this.dbService.getOriginalService().deleteCapsule(id);
      this.clearCache(`capsule_${id}`);
    });
    
    if (result.success) {
      this.stateManager.updateState({}, 'CAPSULE_DELETED');
    }
    
    return result;
  }
  
  async search(query: string): Promise<DatabaseResult<CapsuleModel[]>> {
    const result = await this.dbService['withRetry'](async () => {
      const store = this.dbService['store'];
      if (!store) throw new Error('Database not initialized');
      
      const resultSet = await store.querySql(`
        SELECT * FROM ${this.tableName} 
        WHERE title LIKE ? OR content LIKE ?
        ORDER BY createTime DESC
      `, [`%${query}%`, `%${query}%`]);
      
      const capsules: CapsuleModel[] = [];
      
      if (resultSet.goToFirstRow()) {
        do {
          capsules.push(this.resultSetToCapsule(resultSet));
        } while (resultSet.goToNextRow());
      }
      
      resultSet.close();
      return capsules;
    });
    
    return result;
  }
  
  async count(): Promise<DatabaseResult<number>> {
    const result = await this.dbService['withRetry'](async () => {
      const store = this.dbService['store'];
      if (!store) throw new Error('Database not initialized');
      
      const pred = new (globalThis as any).relationalStore.RdbPredicates(this.tableName);
      const resultSet = await store.query(pred);
      const count = resultSet.getRowCount();
      resultSet.close();
      
      return count;
    });
    
    return result;
  }
  
  /**
   * 获取过期的时间胶囊
   */
  async getExpiredCapsules(): Promise<DatabaseResult<CapsuleModel[]>> {
    const result = await this.dbService['withRetry'](async () => {
      return await this.dbService.getOriginalService().getExpiredCapsules();
    });
    
    return {
      success: true,
      data: result,
      timestamp: new Date().toISOString()
    };
  }
  
  /**
   * 获取即将到期的时间胶囊
   */
  async getUpcomingCapsules(days: number = 7): Promise<DatabaseResult<CapsuleModel[]>> {
    const result = await this.dbService['withRetry'](async () => {
      return await this.dbService.getOriginalService().getUpcomingCapsules(days);
    });
    
    return {
      success: true,
      data: result,
      timestamp: new Date().toISOString()
    };
  }
  
  /**
   * 开启时间胶囊
   */
  async openCapsule(id: number): Promise<DatabaseResult<void>> {
    const result = await this.dbService['withRetry'](async () => {
      await this.update(id, { isOpened: 1 });
    });
    
    if (result.success) {
      this.stateManager.updateState({}, 'CAPSULE_UPDATED');
    }
    
    return result;
  }
  
  /**
   * 获取时间胶囊统计
   */
  async getCapsuleStats(): Promise<DatabaseResult<{
    total: number;
    opened: number;
    locked: number;
    expired: number;
  }>> {
    const result = await this.dbService['withRetry'](async () => {
      return await this.dbService.getOriginalService().getCapsuleStats();
    });
    
    return {
      success: true,
      data: result,
      timestamp: new Date().toISOString()
    };
  }
  
  private clearCache(pattern?: string): void {
    this.dbService['clearCache'](pattern);
  }
  
  private updateStateFromEvent(event: DatabaseEvent): void {
    switch (event.type) {
      case 'INSERT':
      case 'UPDATE':
      case 'DELETE':
        this.stateManager.updateState({}, 'CAPSULES_LOADED');
        break;
    }
  }
  
  private resultSetToCapsule(resultSet: any): CapsuleModel {
    return {
      id: resultSet.getLong(resultSet.getColumnIndex('id')),
      title: resultSet.getString(resultSet.getColumnIndex('title')),
      content: resultSet.getString(resultSet.getColumnIndex('content')),
      openDate: resultSet.getString(resultSet.getColumnIndex('openDate')),
      createTime: resultSet.getString(resultSet.getColumnIndex('createTime')),
      isOpened: resultSet.getLong(resultSet.getColumnIndex('isOpened')),
      isOpenTime: resultSet.getLong(resultSet.getColumnIndex('isOpenTime')) === 1
    };
  }
}

/**
 * 设置仓库实现
 */
export class SettingsRepository implements IRepository<SettingsModel>, DatabaseObserver {
  private dbService: EnhancedDatabaseService;
  private stateManager: StateManager;
  private readonly tableName = 'settings';
  
  constructor() {
    this.dbService = EnhancedDatabaseService.getInstance();
    this.stateManager = StateManager.getInstance();
    this.dbService.registerObserver('settings_repository', this);
  }
  
  onDatabaseEvent(event: DatabaseEvent): void {
    if (event.table === this.tableName) {
      this.clearCache();
      this.updateStateFromEvent(event);
    }
  }
  
  getObservedTables(): string[] {
    return [this.tableName];
  }
  
  getObservedEventTypes(): string[] {
    return ['UPDATE'];
  }
  
  async findById(id: number): Promise<DatabaseResult<SettingsModel | null>> {
    const result = await this.dbService.getSettings();
    
    if (result.success && result.data && result.data.id === id) {
      return result;
    }
    
    return {
      success: true,
      data: null,
      timestamp: new Date().toISOString()
    };
  }
  
  async findAll(offset: number = 0, limit: number = 1): Promise<DatabaseResult<SettingsModel[]>> {
    const result = await this.dbService.getSettings();
    
    if (result.success && result.data) {
      return {
        success: true,
        data: [result.data],
        timestamp: new Date().toISOString()
      };
    }
    
    return {
      success: true,
      data: [],
      timestamp: new Date().toISOString()
    };
  }
  
  async save(settings: SettingsModel): Promise<DatabaseResult<number>> {
    const result = await this.dbService['withRetry'](async () => {
      const store = this.dbService['store'];
      if (!store) throw new Error('Database not initialized');
      
      const values = {
        darkMode: settings.darkMode,
        reminderTime: settings.reminderTime,
        reminderEnabled: settings.reminderEnabled,
        syncEnabled: settings.syncEnabled,
        backupEnabled: settings.backupEnabled,
        fontSize: settings.fontSize
      };
      
      let id: number;
      if (settings.id) {
        const pred = new (globalThis as any).relationalStore.RdbPredicates(this.tableName);
        pred.equalTo('id', settings.id);
        await store.update(values, pred);
        id = settings.id;
      } else {
        id = await store.insert(this.tableName, values);
      }
      
      this.clearCache();
      return id;
    });
    
    if (result.success) {
      this.stateManager.updateState({ userSettings: await this.getSettings() }, 'USER_SETTINGS_CHANGED');
    }
    
    return result;
  }
  
  async update(id: number, updates: Partial<SettingsModel>): Promise<DatabaseResult<void>> {
    const result = await this.dbService.updateSettings(updates);
    
    if (result.success) {
      this.stateManager.updateState({ userSettings: await this.getSettings() }, 'USER_SETTINGS_CHANGED');
    }
    
    return result;
  }
  
  async delete(id: number): Promise<DatabaseResult<void>> {
    // 设置不应该被删除，所以这里不做任何操作
    return {
      success: true,
      timestamp: new Date().toISOString()
    };
  }
  
  async search(query: string): Promise<DatabaseResult<SettingsModel[]>> {
    // 设置不支持搜索
    return {
      success: true,
      data: [],
      timestamp: new Date().toISOString()
    };
  }
  
  async count(): Promise<DatabaseResult<number>> {
    const result = await this.dbService['withRetry'](async () => {
      const store = this.dbService['store'];
      if (!store) throw new Error('Database not initialized');
      
      const resultSet = await store.querySql('SELECT COUNT(*) as count FROM settings');
      let count = 0;
      
      if (resultSet.goToFirstRow()) {
        count = resultSet.getLong(resultSet.getColumnIndex('count'));
      }
      
      resultSet.close();
      return count;
    });
    
    return result;
  }
  
  /**
   * 获取设置
   */
  async getSettings(): Promise<SettingsModel | null> {
    const result = await this.dbService.getSettings();
    return result.success ? result.data : null;
  }
  
  /**
   * 获取设置项的值
   */
  async getSettingValue<T extends keyof SettingsModel>(key: T): Promise<DatabaseResult<SettingsModel[T]>> {
    const settings = await this.getSettings();
    
    if (settings && settings[key] !== undefined) {
      return {
        success: true,
        data: settings[key],
        timestamp: new Date().toISOString()
      };
    }
    
    return {
      success: false,
      error: new Error(`Setting ${key} not found`),
      timestamp: new Date().toISOString()
    };
  }
  
  /**
   * 更新单个设置项
   */
  async updateSettingValue<T extends keyof SettingsModel>(key: T, value: SettingsModel[T]): Promise<DatabaseResult<void>> {
    const updates = { [key]: value } as Partial<SettingsModel>;
    return this.update(1, updates);
  }
  
  /**
   * 重置设置到默认值
   */
  async resetToDefaults(): Promise<DatabaseResult<void>> {
    const defaultSettings: SettingsModel = {
      darkMode: 2,
      reminderTime: '21:00',
      reminderEnabled: 1,
      syncEnabled: 0,
      backupEnabled: 0,
      fontSize: 16
    };
    
    return this.update(1, defaultSettings);
  }
  
  /**
   * 导出设置
   */
  async exportSettings(): Promise<DatabaseResult<string>> {
    const settings = await this.getSettings();
    
    if (settings) {
      return {
        success: true,
        data: JSON.stringify(settings),
        timestamp: new Date().toISOString()
      };
    }
    
    return {
      success: false,
      error: new Error('No settings found'),
      timestamp: new Date().toISOString()
    };
  }
  
  /**
   * 导入设置
   */
  async importSettings(settingsJson: string): Promise<DatabaseResult<void>> {
    try {
      const settings = JSON.parse(settingsJson) as SettingsModel;
      return this.update(1, settings);
    } catch (error) {
      return {
        success: false,
        error: error as Error,
        timestamp: new Date().toISOString()
      };
    }
  }
  
  private clearCache(): void {
    this.dbService['clearCache']('settings');
  }
  
  private updateStateFromEvent(event: DatabaseEvent): void {
    if (event.type === 'UPDATE') {
      this.stateManager.updateState({}, 'USER_SETTINGS_CHANGED');
    }
  }
}

/**
 * 仓库工厂类
 */
export class RepositoryFactory {
  private static noteRepository: NoteRepository;
  private static capsuleRepository: CapsuleRepository;
  private static settingsRepository: SettingsRepository;
  
  static getNoteRepository(): NoteRepository {
    if (!RepositoryFactory.noteRepository) {
      RepositoryFactory.noteRepository = new NoteRepository();
    }
    return RepositoryFactory.noteRepository;
  }
  
  static getCapsuleRepository(): CapsuleRepository {
    if (!RepositoryFactory.capsuleRepository) {
      RepositoryFactory.capsuleRepository = new CapsuleRepository();
    }
    return RepositoryFactory.capsuleRepository;
  }
  
  static getSettingsRepository(): SettingsRepository {
    if (!RepositoryFactory.settingsRepository) {
      RepositoryFactory.settingsRepository = new SettingsRepository();
    }
    return RepositoryFactory.settingsRepository;
  }
  
  /**
   * 销毁所有仓库实例
   */
  static destroy(): void {
    if (RepositoryFactory.noteRepository) {
      RepositoryFactory.noteRepository = null!;
    }
    if (RepositoryFactory.capsuleRepository) {
      RepositoryFactory.capsuleRepository = null!;
    }
    if (RepositoryFactory.settingsRepository) {
      RepositoryFactory.settingsRepository = null!;
    }
  }
}