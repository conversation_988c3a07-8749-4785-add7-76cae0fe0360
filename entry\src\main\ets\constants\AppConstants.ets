import { MorandiColors } from './MorandiColors';

// 应用常量定义
export class AppConstants {
  // 应用信息
  static readonly APP_NAME = 'TimeNotes';
  static readonly VERSION = '1.0.0';
  
  // 页面路由
  static readonly ROUTES = {
    INDEX: 'pages/Index',
    TIMELINE: 'pages/Timeline',
    CALENDAR: 'pages/Calendar',
    STATS: 'pages/Stats',
    PROFILE: 'pages/Profile',
    SEARCH: 'pages/Search',
    CAPSULE: 'pages/Capsule',
    SETTINGS: 'pages/Settings'
  };
  
  // 数据库相关
  static readonly DATABASE_NAME = 'TimeNotes.db';
  static readonly TABLE_NOTES = 'notes';
  static readonly TABLE_CAPSULES = 'capsules';
  
  // 分类
  static readonly CATEGORIES = [
    { id: 1, name: '生活感悟', color: MorandiColors.categoryLife, icon: '❤️' },
    { id: 2, name: '工作', color: MorandiColors.categoryWork, icon: '💼' },
    { id: 3, name: '学习', color: MorandiColors.categoryStudy, icon: '📚' },
    { id: 4, name: '计划', color: MorandiColors.categoryPlan, icon: '📋' }
  ];
  
  // 时间格式
  static readonly DATE_FORMAT = 'YYYY-MM-DD';
  static readonly TIME_FORMAT = 'HH:mm';
  static readonly DATETIME_FORMAT = 'YYYY-MM-DD HH:mm';
  
  // 分页
  static readonly PAGE_SIZE = 20;
  
  // 时间胶囊设置
  static readonly CAPSULE_MIN_DAYS = 1;
  static readonly CAPSULE_MAX_DAYS = 3650; // 10年
}