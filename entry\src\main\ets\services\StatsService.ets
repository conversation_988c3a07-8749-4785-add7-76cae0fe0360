import { DatabaseService } from './DatabaseService';
import { DateUtils } from '../utils/DateUtils';
import { MorandiColors } from '../constants/MorandiColors';

// 统计数据模型
export interface StatsOverview {
  totalNotes: number;
  consecutiveDays: number;
  categoryCount: number;
  completionRate: number;
}

export interface MonthlyTrend {
  date: string;
  count: number;
}

export interface CategoryStats {
  categoryId: number;
  categoryName: string;
  count: number;
  color: string;
  percentage: number;
}

export interface DailyStats {
  date: string;
  noteCount: number;
  wordCount: number;
}

/**
 * 统计服务类
 */
export class StatsService {
  private static instance: StatsService;
  private db: DatabaseService;
  
  private constructor() {
    this.db = DatabaseService.getInstance();
  }
  
  static getInstance(): StatsService {
    if (!StatsService.instance) {
      StatsService.instance = new StatsService();
    }
    return StatsService.instance;
  }
  
  /**
   * 获取统计概览
   */
  async getOverview(): Promise<StatsOverview> {
    try {
      const [totalNotes, consecutiveDays, categoryStats] = await Promise.all([
        this.getTotalNotes(),
        this.getConsecutiveDays(),
        this.getCategoryStats()
      ]);
      
      // 计算完成率（基于有记录的天数）
      const completionRate = this.calculateCompletionRate();
      
      return {
        totalNotes,
        consecutiveDays,
        categoryCount: categoryStats.length,
        completionRate
      };
    } catch (error) {
      console.error('Failed to get overview:', error);
      return {
        totalNotes: 0,
        consecutiveDays: 0,
        categoryCount: 0,
        completionRate: 0
      };
    }
  }
  
  /**
   * 获取总笔记数
   */
  private async getTotalNotes(): Promise<number> {
    const notes = await this.db.getNotes();
    return notes.length;
  }
  
  /**
   * 获取连续记录天数
   */
  private async getConsecutiveDays(): Promise<number> {
    try {
      const noteDates = await this.db.getNoteDates();
      if (noteDates.length === 0) return 0;
      
      const today = DateUtils.today();
      let consecutiveCount = 0;
      let currentDate = new Date(today);
      
      for (let i = 0; i < 365; i++) { // 最多检查365天
        const dateStr = DateUtils.formatDate(currentDate, 'yyyy-MM-dd');
        if (noteDates.includes(dateStr)) {
          consecutiveCount++;
          currentDate.setDate(currentDate.getDate() - 1);
        } else {
          break;
        }
      }
      
      return consecutiveCount;
    } catch (error) {
      console.error('Failed to get consecutive days:', error);
      return 0;
    }
  }
  
  /**
   * 计算完成率（本月有记录的天数/本月天数）
   */
  private calculateCompletionRate(): number {
    const today = new Date();
    const year = today.getFullYear();
    const month = today.getMonth();
    const daysInMonth = new Date(year, month + 1, 0).getDate();
    const currentDay = today.getDate();
    
    // 简化的完成率计算
    return Math.round((currentDay / daysInMonth) * 100);
  }
  
  /**
   * 获取分类统计
   */
  async getCategoryStats(): Promise<CategoryStats[]> {
    try {
      const notes = await this.db.getNotes();
      const categoryMap = new Map<number, number>();
      
      // 统计每个分类的笔记数
      notes.forEach(note => {
        const count = categoryMap.get(note.categoryId) || 0;
        categoryMap.set(note.categoryId, count + 1);
      });
      
      const categories = [
        { id: 1, name: '工作', color: MorandiColors.categoryWork },
        { id: 2, name: '生活', color: MorandiColors.categoryLife },
        { id: 3, name: '学习', color: MorandiColors.categoryStudy },
        { id: 4, name: '计划', color: MorandiColors.categoryPlan }
      ];
      
      const stats: CategoryStats[] = [];
      const totalNotes = notes.length;
      
      categories.forEach(category => {
        const count = categoryMap.get(category.id) || 0;
        const percentage = totalNotes > 0 ? Math.round((count / totalNotes) * 100) : 0;
        
        stats.push({
          categoryId: category.id,
          categoryName: category.name,
          count,
          color: category.color,
          percentage
        });
      });
      
      return stats.sort((a, b) => b.count - a.count);
    } catch (error) {
      console.error('Failed to get category stats:', error);
      return [];
    }
  }
  
  /**
   * 获取本月记录趋势
   */
  async getMonthlyTrend(): Promise<MonthlyTrend[]> {
    try {
      const today = new Date();
      const year = today.getFullYear();
      const month = today.getMonth() + 1;
      
      const monthlyStats = await this.db.getMonthlyNoteStats(year, month);
      
      // 补充没有记录的日期
      const daysInMonth = new Date(year, month, 0).getDate();
      const trendMap = new Map<string, number>();
      
      monthlyStats.forEach(stat => {
        trendMap.set(stat.date, stat.count);
      });
      
      const result: MonthlyTrend[] = [];
      for (let day = 1; day <= daysInMonth; day++) {
        const dateStr = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
        const count = trendMap.get(dateStr) || 0;
        result.push({ date: dateStr, count });
      }
      
      return result;
    } catch (error) {
      console.error('Failed to get monthly trend:', error);
      return [];
    }
  }
  
  /**
   * 获取每日统计
   */
  async getDailyStats(startDate: string, endDate: string): Promise<DailyStats[]> {
    try {
      const allNotes = await this.db.getNotes();
      const dailyMap = new Map<string, { noteCount: number; wordCount: number }>();
      
      allNotes.forEach(note => {
        const date = note.createTime.substring(0, 10);
        if (date >= startDate && date <= endDate) {
          const existing = dailyMap.get(date) || { noteCount: 0, wordCount: 0 };
          const wordCount = note.content.length;
          
          dailyMap.set(date, {
            noteCount: existing.noteCount + 1,
            wordCount: existing.wordCount + wordCount
          });
        }
      });
      
      const result: DailyStats[] = [];
      const start = new Date(startDate);
      const end = new Date(endDate);
      
      for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
        const dateStr = DateUtils.formatDate(d, 'yyyy-MM-dd');
        const stats = dailyMap.get(dateStr) || { noteCount: 0, wordCount: 0 };
        
        result.push({
          date: dateStr,
          noteCount: stats.noteCount,
          wordCount: stats.wordCount
        });
      }
      
      return result;
    } catch (error) {
      console.error('Failed to get daily stats:', error);
      return [];
    }
  }
  
  /**
   * 获取最活跃的时段
   */
  async getActiveHours(): Promise<{ hour: number; count: number }[]> {
    try {
      const notes = await this.db.getNotes();
      const hourMap = new Map<number, number>();
      
      // 初始化24小时
      for (let i = 0; i < 24; i++) {
        hourMap.set(i, 0);
      }
      
      notes.forEach(note => {
        const hour = parseInt(note.createTime.substring(11, 13));
        const count = hourMap.get(hour) || 0;
        hourMap.set(hour, count + 1);
      });
      
      const result: { hour: number; count: number }[] = [];
      hourMap.forEach((count, hour) => {
        result.push({ hour, count });
      });
      
      return result.sort((a, b) => b.count - a.count);
    } catch (error) {
      console.error('Failed to get active hours:', error);
      return [];
    }
  }
  
  /**
   * 获取平均字数
   */
  async getAverageWordCount(): Promise<number> {
    try {
      const notes = await this.db.getNotes();
      if (notes.length === 0) return 0;
      
      const totalWords = notes.reduce((sum, note) => sum + note.content.length, 0);
      return Math.round(totalWords / notes.length);
    } catch (error) {
      console.error('Failed to get average word count:', error);
      return 0;
    }
  }
  
  /**
   * 获取最受欢迎的标签
   */
  async getPopularTags(limit: number = 10): Promise<{ tag: string; count: number }[]> {
    try {
      const notes = await this.db.getNotes();
      const tagMap = new Map<string, number>();
      
      notes.forEach(note => {
        if (note.tags) {
          note.tags.split(',').forEach(tag => {
            const trimmedTag = tag.trim();
            if (trimmedTag) {
              const count = tagMap.get(trimmedTag) || 0;
              tagMap.set(trimmedTag, count + 1);
            }
          });
        }
      });
      
      const result: { tag: string; count: number }[] = [];
      tagMap.forEach((count, tag) => {
        result.push({ tag, count });
      });
      
      return result.sort((a, b) => b.count - a.count).slice(0, limit);
    } catch (error) {
      console.error('Failed to get popular tags:', error);
      return [];
    }
  }
}