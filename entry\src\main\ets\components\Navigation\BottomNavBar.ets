import { MorandiColors } from '../../constants/MorandiColors';

export interface NavItem {
  page: string;
  icon: string;
  label: string;
  activeIcon?: string;
}

@Component
export struct BottomNavBar {
  @Prop items: NavItem[] = [];
  @Prop currentPage: string = '';
  onPageChange: (page: string) => void = () => {};
  
  build() {
    Row() {
      ForEach(this.items, (item: NavItem) => {
        Column() {
          // 图标（用文本代替）
          Text(this.currentPage === item.page ? (item.activeIcon || item.icon) : item.icon)
            .fontSize(24)
            .fontColor(this.currentPage === item.page ? MorandiColors.accent : MorandiColors.textHint);
          
          Text(item.label)
            .fontSize(10)
            .fontColor(this.currentPage === item.page ? MorandiColors.accent : MorandiColors.textHint)
            .margin({ top: 4 });
        }
        .layoutWeight(1)
        .justifyContent(FlexAlign.Center)
        .onClick(() => {
          this.onPageChange(item.page);
        });
      });
    }
    .width('100%')
    .height(80)
    .backgroundColor(MorandiColors.background)
    .border({ width: { top: 1 }, color: MorandiColors.border })
    .padding({ bottom: 20 });
  }
}