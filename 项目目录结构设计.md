# TimeNotes项目目录结构设计

## 应用根目录结构
```
TimeNotes/
├── entry/                     # 主模块入口
│   ├── src/main/            
│   │   ├── ets/              # ArkTS源码
│   │   │   ├── entryability/ # 应用入口
│   │   │   ├── pages/        # 页面
│   │   │   ├── components/   # 自定义组件
│   │   │   ├── model/        # 数据模型
│   │   │   ├── viewmodel/    # 视图模型
│   │   │   ├── services/     # 业务服务
│   │   │   ├── utils/        # 工具类
│   │   │   └── constants/    # 常量定义
│   │   ├── resources/        # 资源文件
│   │   └── module.json5      # 模块配置
│   └── build-profile.json5   # 构建配置
├── features/                 # 功能模块（动态特性）
│   ├── dataSync/            # 数据同步特性
│   └── aiAssistant/         # AI助手特性
├── shared/                   # 共享模块
│   ├── ui/                  # 共享UI组件（引用SharedUI）
│   ├── database/            # 数据库操作
│   └── network/             # 网络请求
├── hvigor/                  # 构建脚本
├── ohosTest/                # 测试目录
└── README.md               # 项目说明
```

## 详细模块说明

### 1. entry/src/main/ets/ - 源码目录

#### entryability/ - 应用入口
```
entryability/
├── EntryAbility.ets         # 应用主入口
├── MyApplication.ets        # 应用全局状态
└── lifecycle/               # 生命周期管理
    └── AppLifecycle.ets     # 应用生命周期回调
```

#### pages/ - 页面文件
```
pages/
├── Index.ets               # 主页面（时光流）
├── NoteEdit.ets            # 笔记编辑页
├── CategoryManager.ets     # 分类管理页
├── Search.ets              # 搜索结果页
├── TimeCapsule.ets         # 时光胶囊页
├── Settings.ets            # 设置页
└── About.ets               # 关于页
```

#### components/ - 自定义组件
```
components/
├── NoteCard/               # 笔记卡片组件
│   ├── NoteCard.ets
│   └── NoteCardType.ets
├── CategoryBar/           # 分类栏组件
│   ├── CategoryBar.ets
│   └── CategoryItem.ets
├── TimeLine/              # 时间轴组件
│   ├── TimeLine.ets
│   └── TimeLineItem.ets
├── RichEditor/            # 富文本编辑器
│   ├── RichEditor.ets
│   └── EditorToolbar.ets
└── MoodSelector/          # 心情选择器
    ├── MoodSelector.ets
    └── MoodGrid.ets
```

#### model/ - 数据模型
```
model/
├── NoteModel.ets          # 笔记数据模型
├── CategoryModel.ets      # 分类数据模型
├── MoodModel.ets          # 心情数据模型
├── SyncLogModel.ets       # 同步日志模型
├── UserModel.ets          # 用户模型
└── BaseModel.ets         # 基础模型类
```

#### viewmodel/ - 视图模型
```
viewmodel/
├── NoteViewModel.ets      # 笔记视图模型
├── CategoryViewModel.ets  # 分类视图模型
├── SearchViewModel.ets    # 搜索视图模型
├── SettingsViewModel.ets  # 设置视图模型
└── BaseViewModel.ets      # 基础视图模型
```

#### services/ - 业务服务
```
services/
├── NoteService.ets        # 笔记业务服务
├── CategoryService.ets    # 分类业务服务
├── SyncService.ets        # 同步服务
├── AiService.ets          # AI服务（预留）
├── BackupService.ets      # 备份服务
└── ServiceProvider.ets    # 服务提供者
```

#### utils/ - 工具类
```
utils/
├── DateUtil.ets           # 日期工具
├── EncryptUtil.ets        # 加密工具
├── StorageUtil.ets        # 存储工具
├── LogUtil.ets            # 日志工具
├── Validator.ets          # 验证工具
└── CommonUtil.ets         # 通用工具
```

#### constants/ - 常量定义
```
constants/
├── AppConstants.ets       # 应用常量
├── DbConstants.ets        # 数据库常量
├── SyncConstants.ets      # 同步常量
└── UIConstants.ets        # UI常量
```

### 2. features/ - 功能模块（动态特性）

#### dataSync/ - 数据同步特性
```
dataSync/
├── src/main/
│   ├── ets/
│   │   ├── distributor/    # 分布式管理
│   │   ├── sync/          # 同步逻辑
│   │   └── cloud/         # 云同步
│   └── module.json5
└── build-profile.json5
```

### 3. shared/ - 共享模块

#### database/ - 数据库操作
```
database/
├── src/main/
│   ├── ets/
│   │   ├── dao/           # 数据访问对象
│   │   │   ├── NoteDao.ets
│   │   │   ├── CategoryDao.ets
│   │   │   └── SyncLogDao.ets
│   │   ├── entity/        # 数据库实体
│   │   ├── helper/        # 数据库助手
│   │   └── migration/     # 数据库迁移
│   └── resources/
└── build-profile.json5
```

#### network/ - 网络请求
```
network/
├── src/main/
│   ├── ets/
│   │   ├── http/          # HTTP请求
│   │   ├── interceptor/   # 拦截器
│   │   └── observer/      # 响应观察者
│   └── module.json5
└── build-profile.json5
```

## 核心架构模式

### 1. 分层架构
```
Presentation Layer (UI + Components)
           ↓
ViewModel Layer (State Management)
           ↓
Service Layer (Business Logic)
           ↓
Data Layer (Database + Network)
```

### 2. 模块化设计
- **entry模块**：应用主入口，包含核心UI和业务逻辑
- **features模块**：可选功能模块，支持动态加载
- **shared模块**：共享功能，供其他模块使用

### 3. 数据流设计
- **单向数据流**：UI → ViewModel → Service → Data
- **状态管理**：使用AbilityStage管理应用全局状态
- **响应式UI**：使用@State和@Prop实现数据绑定

## 开发规范

### 1. 命名规范
- 文件名：PascalCase（如：NoteEdit.ets）
- 变量名：camelCase（如：noteTitle）
- 常量名：UPPER_SNAKE_CASE（如：MAX_NOTE_LENGTH）
- 类名：PascalCase（如：NoteService）

### 2. 目录结构约定
- 每个功能模块独立的目录
- 组件和样式文件放在一起
- 测试文件与源码文件同级目录

### 3. 模块依赖原则
- entry模块依赖shared和features模块
- features模块之间不能相互依赖
- shared模块不能依赖其他模块