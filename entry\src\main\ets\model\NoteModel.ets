// 笔记数据模型
export interface NoteModel {
  id?: number; // 主键
  title: string; // 标题
  content: string; // 内容
  categoryId: number; // 分类ID
  tags: string; // 标签，用逗号分隔
  mood: string; // 心情：happy, calm, sad, angry, excited, tired
  weather: string; // 天气：sunny, cloudy, rainy, snowy, windy
  location: string; // 位置信息
  images: string; // 图片路径，用逗号分隔
  voiceNote: string; // 语音笔记路径
  createTime: string; // 创建时间
  updateTime: string; // 更新时间
  isDeleted: number; // 是否删除 0/1
}

// 时间胶囊数据模型
export interface CapsuleModel {
  id?: number;
  title: string;
  content: string;
  openDate: string; // 开启日期
  createTime: string;
  isOpened: number; // 是否已开启 0/1
  isOpenTime: boolean; // 是否定时开启
}

// 分类统计模型
export interface CategoryStatsModel {
  categoryId: number;
  categoryName: string;
  count: number;
  color: string;
}

// 每日统计模型
export interface DailyStatsModel {
  date: string;
  noteCount: number;
  wordCount: number;
}

// 应用设置模型
export interface SettingsModel {
  id?: number;
  darkMode: number; // 0 关闭 1 开启 2 跟随系统
  reminderTime: string; // 提醒时间
  reminderEnabled: number; // 0 关闭 1 开启
  syncEnabled: number; // 0 关闭 1 开启
  backupEnabled: number; // 0 关闭 1 开启
  fontSize: number; // 字体大小
}