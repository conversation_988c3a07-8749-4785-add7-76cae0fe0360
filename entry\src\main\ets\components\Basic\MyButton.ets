import { MorandiColors } from '../../constants/MorandiColors';

@Component
export struct MyButton {
  @Prop text: string = '';
  @Prop type: 'primary' | 'secondary' | 'text' = 'primary';
  @Prop disabled: boolean = false;
  @Prop loading: boolean = false;
  onClick?: () => void;
  
  build() {
    Button() {
      if (this.loading) {
        LoadingProgress()
          .width(20)
          .height(20)
          .color(this.type === 'primary' ? MorandiColors.background : MorandiColors.primary);
      } else {
        Text(this.text)
          .fontSize(16)
          .fontWeight(500);
      }
    }
    .width('100%')
    .height(48)
    .backgroundColor(this.getBackgroundColor())
    .fontColor(this.getFontColor())
    .borderRadius(24)
    .border({ width: this.type === 'secondary' ? 1 : 0, color: MorandiColors.border })
    .opacity(this.disabled ? 0.6 : 1)
    .enabled(!this.disabled && !this.loading)
    .onClick(() => {
      if (!this.disabled && !this.loading && this.onClick) {
        this.onClick();
      }
    });
  }
  
  private getBackgroundColor(): ResourceColor {
    if (this.type === 'primary') {
      return MorandiColors.accent;
    } else if (this.type === 'secondary') {
      return MorandiColors.background;
    } else {
      return Color.Transparent;
    }
  }
  
  private getFontColor(): ResourceColor {
    if (this.type === 'primary') {
      return MorandiColors.background;
    } else if (this.type === 'secondary') {
      return MorandiColors.textPrimary;
    } else {
      return MorandiColors.accent;
    }
  }
}