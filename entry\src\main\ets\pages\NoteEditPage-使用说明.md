# NoteEditPage 使用说明

## 功能概述

NoteEditPage 是TimeNotes应用的笔记编辑页面，提供了完整的笔记编辑功能，包括富文本编辑、心情选择、天气标记、分类管理等功能。

## 主要功能

### 1. 顶部栏功能
- **返回按钮**：点击返回上一页，如果有未保存的更改会自动保存
- **标题输入框**：用于输入笔记标题，支持实时编辑
- **保存按钮**：手动保存当前笔记，显示保存状态

### 2. 工具栏功能

#### 第一行工具栏
- **心情选择**：选择当前心情（开心、平静、悲伤、愤怒、兴奋、疲惫）
- **天气选择**：选择当前天气（晴天、多云、雨天、雪天、大风）
- **分类选择**：选择笔记分类（生活、工作、学习、计划）
- **语音输入**：语音转文字功能（待实现）
- **位置标记**：记录当前位置信息（待实现）

#### 第二行工具栏
- **格式化工具**：
  - 加粗（B）
  - 斜体（I）
  - 列表（☰）
- **插入图片**：插入图片功能（待实现）
- **插入视频**：插入视频功能（待实现）

### 3. 编辑区功能
- **富文本编辑器**：支持多行文本输入
- **标签输入**：添加标签，用逗号分隔
- **字数统计**：实时显示当前字数
- **自动保存提示**：显示最后自动保存时间

### 4. 底部栏功能
- **创建时间**：显示笔记创建时间
- **心情图标**：显示当前选择的心情
- **天气图标**：显示当前选择的天气
- **同步状态**：显示同步状态图标

## 自动保存功能

- **自动保存间隔**：每30秒自动保存一次
- **触发条件**：只有当内容发生变化时才会自动保存
- **保存提示**：在编辑区底部显示最后自动保存时间
- **返回时保存**：返回上一页时如有未保存的更改会自动保存

## 数据模型扩展

### 新增字段
- `mood`: 心情状态（happy, calm, sad, angry, excited, tired）
- `weather`: 天气状态（sunny, cloudy, rainy, snowy, windy）
- `location`: 位置信息
- `images`: 图片路径（逗号分隔）
- `voiceNote`: 语音笔记路径

### 数据库更新
- 数据库表结构已更新以支持新字段
- 兼容现有数据，新字段默认为空字符串

## 响应式设计

### 布局优化
- 使用 Flex 布局替代固定 Row 布局
- 支持自动换行和空间分配
- 适配不同屏幕尺寸

### 交互优化
- 弹出选择器使用 Flex 布局
- 文本溢出处理
- 触摸区域优化

## 使用方法

### 创建新笔记
1. 从主页点击"新建笔记"按钮
2. 输入笔记标题
3. 选择心情、天气和分类
4. 编辑笔记内容
5. 添加标签
6. 系统会自动保存或手动点击保存

### 编辑现有笔记
1. 从笔记列表点击要编辑的笔记
2. 修改标题、内容等信息
3. 更新心情、天气等状态
4. 系统会自动保存更改

### 心情选择
1. 点击工具栏中的心情选择器
2. 从弹出的心情列表中选择合适的心情
3. 心情图标会立即更新

### 天气选择
1. 点击工具栏中的天气选择器
2. 从弹出的天气列表中选择当前天气
3. 天气图标会立即更新

## 注意事项

1. **自动保存**：系统每30秒自动保存一次，但建议重要内容手动保存
2. **数据同步**：同步状态图标显示当前同步状态
3. **标签格式**：多个标签用逗号分隔
4. **标题要求**：标题不能为空，如果为空会自动设置为"无标题"

## 待实现功能

1. **富文本格式化**：加粗、斜体、列表等功能
2. **媒体插入**：图片和视频插入功能
3. **语音输入**：语音转文字功能
4. **位置服务**：获取当前位置信息
5. **数据同步**：云端数据同步功能

## 文件结构

```
TimeNotes/entry/src/main/ets/pages/
├── NoteEditPage.ets              # 笔记编辑页面
├── NoteDetailPage.ets             # 笔记详情页面
└── ...                           # 其他页面

TimeNotes/entry/src/main/ets/
├── model/NoteModel.ets            # 笔记数据模型
├── services/DatabaseService.ets    # 数据库服务
├── utils/DateUtils.ets            # 日期工具类
└── constants/MorandiColors.ets    # 莫兰迪色系
```

## 样式设计

### 莫兰迪色系
- 使用柔和的莫兰迪色调
- 统一的颜色管理
- 舒适的视觉体验

### 交互反馈
- 按钮点击反馈
- 状态变化提示
- 保存状态显示

### 布局层次
- 清晰的功能分区
- 合理的空间分配
- 良好的信息层级