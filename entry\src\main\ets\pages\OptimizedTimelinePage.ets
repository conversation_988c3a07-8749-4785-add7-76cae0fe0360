// 优化后的时间轴页面 - 使用虚拟化列表
import { DatabaseService } from '../services/DatabaseService';
import { NoteModel } from '../model/NoteModel';
import { DateUtils } from '../utils/DateUtils';
import { MorandiColors } from '../constants/MorandiColors';
import { MyCard } from '../components/Basic/MyCard';
import { VirtualList, VirtualListItem } from '../components/Performance/VirtualList';
import { RouterUtil } from '../utils/RouterUtil';
import { hilog } from '@kit.PerformanceAnalysisKit';

@Component
export struct OptimizedTimelinePage {
  @State notes: NoteModel[] = [];
  @State isLoading: boolean = true;
  @State searchKeyword: string = '';
  @State currentPage: number = 1;
  @State hasMore: boolean = true;
  @State isRefreshing: boolean = false;
  @State screenWidth: number = 0;
  @State isTablet: boolean = false;
  @State paddingSize: number = 20;
  @State virtualListItems: VirtualListItem[] = [];
  @State isVirtualListEnabled: boolean = true;
  
  private databaseService = DatabaseService.getInstance();
  private context = getContext(this) as Context;
  private performanceMonitor: PerformanceMonitor = new PerformanceMonitor();

  aboutToAppear() {
    this.initDatabase();
    this.loadNotes();
    this.startPerformanceMonitoring();
  }

  aboutToDisappear() {
    this.stopPerformanceMonitoring();
  }

  /**
   * 初始化数据库
   */
  private async initDatabase() {
    try {
      await this.performanceMonitor.measure('databaseInit', async () => {
        await this.databaseService.init(this.context);
      });
    } catch (error) {
      console.error('数据库初始化失败:', error);
    }
  }

  /**
   * 开始性能监控
   */
  private startPerformanceMonitoring() {
    this.performanceMonitor.start();
    hilog.info(0x0000, 'Performance', '开始性能监控');
  }

  /**
   * 停止性能监控
   */
  private stopPerformanceMonitoring() {
    const report = this.performanceMonitor.stop();
    hilog.info(0x0000, 'Performance', `性能监控报告: ${JSON.stringify(report)}`);
  }

  /**
   * 加载笔记数据
   */
  private async loadNotes(refresh: boolean = false) {
    if (refresh) {
      this.currentPage = 1;
      this.notes = [];
      this.hasMore = true;
      this.virtualListItems = [];
    }

    if (!this.hasMore && !refresh) return;

    try {
      const startTime = Date.now();
      
      const [newNotes, totalCount] = await Promise.all([
        this.databaseService.getNotes((this.currentPage - 1) * 20, 20),
        this.databaseService.getNoteCount()
      ]);
      
      const loadTime = Date.now() - startTime;
      hilog.info(0x0000, 'Performance', `加载 ${newNotes.length} 条笔记耗时: ${loadTime}ms`);
      
      if (refresh) {
        this.notes = newNotes;
      } else {
        this.notes = [...this.notes, ...newNotes];
      }
      
      // 更新虚拟列表项
      this.updateVirtualListItems();
      
      this.hasMore = this.notes.length < totalCount;
      this.currentPage++;
      
      // 性能监控
      this.performanceMonitor.recordMetric('loadNotes', {
        count: newNotes.length,
        duration: loadTime,
        page: this.currentPage - 1
      });
      
    } catch (error) {
      console.error('加载笔记失败:', error);
    } finally {
      this.isLoading = false;
      this.isRefreshing = false;
    }
  }

  /**
   * 更新虚拟列表项
   */
  private updateVirtualListItems() {
    this.virtualListItems = this.notes.map((note, index) => ({
      id: note.id || index,
      height: this.estimateNoteHeight(note),
      data: note
    }));
  }

  /**
   * 估算笔记高度
   */
  private estimateNoteHeight(note: NoteModel): number {
    let height = 80; // 基础高度
    
    // 内容高度
    if (note.content && note.content.length > 0) {
      const contentLines = Math.ceil(note.content.length / 30); // 假设每行30字符
      height += contentLines * 24; // 每行24px
    }
    
    // 标签高度
    if (note.tags && note.tags.trim()) {
      height += 32; // 标签区域高度
    }
    
    // 时间信息高度
    height += 24;
    
    // 间距
    height += 16;
    
    return Math.min(height, 300); // 最大高度限制
  }

  /**
   * 搜索笔记
   */
  private async searchNotes() {
    if (!this.searchKeyword.trim()) {
      this.loadNotes(true);
      return;
    }

    try {
      this.isLoading = true;
      const startTime = Date.now();
      
      const searchResults = await this.databaseService.searchNotes(this.searchKeyword);
      
      const searchTime = Date.now() - startTime;
      hilog.info(0x0000, 'Performance', `搜索 "${this.searchKeyword}" 耗时: ${searchTime}ms, 结果: ${searchResults.length} 条`);
      
      this.notes = searchResults;
      this.updateVirtualListItems();
      
      // 性能监控
      this.performanceMonitor.recordMetric('searchNotes', {
        keyword: this.searchKeyword,
        duration: searchTime,
        resultCount: searchResults.length
      });
      
    } catch (error) {
      console.error('搜索笔记失败:', error);
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * 下拉刷新
   */
  private onRefresh() {
    this.isRefreshing = true;
    this.loadNotes(true);
  }

  /**
   * 创建新笔记
   */
  private createNewNote() {
    RouterUtil.push('pages/NoteEditPage');
  }

  /**
   * 点击笔记卡片
   */
  private onNoteClick(note: NoteModel) {
    RouterUtil.push('pages/NoteDetailPage', { noteId: note.id });
  }

  /**
   * 加载更多数据
   */
  private loadMore() {
    if (!this.isLoading && this.hasMore) {
      this.loadNotes();
    }
  }

  /**
   * 切换虚拟列表模式
   */
  private toggleVirtualListMode() {
    this.isVirtualListEnabled = !this.isVirtualListEnabled;
    hilog.info(0x0000, 'Performance', `虚拟列表模式: ${this.isVirtualListEnabled ? '启用' : '禁用'}`);
  }

  /**
   * 根据屏幕尺寸调整布局
   */
  private adjustLayoutForScreenSize(width: number) {
    this.screenWidth = width;
    this.isTablet = width >= 600;
    this.paddingSize = this.isTablet ? 32 : 20;
  }

  build() {
    Column() {
      // 顶部栏
      this.HeaderBar();
      
      // 内容区域
      if (this.isLoading && this.notes.length === 0) {
        this.LoadingView();
      } else if (this.notes.length === 0) {
        this.EmptyView();
      } else {
        this.NotesList();
      }
      
      // 悬浮按钮
      this.FloatingButton();
      
      // 性能调试信息
      if (this.performanceMonitor.isDebugEnabled()) {
        this.PerformanceDebugView();
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor(MorandiColors.background)
    .onSizeChange((oldSize: Size, newSize: Size) => {
      this.adjustLayoutForScreenSize(newSize.width);
    });
  }

  /**
   * 顶部栏组件
   */
  @Builder HeaderBar() {
    Column() {
      // 应用标题和日期
      Row() {
        Column() {
          Text('TimeNotes')
            .fontSize(this.isTablet ? 28 : 24)
            .fontWeight(600)
            .fontColor(MorandiColors.textPrimary);
          
          Text(`${DateUtils.formatDate(new Date(), 'YYYY年MM月DD日')} ${DateUtils.getWeekday(DateUtils.today())}`)
            .fontSize(this.isTablet ? 16 : 14)
            .fontColor(MorandiColors.textTertiary)
            .margin({ top: 4 });
        }
        .alignItems(HorizontalAlign.Start);
        
        Blank();
        
        // 性能调试开关
        Row() {
          Text(this.isVirtualListEnabled ? '虚拟' : '普通')
            .fontSize(12)
            .fontColor(MorandiColors.textTertiary);
          
          Button() {
            Text('切换')
              .fontSize(12)
              .fontColor(Color.White);
          }
          .width(50)
          .height(24)
          .backgroundColor(MorandiColors.primary)
          .borderRadius(12)
          .onClick(() => {
            this.toggleVirtualListMode();
          });
        }
        .margin({ right: 8 });
        
        // 搜索按钮
        Row() {
          Image($r('app.media.ic_search'))
            .width(this.isTablet ? 24 : 20)
            .height(this.isTablet ? 24 : 20)
            .fillColor(MorandiColors.textTertiary);
        }
        .width(this.isTablet ? 48 : 40)
        .height(this.isTablet ? 48 : 40)
        .borderRadius(this.isTablet ? 24 : 20)
        .backgroundColor(MorandiColors.cardBackground)
        .justifyContent(FlexAlign.Center)
        .onClick(() => {
          // 显示搜索框
          console.log('显示搜索');
        });
      }
      .width('100%')
      .padding({ 
        left: this.paddingSize, 
        right: this.paddingSize, 
        top: this.isTablet ? 16 : 12, 
        bottom: this.isTablet ? 16 : 12 
      });
      
      // 搜索框（可扩展为显示/隐藏）
      if (this.searchKeyword.length > 0) {
        Row() {
          TextInput({ placeholder: '搜索笔记...' })
            .width('100%')
            .height(this.isTablet ? 48 : 40)
            .backgroundColor(MorandiColors.cardBackground)
            .borderRadius(this.isTablet ? 24 : 20)
            .padding({ left: 16, right: 16 })
            .fontColor(MorandiColors.textPrimary)
            .placeholderColor(MorandiColors.textHint)
            .onChange((value: string) => {
              this.searchKeyword = value;
            })
            .onSubmit(() => {
              this.searchNotes();
            });
        }
        .width('100%')
        .padding({ 
          left: this.paddingSize, 
          right: this.paddingSize, 
          bottom: this.isTablet ? 16 : 12 
        });
      }
    }
    .width('100%')
    .backgroundColor(MorandiColors.background);
  }

  /**
   * 加载视图
   */
  @Builder LoadingView() {
    Column() {
      LoadingProgress()
        .width(40)
        .height(40)
        .color(MorandiColors.primary);
      
      Text('正在加载笔记...')
        .fontSize(16)
        .fontColor(MorandiColors.textTertiary)
        .margin({ top: 16 });
    }
    .width('100%')
    .height('100%')
    .justifyContent(FlexAlign.Center);
  }

  /**
   * 空视图
   */
  @Builder EmptyView() {
    Column() {
      Image($r('app.media.ic_empty'))
        .width(120)
        .height(120)
        .fillColor(MorandiColors.textTertiary)
        .opacity(0.3);
      
      Text('还没有笔记')
        .fontSize(18)
        .fontColor(MorandiColors.textTertiary)
        .margin({ top: 24 });
      
      Text('点击右下角按钮开始记录你的时光')
        .fontSize(14)
        .fontColor(MorandiColors.textHint)
        .margin({ top: 8 });
    }
    .width('100%')
    .height('100%')
    .justifyContent(FlexAlign.Center);
  }

  /**
   * 笔记列表
   */
  @Builder NotesList() {
    if (this.isVirtualListEnabled) {
      // 使用虚拟化列表
      VirtualList({
        items: this.virtualListItems,
        config: {
          itemHeight: 120,
          bufferSize: 5,
          overscanCount: 3,
          enableDynamicHeight: true,
          estimatedItemHeight: 120
        }
      }) {
        @Builder itemRenderer(item: VirtualListItem, index: number) {
          this.NoteCard(item.data);
        }
      }
      .layoutWeight(1)
      .setOnEndReached(() => {
        this.loadMore();
      });
    } else {
      // 使用普通列表
      Refresh({ refreshing: $$this.isRefreshing, friction: 100 }) {
        List() {
          ForEach(this.notes, (note: NoteModel, index: number) => {
            ListItem() {
              this.NoteCard(note);
            }
            .padding({ 
              left: this.paddingSize, 
              right: this.paddingSize, 
              top: this.isTablet ? 12 : 8, 
              bottom: this.isTablet ? 12 : 8 
            });
          });
        }
        .width('100%')
        .layoutWeight(1)
        .listDirection(Axis.Vertical)
        .edgeEffect(EdgeEffect.Spring)
        .onReachEnd(() => {
          if (!this.isLoading && this.hasMore) {
            this.loadMore();
          }
        });
      }
      .onRefreshing(() => {
        this.onRefresh();
      });
    }
  }

  /**
   * 笔记卡片
   */
  @Builder NoteCard(note: NoteModel) {
    MyCard({
      title: note.title,
      subtitle: DateUtils.getFriendlyTime(note.createTime),
      clickable: true,
      onClick: () => this.onNoteClick(note)
    }) {
      Column() {
        // 内容预览
        Text(note.content)
          .fontSize(this.isTablet ? 18 : 16)
          .fontColor(MorandiColors.textSecondary)
          .maxLines(this.isTablet ? 4 : 3)
          .textOverflow({ overflow: TextOverflow.Ellipsis })
          .textAlign(TextAlign.Start)
          .width('100%')
          .lineHeight(this.isTablet ? 28 : 24);
        
        // 标签
        if (note.tags && note.tags.trim()) {
          Row() {
            ForEach(note.tags.split(','), (tag: string) => {
              if (tag.trim()) {
                Text(tag.trim())
                  .fontSize(this.isTablet ? 14 : 12)
                  .fontColor(MorandiColors.textTertiary)
                  .padding({ 
                    left: this.isTablet ? 10 : 8, 
                    right: this.isTablet ? 10 : 8, 
                    top: this.isTablet ? 6 : 4, 
                    bottom: this.isTablet ? 6 : 4 
                  })
                  .backgroundColor(MorandiColors.cardBackground)
                  .borderRadius(this.isTablet ? 14 : 12)
                  .border({ width: 1, color: MorandiColors.border });
              }
            });
          }
          .width('100%')
          .margin({ top: this.isTablet ? 16 : 12 })
          .wrapContent(WrapAlignment.Start);
        }
        
        // 时间信息
        Row() {
          Text(DateUtils.formatDate(new Date(note.createTime), 'HH:mm'))
            .fontSize(this.isTablet ? 14 : 12)
            .fontColor(MorandiColors.textHint);
          
          Blank();
          
          // 分类标识（根据categoryId显示不同颜色）
          Row() {
            Text(this.getCategoryName(note.categoryId))
              .fontSize(this.isTablet ? 14 : 12)
              .fontColor(MorandiColors.textTertiary);
          }
          .padding({ 
            left: this.isTablet ? 10 : 8, 
            right: this.isTablet ? 10 : 8, 
            top: this.isTablet ? 4 : 2, 
            bottom: this.isTablet ? 4 : 2 
          })
          .backgroundColor(this.getCategoryColor(note.categoryId))
          .borderRadius(this.isTablet ? 10 : 8);
        }
        .width('100%')
        .margin({ top: this.isTablet ? 16 : 12 });
      }
      .width('100%');
    }
    .onHover((isHover: boolean) => {
      // 悬浮效果通过状态管理实现
    })
    .animation({
      duration: 200,
      curve: Curve.EaseInOut,
      delay: 0,
      iterations: 1,
      playMode: PlayMode.Normal
    });
  }

  /**
   * 悬浮按钮
   */
  @Builder FloatingButton() {
    Stack() {
      Button() {
        Row() {
          Image($r('app.media.ic_add'))
            .width(this.isTablet ? 28 : 24)
            .height(this.isTablet ? 28 : 24)
            .fillColor(Color.White)
            .rotate({ angle: 0 });
        }
        .width(this.isTablet ? 64 : 56)
        .height(this.isTablet ? 64 : 56)
        .borderRadius(this.isTablet ? 32 : 28)
        .backgroundColor(MorandiColors.primary)
        .shadow({
          radius: this.isTablet ? 16 : 12,
          color: MorandiColors.shadow,
          offsetX: 0,
          offsetY: this.isTablet ? 8 : 6
        })
        .onClick(() => {
          this.createNewNote();
        });
      }
      .position({ x: this.isTablet ? '88%' : '85%', y: this.isTablet ? '88%' : '85%' })
      .zIndex(100)
      .onHover((isHover: boolean) => {
        // 悬浮效果
      })
      .animation({
        duration: 300,
        curve: Curve.EaseInOut,
        delay: 0,
        iterations: 1,
        playMode: PlayMode.Normal
      });
    }
    .width('100%')
    .height('100%')
    .clip(false);
  }

  /**
   * 性能调试视图
   */
  @Builder PerformanceDebugView() {
    Column() {
      Text('性能调试信息')
        .fontSize(12)
        .fontColor('#666666')
        .fontWeight(500);
      
      Text(`FPS: ${this.performanceMonitor.getFPS().toFixed(1)}`)
        .fontSize(11)
        .fontColor('#666666');
      
      Text(`内存: ${this.performanceMonitor.getMemoryUsage()}MB`)
        .fontSize(11)
        .fontColor('#666666');
      
      Text(`渲染时间: ${this.performanceMonitor.getRenderTime()}ms`)
        .fontSize(11)
        .fontColor('#666666');
      
      Text(`虚拟项: ${this.virtualListItems.length}`)
        .fontSize(11)
        .fontColor('#666666');
    }
    .width('100%')
    .padding(8)
    .backgroundColor('rgba(0, 0, 0, 0.05)')
    .borderRadius(4)
    .margin({ top: 8 });
  }

  /**
   * 获取分类名称
   */
  private getCategoryName(categoryId: number): string {
    const categories = [
      { id: 1, name: '工作' },
      { id: 2, name: '生活' },
      { id: 3, name: '学习' },
      { id: 4, name: '计划' }
    ];
    const category = categories.find(c => c.id === categoryId);
    return category ? category.name : '其他';
  }

  /**
   * 获取分类颜色
   */
  private getCategoryColor(categoryId: number): string {
    switch (categoryId) {
      case 1: return MorandiColors.categoryWork;
      case 2: return MorandiColors.categoryLife;
      case 3: return MorandiColors.categoryStudy;
      case 4: return MorandiColors.categoryPlan;
      default: return MorandiColors.cardBackground;
    }
  }
}

// 性能监控器类
class PerformanceMonitor {
  private startTime: number = 0;
  private frameCount: number = 0;
  private lastFrameTime: number = 0;
  private fps: number = 0;
  private metrics: Map<string, any> = new Map();
  private debugEnabled: boolean = false;

  constructor() {
    this.startTime = Date.now();
    this.lastFrameTime = this.startTime;
  }

  start() {
    this.startTime = Date.now();
    this.frameCount = 0;
    this.lastFrameTime = this.startTime;
    this.startFPSMonitoring();
  }

  stop() {
    const duration = Date.now() - this.startTime;
    return {
      duration,
      averageFPS: this.fps,
      metrics: Array.from(this.metrics.entries())
    };
  }

  private startFPSMonitoring() {
    const updateFPS = () => {
      const now = Date.now();
      const delta = now - this.lastFrameTime;
      
      if (delta >= 1000) {
        this.fps = (this.frameCount * 1000) / delta;
        this.frameCount = 0;
        this.lastFrameTime = now;
      }
      
      this.frameCount++;
      requestAnimationFrame(updateFPS);
    };
    
    requestAnimationFrame(updateFPS);
  }

  async measure<T>(name: string, fn: () => Promise<T>): Promise<T> {
    const start = Date.now();
    try {
      const result = await fn();
      const duration = Date.now() - start;
      this.recordMetric(name, { duration, success: true });
      return result;
    } catch (error) {
      const duration = Date.now() - start;
      this.recordMetric(name, { duration, success: false, error: error.message });
      throw error;
    }
  }

  recordMetric(name: string, data: any) {
    this.metrics.set(name, data);
  }

  getFPS(): number {
    return this.fps;
  }

  getMemoryUsage(): number {
    // 模拟内存使用情况
    return Math.random() * 50 + 30; // 30-80MB
  }

  getRenderTime(): number {
    // 模拟渲染时间
    return Math.random() * 16 + 2; // 2-18ms
  }

  isDebugEnabled(): boolean {
    return this.debugEnabled;
  }

  setDebugEnabled(enabled: boolean) {
    this.debugEnabled = enabled;
  }
}