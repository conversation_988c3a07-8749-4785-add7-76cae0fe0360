import { NoteModel } from '../model/NoteModel';
import { MorandiColors } from '../constants/MorandiColors';
import { AppConstants } from '../constants/AppConstants';
import { DateUtils } from '../utils/DateUtils';
import { DatabaseService } from '../services/DatabaseService';
import { CalendarPageComponent } from './CalendarPage';
import { StatsPageComponent } from './StatsPage';
import { ProfilePageComponent } from './ProfilePage';

// 页面枚举
enum Page {
  TIMELINE = 'timeline',
  CALENDAR = 'calendar',
  STATS = 'stats',
  PROFILE = 'profile'
}

@Entry
@Component
struct Main {
  @State currentPage: Page = Page.TIMELINE;
  @State notes: NoteModel[] = [];
  @State isLoading: boolean = false;
  
  aboutToAppear() {
    this.loadNotes();
  }
  
  /**
   * 加载笔记数据
   */
  private async loadNotes() {
    this.isLoading = true;
    try {
      const db = DatabaseService.getInstance();
      this.notes = await db.getNotes();
    } catch (error) {
      console.error('Failed to load notes:', error);
    } finally {
      this.isLoading = false;
    }
  }
  
  build() {
    Column() {
      // 顶部状态栏占位
      Row()
        .width('100%')
        .height(44)
        .backgroundColor(MorandiColors.background);
      
      // 页面内容区域
      Stack() {
        // 时间轴页面
        if (this.currentPage === Page.TIMELINE) {
          this.TimeTabPage()
        }
        
        // 日历页面
        if (this.currentPage === Page.CALENDAR) {
          this.CalendarPage()
        }
        
        // 统计页面
        if (this.currentPage === Page.STATS) {
          this.StatsPage()
        }
        
        // 个人中心页面
        if (this.currentPage === Page.PROFILE) {
          this.ProfilePage()
        }
      }
      .layoutWeight(1);
      
      // 底部导航栏
      this.BottomNavigationBar();
    }
    .width('100%')
    .height('100%')
    .backgroundColor(MorandiColors.background);
  }
  
  /**
   * 时间轴页面
   */
  @Builder TimeTabPage() {
    Column() {
      // 顶部标题
      Row() {
        Column() {
          Text('时光拾光')
            .fontSize(28)
            .fontWeight(400)
            .fontColor(MorandiColors.textPrimary)
            .letterSpacing(1);
          
          Text(DateUtils.getWeekday(DateUtils.today()) + ' ' + DateUtils.formatDate(new Date(), 'MM月DD日'))
            .fontSize(14)
            .fontColor(MorandiColors.textTertiary)
            .margin({ top: 8 });
        }
        .alignItems(HorizontalAlign.Start)
      }
      .width('100%')
      .padding({ left: 24, right: 24, top: 20, bottom: 16 })
      .backgroundColor(MorandiColors.background);
      
      // 笔记列表
      if (this.isLoading) {
        LoadingProgress()
          .width(50)
          .height(50);
      } else {
        List() {
          ForEach(this.notes, (note: NoteModel) => {
            ListItem() {
              this.NoteCard(note);
            }
          });
        }
        .width('100%')
        .layoutWeight(1)
        .listDirection(Axis.Vertical)
        .edgeEffect(EdgeEffect.Spring);
      }
    }
  }
  
  /**
   * 日历页面
   */
  @Builder CalendarPage() {
    CalendarPageComponent();
  }
  
  /**
   * 统计页面
   */
  @Builder StatsPage() {
    StatsPageComponent();
  }
  
  /**
   * 个人中心页面
   */
  @Builder ProfilePage() {
    Column() {
      Text('个人中心')
        .fontSize(20)
        .fontColor(MorandiColors.textPrimary);
    }
    .width('100%')
    .height('100%')
    .justifyContent(FlexAlign.Center);
  }
  
  /**
   * 笔记卡片组件
   */
  @Builder NoteCard(note: NoteModel) {
    Column() {
      // 时间
      Text(DateUtils.getFriendlyTime(note.createTime))
        .fontSize(12)
        .fontColor(MorandiColors.textTertiary);
      
      // 标题
      Text(note.title)
        .fontSize(18)
        .fontWeight(400)
        .fontColor(MorandiColors.textPrimary)
        .margin({ top: 8, bottom: 8 })
        .maxLines(2)
        .textOverflow({ overflow: TextOverflow.Ellipsis });
      
      // 内容
      Text(note.content)
        .fontSize(14)
        .fontColor(MorandiColors.textSecondary)
        .lineHeight(20)
        .maxLines(3)
        .textOverflow({ overflow: TextOverflow.Ellipsis });
      
      // 标签
      if (note.tags) {
        Flex({ wrap: FlexWrap.Wrap }) {
          ForEach(note.tags.split(','), (tag: string) => {
            if (tag.trim()) {
              Text(tag.trim())
                .fontSize(11)
                .fontColor(MorandiColors.textHint)
                .backgroundColor(MorandiColors.border)
                .padding({ left: 12, right: 12, top: 4, bottom: 4 })
                .borderRadius(16)
                .margin({ top: 12, right: 8 });
            }
          });
        }
      }
    }
    .width('100%')
    .padding(16)
    .backgroundColor(MorandiColors.cardBackground)
    .borderRadius(16)
    .border({ width: 1, color: MorandiColors.border })
    .margin({ left: 24, right: 24, bottom: 16 })
    .onClick(() => {
      // TODO: 跳转到笔记详情页
    });
  }
  
  /**
   * 底部导航栏
   */
  @Builder BottomNavigationBar() {
    Row() {
      ForEach([
        { page: Page.TIMELINE, icon: 'timeline', label: '时间轴' },
        { page: Page.CALENDAR, icon: 'calendar', label: '日历' },
        { page: Page.STATS, icon: 'stats', label: '统计' },
        { page: Page.PROFILE, icon: 'profile', label: '我的' }
      ], (item: { page: Page, icon: string, label: string }) => {
        Column() {
          // 图标占位
          Text(this.getIconSymbol(item.icon))
            .fontSize(24)
            .fontColor(this.currentPage === item.page ? MorandiColors.accent : MorandiColors.textHint);
          
          Text(item.label)
            .fontSize(10)
            .fontColor(this.currentPage === item.page ? MorandiColors.accent : MorandiColors.textHint)
            .margin({ top: 4 });
        }
        .layoutWeight(1)
        .justifyContent(FlexAlign.Center)
        .onClick(() => {
          this.currentPage = item.page;
        });
      });
    }
    .width('100%')
    .height(80)
    .backgroundColor(MorandiColors.background)
    .border({ width: { top: 1 }, color: MorandiColors.border })
    .padding({ bottom: 20 });
  }
  
  /**
   * 获取图标符号（简单文本代替）
   */
  private getIconSymbol(icon: string): string {
    switch (icon) {
      case 'timeline': return '✓';
      case 'calendar': return '📅';
      case 'stats': return '📊';
      case 'profile': return '👤';
      default: return '○';
    }
  }
}