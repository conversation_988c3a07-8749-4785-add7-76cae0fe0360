import { StateManager } from '../state/StateManager';
import { EnhancedDatabaseService } from '../services/EnhancedDatabaseService';
import { RepositoryFactory } from '../repositories/index';
import { DataPreloadService } from '../services/DataPreloadService';
import { BackupRestoreService, BackupScheduler } from '../services/BackupRestoreService';
import { DataConsistencyService } from '../services/DataConsistencyService';
import { DataChangeManager } from '../data/DataObserver';
import { MyApplication } from '../entryability/MyApplication';

/**
 * 应用配置
 */
export interface AppConfig {
  database: {
    name: string;
    securityLevel: string;
  };
  cache: {
    enabled: boolean;
    maxSize: number;
    ttl: number;
  };
  preload: {
    enabled: boolean;
    batchSize: number;
    maxConcurrent: number;
  };
  backup: {
    enabled: boolean;
    autoBackup: boolean;
    backupInterval: number;
    maxBackups: number;
  };
  consistency: {
    enabled: boolean;
    autoCheck: boolean;
    checkInterval: number;
  };
  performance: {
    enableIndexes: boolean;
    enableCaching: boolean;
    enableBatching: boolean;
  };
}

/**
 * 应用服务管理器
 */
export class AppServiceManager {
  private static instance: AppServiceManager;
  private initialized = false;
  private config: AppConfig;
  
  // 服务实例
  private stateManager: StateManager;
  private dbService: EnhancedDatabaseService;
  private preloadService: DataPreloadService;
  private backupService: BackupRestoreService;
  private backupScheduler: BackupScheduler;
  private consistencyService: DataConsistencyService;
  private dataChangeManager: DataChangeManager;
  
  private constructor() {
    this.config = this.getDefaultConfig();
  }
  
  static getInstance(): AppServiceManager {
    if (!AppServiceManager.instance) {
      AppServiceManager.instance = new AppServiceManager();
    }
    return AppServiceManager.instance;
  }
  
  /**
   * 获取默认配置
   */
  private getDefaultConfig(): AppConfig {
    return {
      database: {
        name: 'timenotes.db',
        securityLevel: 'S1'
      },
      cache: {
        enabled: true,
        maxSize: 1000,
        ttl: 5 * 60 * 1000 // 5分钟
      },
      preload: {
        enabled: true,
        batchSize: 5,
        maxConcurrent: 3
      },
      backup: {
        enabled: true,
        autoBackup: true,
        backupInterval: 24 * 60 * 60 * 1000, // 24小时
        maxBackups: 10
      },
      consistency: {
        enabled: true,
        autoCheck: true,
        checkInterval: 7 * 24 * 60 * 60 * 1000 // 7天
      },
      performance: {
        enableIndexes: true,
        enableCaching: true,
        enableBatching: true
      }
    };
  }
  
  /**
   * 初始化应用服务
   */
  async initialize(context: Context, customConfig?: Partial<AppConfig>): Promise<void> {
    if (this.initialized) {
      console.warn('AppServiceManager already initialized');
      return;
    }
    
    try {
      console.log('Initializing AppServiceManager...');
      
      // 合并配置
      if (customConfig) {
        this.config = { ...this.config, ...customConfig };
      }
      
      // 初始化应用
      const myApp = MyApplication.getInstance();
      await myApp.init();
      
      // 初始化状态管理器
      console.log('Initializing StateManager...');
      this.stateManager = StateManager.getInstance();
      
      // 初始化数据库服务
      console.log('Initializing DatabaseService...');
      this.dbService = EnhancedDatabaseService.getInstance();
      await this.dbService.init(context);
      
      // 初始化数据变化管理器
      console.log('Initializing DataChangeManager...');
      this.dataChangeManager = DataChangeManager.getInstance();
      
      // 初始化数据预加载服务
      console.log('Initializing DataPreloadService...');
      this.preloadService = DataPreloadService.getInstance();
      if (this.config.preload.enabled) {
        await this.preloadService.initialize();
      }
      
      // 初始化备份恢复服务
      console.log('Initializing BackupRestoreService...');
      this.backupService = BackupRestoreService.getInstance();
      if (this.config.backup.enabled) {
        if (this.config.backup.autoBackup) {
          this.backupScheduler = BackupScheduler.getInstance();
          this.backupScheduler.setBackupInterval(this.config.backup.backupInterval);
          this.backupScheduler.start();
        }
      }
      
      // 初始化数据一致性服务
      console.log('Initializing DataConsistencyService...');
      this.consistencyService = DataConsistencyService.getInstance();
      if (this.config.consistency.enabled && this.config.consistency.autoCheck) {
        // 设置定时一致性检查
        this.scheduleConsistencyCheck();
      }
      
      // 执行初始数据加载
      console.log('Performing initial data load...');
      await this.performInitialDataLoad();
      
      // 执行初始一致性检查
      console.log('Performing initial consistency check...');
      await this.performInitialConsistencyCheck();
      
      this.initialized = true;
      console.log('AppServiceManager initialized successfully');
      
    } catch (error) {
      console.error('Failed to initialize AppServiceManager:', error);
      throw error;
    }
  }
  
  /**
   * 执行初始数据加载
   */
  private async performInitialDataLoad(): Promise<void> {
    try {
      // 加载设置
      const settings = await RepositoryFactory.getSettingsRepository().getSettings();
      if (settings) {
        this.stateManager.updateState({ userSettings: settings }, 'USER_SETTINGS_CHANGED');
      }
      
      // 加载统计数据
      const [noteCount, capsuleCount] = await Promise.all([
        RepositoryFactory.getNoteRepository().count(),
        RepositoryFactory.getCapsuleRepository().count()
      ]);
      
      this.stateManager.updateState({
        stats: {
          totalNotes: noteCount.success ? noteCount.data! : 0,
          totalCapsules: capsuleCount.success ? capsuleCount.data! : 0,
          writingStreak: 0,
          thisMonthNotes: 0
        }
      }, 'STATS_UPDATED');
      
      console.log('Initial data load completed');
      
    } catch (error) {
      console.error('Failed to perform initial data load:', error);
    }
  }
  
  /**
   * 执行初始一致性检查
   */
  private async performInitialConsistencyCheck(): Promise<void> {
    try {
      const report = await this.consistencyService.performQuickCheck();
      
      if (report.failedChecks > 0) {
        console.warn(`Initial consistency check found ${report.failedChecks} issues`);
        
        // 如果有严重问题，尝试自动修复
        if (report.critical > 0) {
          console.log('Attempting auto-repair...');
          await this.consistencyService.performFullCheck({
            enabled: true,
            dryRun: false,
            backupBeforeRepair: true,
            repairStrategies: {
              orphanedRecords: true,
              inconsistentDates: true,
              missingReferences: true,
              corruptedData: true,
              duplicateRecords: true
            }
          });
        }
      } else {
        console.log('Initial consistency check passed');
      }
      
    } catch (error) {
      console.error('Failed to perform initial consistency check:', error);
    }
  }
  
  /**
   * 安排一致性检查
   */
  private scheduleConsistencyCheck(): void {
    const checkInterval = this.config.consistency.checkInterval;
    
    // 立即执行一次检查
    setTimeout(() => {
      this.performScheduledConsistencyCheck();
    }, 60000); // 1分钟后开始
    
    // 设置定期检查
    setInterval(() => {
      this.performScheduledConsistencyCheck();
    }, checkInterval);
  }
  
  /**
   * 执行计划的一致性检查
   */
  private async performScheduledConsistencyCheck(): Promise<void> {
    try {
      console.log('Performing scheduled consistency check...');
      const report = await this.consistencyService.performQuickCheck();
      
      if (report.failedChecks > 0) {
        console.warn(`Scheduled consistency check found ${report.failedChecks} issues`);
        
        // 可以在这里添加通知逻辑
        if (report.critical > 0) {
          console.error('Critical issues found in scheduled consistency check');
        }
      } else {
        console.log('Scheduled consistency check passed');
      }
      
    } catch (error) {
      console.error('Failed to perform scheduled consistency check:', error);
    }
  }
  
  /**
   * 获取服务实例
   */
  getStateManager(): StateManager {
    this.ensureInitialized();
    return this.stateManager;
  }
  
  getDatabaseService(): EnhancedDatabaseService {
    this.ensureInitialized();
    return this.dbService;
  }
  
  getPreloadService(): DataPreloadService {
    this.ensureInitialized();
    return this.preloadService;
  }
  
  getBackupService(): BackupRestoreService {
    this.ensureInitialized();
    return this.backupService;
  }
  
  getConsistencyService(): DataConsistencyService {
    this.ensureInitialized();
    return this.consistencyService;
  }
  
  getDataChangeManager(): DataChangeManager {
    this.ensureInitialized();
    return this.dataChangeManager;
  }
  
  /**
   * 获取配置
   */
  getConfig(): AppConfig {
    return { ...this.config };
  }
  
  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<AppConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    // 应用新配置
    this.applyConfigChanges();
  }
  
  /**
   * 应用配置更改
   */
  private applyConfigChanges(): void {
    // 更新预加载配置
    if (this.preloadService) {
      this.preloadService.updateConfig(this.config.preload);
    }
    
    // 更新备份配置
    if (this.backupScheduler) {
      if (this.config.backup.autoBackup) {
        this.backupScheduler.setBackupInterval(this.config.backup.backupInterval);
        if (!this.backupScheduler.isSchedulerRunning()) {
          this.backupScheduler.start();
        }
      } else {
        this.backupScheduler.stop();
      }
    }
  }
  
  /**
   * 确保已初始化
   */
  private ensureInitialized(): void {
    if (!this.initialized) {
      throw new Error('AppServiceManager not initialized. Call initialize() first.');
    }
  }
  
  /**
   * 执行应用维护
   */
  async performMaintenance(): Promise<void> {
    try {
      console.log('Performing app maintenance...');
      
      // 数据库维护
      if (this.dbService) {
        await this.dbService.performMaintenance();
      }
      
      // 清理预加载缓存
      if (this.preloadService) {
        this.preloadService.cleanupExpiredResults();
      }
      
      // 执行一致性检查
      if (this.consistencyService && this.config.consistency.enabled) {
        await this.consistencyService.performQuickCheck();
      }
      
      console.log('App maintenance completed');
      
    } catch (error) {
      console.error('Failed to perform app maintenance:', error);
    }
  }
  
  /**
   * 获取应用状态
   */
  getAppStatus(): {
    initialized: boolean;
    services: {
      stateManager: boolean;
      database: boolean;
      preload: boolean;
      backup: boolean;
      consistency: boolean;
      dataChange: boolean;
    };
    lastConsistencyCheck: string | null;
    stats: any;
  } {
    const state = this.stateManager?.getState() || null;
    
    return {
      initialized: this.initialized,
      services: {
        stateManager: !!this.stateManager,
        database: !!this.dbService,
        preload: !!this.preloadService,
        backup: !!this.backupService,
        consistency: !!this.consistencyService,
        dataChange: !!this.dataChangeManager
      },
      lastConsistencyCheck: this.consistencyService?.getLastCheckTime() || null,
      stats: state?.stats || null
    };
  }
  
  /**
   * 销毁服务管理器
   */
  async destroy(): Promise<void> {
    try {
      console.log('Destroying AppServiceManager...');
      
      // 停止备份调度器
      if (this.backupScheduler) {
        this.backupScheduler.stop();
      }
      
      // 销毁预加载服务
      if (this.preloadService) {
        this.preloadService.destroy();
      }
      
      // 销毁仓库工厂
      RepositoryFactory.destroy();
      
      // 清理状态
      this.initialized = false;
      
      console.log('AppServiceManager destroyed');
      
    } catch (error) {
      console.error('Failed to destroy AppServiceManager:', error);
    }
  }
}

/**
 * 应用初始化装饰器
 */
export function AppInitializer() {
  return function <T extends { new (...args: any[]): any }>(constructor: T) {
    return class extends constructor {
      private serviceManager: AppServiceManager;
      
      constructor(...args: any[]) {
        super(...args);
        this.serviceManager = AppServiceManager.getInstance();
      }
      
      /**
       * 初始化应用
       */
      async initializeApp(context: Context, config?: Partial<AppConfig>): Promise<void> {
        try {
          await this.serviceManager.initialize(context, config);
          
          // 调用子类的初始化方法
          if (typeof this.onAppInitialized === 'function') {
            await this.onAppInitialized();
          }
          
        } catch (error) {
          console.error('Failed to initialize app:', error);
          throw error;
        }
      }
      
      /**
       * 应用初始化完成回调（子类可重写）
       */
      protected async onAppInitialized(): Promise<void> {
        // 子类实现
      }
      
      /**
       * 获取服务管理器
       */
      protected getServiceManager(): AppServiceManager {
        return this.serviceManager;
      }
      
      /**
       * 组件销毁时清理
       */
      aboutToDisappear(): void {
        // 可以在这里添加清理逻辑
        if (super.aboutToDisappear) {
          super.aboutToDisappear();
        }
      }
    };
  };
}

/**
 * 便捷的服务访问器
 */
export class ServiceAccessor {
  private static serviceManager: AppServiceManager;
  
  static setServiceManager(manager: AppServiceManager): void {
    ServiceAccessor.serviceManager = manager;
  }
  
  static getStateManager(): StateManager {
    return ServiceAccessor.serviceManager.getStateManager();
  }
  
  static getDatabaseService(): EnhancedDatabaseService {
    return ServiceAccessor.serviceManager.getDatabaseService();
  }
  
  static getPreloadService(): DataPreloadService {
    return ServiceAccessor.serviceManager.getPreloadService();
  }
  
  static getBackupService(): BackupRestoreService {
    return ServiceAccessor.serviceManager.getBackupService();
  }
  
  static getConsistencyService(): DataConsistencyService {
    return ServiceAccessor.serviceManager.getConsistencyService();
  }
  
  static getDataChangeManager(): DataChangeManager {
    return ServiceAccessor.serviceManager.getDataChangeManager();
  }
  
  static getNoteRepository() {
    return RepositoryFactory.getNoteRepository();
  }
  
  static getCapsuleRepository() {
    return RepositoryFactory.getCapsuleRepository();
  }
  
  static getSettingsRepository() {
    return RepositoryFactory.getSettingsRepository();
  }
}