import { StateManager, StateChangeEvent, StateObserver } from '../state/StateManager';
import { EnhancedDatabaseService, DatabaseEvent, DatabaseObserver } from '../services/EnhancedDatabaseService';
import { RepositoryFactory } from '../repositories/index';
import { NoteModel, CapsuleModel, SettingsModel } from '../model/NoteModel';

/**
 * 数据变化事件类型
 */
export type DataChangeEventType = 
  | 'NOTE_CREATED'
  | 'NOTE_UPDATED'
  | 'NOTE_DELETED'
  | 'CAPSULE_CREATED'
  | 'CAPSULE_UPDATED'
  | 'CAPSULE_DELETED'
  | 'CAPSULE_OPENED'
  | 'SETTINGS_CHANGED'
  | 'BULK_OPERATION'
  | 'DATA_SYNCED'
  | 'DATA_BACKED_UP'
  | 'DATA_RESTORED';

/**
 * 数据变化事件
 */
export interface DataChangeEvent {
  type: DataChangeEventType;
  entityId?: number;
  entityType?: 'note' | 'capsule' | 'settings';
  changes?: any;
  timestamp: string;
  source: 'user' | 'system' | 'sync' | 'backup';
}

/**
 * 数据观察者接口
 */
export interface DataObserver {
  onDataChanged(event: DataChangeEvent): void;
  getObservedTypes(): DataChangeEventType[];
  getObservedEntities(): ('note' | 'capsule' | 'settings')[];
}

/**
 * 数据变化管理器
 */
export class DataChangeManager {
  private static instance: DataChangeManager;
  private stateManager: StateManager;
  private dbService: EnhancedDatabaseService;
  private observers: Map<string, DataObserver> = new Map();
  private eventHistory: DataChangeEvent[] = [];
  private readonly maxHistorySize = 1000;
  private isProcessing = false;
  
  private constructor() {
    this.stateManager = StateManager.getInstance();
    this.dbService = EnhancedDatabaseService.getInstance();
    this.initialize();
  }
  
  static getInstance(): DataChangeManager {
    if (!DataChangeManager.instance) {
      DataChangeManager.instance = new DataChangeManager();
    }
    return DataChangeManager.instance;
  }
  
  /**
   * 初始化数据变化管理器
   */
  private initialize(): void {
    // 注册状态观察者
    this.stateManager.registerObserver('data_change_manager', {
      onStateChanged: (event: StateChangeEvent) => {
        this.handleStateChange(event);
      },
      getObservedTypes: () => [
        'NOTE_CREATED',
        'NOTE_UPDATED',
        'NOTE_DELETED',
        'CAPSULE_CREATED',
        'CAPSULE_UPDATED',
        'CAPSULE_DELETED',
        'USER_SETTINGS_CHANGED'
      ]
    });
    
    // 注册数据库观察者
    this.dbService.registerObserver('data_change_manager', {
      onDatabaseEvent: (event: DatabaseEvent) => {
        this.handleDatabaseEvent(event);
      },
      getObservedTables: () => ['notes', 'capsules', 'settings'],
      getObservedEventTypes: () => ['INSERT', 'UPDATE', 'DELETE']
    });
  }
  
  /**
   * 处理状态变化
   */
  private handleStateChange(event: StateChangeEvent): void {
    let dataEvent: DataChangeEvent | null = null;
    
    switch (event.type) {
      case 'NOTE_CREATED':
        dataEvent = {
          type: 'NOTE_CREATED',
          entityType: 'note',
          timestamp: event.timestamp,
          source: 'user'
        };
        break;
      
      case 'NOTE_UPDATED':
        dataEvent = {
          type: 'NOTE_UPDATED',
          entityType: 'note',
          timestamp: event.timestamp,
          source: 'user',
          changes: event.payload
        };
        break;
      
      case 'NOTE_DELETED':
        dataEvent = {
          type: 'NOTE_DELETED',
          entityType: 'note',
          timestamp: event.timestamp,
          source: 'user'
        };
        break;
      
      case 'CAPSULE_CREATED':
        dataEvent = {
          type: 'CAPSULE_CREATED',
          entityType: 'capsule',
          timestamp: event.timestamp,
          source: 'user'
        };
        break;
      
      case 'CAPSULE_UPDATED':
        dataEvent = {
          type: 'CAPSULE_UPDATED',
          entityType: 'capsule',
          timestamp: event.timestamp,
          source: 'user',
          changes: event.payload
        };
        break;
      
      case 'CAPSULE_DELETED':
        dataEvent = {
          type: 'CAPSULE_DELETED',
          entityType: 'capsule',
          timestamp: event.timestamp,
          source: 'user'
        };
        break;
      
      case 'USER_SETTINGS_CHANGED':
        dataEvent = {
          type: 'SETTINGS_CHANGED',
          entityType: 'settings',
          timestamp: event.timestamp,
          source: 'user',
          changes: event.payload
        };
        break;
    }
    
    if (dataEvent) {
      this.emitDataChangeEvent(dataEvent);
    }
  }
  
  /**
   * 处理数据库事件
   */
  private handleDatabaseEvent(event: DatabaseEvent): void {
    let dataEvent: DataChangeEvent | null = null;
    
    switch (event.table) {
      case 'notes':
        switch (event.type) {
          case 'INSERT':
            dataEvent = {
              type: 'NOTE_CREATED',
              entityType: 'note',
              entityId: event.details?.id,
              timestamp: event.timestamp,
              source: 'system'
            };
            break;
          case 'UPDATE':
            dataEvent = {
              type: 'NOTE_UPDATED',
              entityType: 'note',
              entityId: event.details?.id,
              timestamp: event.timestamp,
              source: 'system',
              changes: event.details
            };
            break;
          case 'DELETE':
            dataEvent = {
              type: 'NOTE_DELETED',
              entityType: 'note',
              entityId: event.details?.id,
              timestamp: event.timestamp,
              source: 'system'
            };
            break;
        }
        break;
      
      case 'capsules':
        switch (event.type) {
          case 'INSERT':
            dataEvent = {
              type: 'CAPSULE_CREATED',
              entityType: 'capsule',
              entityId: event.details?.id,
              timestamp: event.timestamp,
              source: 'system'
            };
            break;
          case 'UPDATE':
            // 检查是否是开启胶囊
            if (event.details?.isOpened === 1) {
              dataEvent = {
                type: 'CAPSULE_OPENED',
                entityType: 'capsule',
                entityId: event.details?.id,
                timestamp: event.timestamp,
                source: 'system',
                changes: event.details
              };
            } else {
              dataEvent = {
                type: 'CAPSULE_UPDATED',
                entityType: 'capsule',
                entityId: event.details?.id,
                timestamp: event.timestamp,
                source: 'system',
                changes: event.details
              };
            }
            break;
          case 'DELETE':
            dataEvent = {
              type: 'CAPSULE_DELETED',
              entityType: 'capsule',
              entityId: event.details?.id,
              timestamp: event.timestamp,
              source: 'system'
            };
            break;
        }
        break;
      
      case 'settings':
        if (event.type === 'UPDATE') {
          dataEvent = {
            type: 'SETTINGS_CHANGED',
            entityType: 'settings',
            timestamp: event.timestamp,
            source: 'system',
            changes: event.details
          };
        }
        break;
    }
    
    if (dataEvent) {
      this.emitDataChangeEvent(dataEvent);
    }
  }
  
  /**
   * 发送数据变化事件
   */
  private emitDataChangeEvent(event: DataChangeEvent): void {
    // 记录事件历史
    this.eventHistory.push(event);
    
    // 保持历史记录在限制范围内
    if (this.eventHistory.length > this.maxHistorySize) {
      this.eventHistory.shift();
    }
    
    // 通知观察者
    this.notifyObservers(event);
  }
  
  /**
   * 通知观察者
   */
  private notifyObservers(event: DataChangeEvent): void {
    for (const observer of this.observers.values()) {
      try {
        const observedTypes = observer.getObservedTypes();
        const observedEntities = observer.getObservedEntities();
        
        // 检查是否观察该类型
        const typeMatch = observedTypes.length === 0 || observedTypes.includes(event.type);
        
        // 检查是否观察该实体类型
        const entityMatch = observedEntities.length === 0 || 
          (event.entityType && observedEntities.includes(event.entityType));
        
        if (typeMatch && entityMatch) {
          observer.onDataChanged(event);
        }
      } catch (error) {
        console.error('Error notifying data observer:', error);
      }
    }
  }
  
  /**
   * 注册数据观察者
   */
  public registerObserver(id: string, observer: DataObserver): void {
    this.observers.set(id, observer);
    console.log(`Data observer registered: ${id}`);
  }
  
  /**
   * 注销数据观察者
   */
  public unregisterObserver(id: string): void {
    this.observers.delete(id);
    console.log(`Data observer unregistered: ${id}`);
  }
  
  /**
   * 手动触发数据变化事件
   */
  public triggerEvent(event: DataChangeEvent): void {
    this.emitDataChangeEvent(event);
  }
  
  /**
   * 批量触发数据变化事件
   */
  public triggerEvents(events: DataChangeEvent[]): void {
    events.forEach(event => this.emitDataChangeEvent(event));
  }
  
  /**
   * 获取事件历史
   */
  public getEventHistory(limit?: number): DataChangeEvent[] {
    const history = [...this.eventHistory];
    if (limit) {
      return history.slice(-limit);
    }
    return history;
  }
  
  /**
   * 清除事件历史
   */
  public clearEventHistory(): void {
    this.eventHistory = [];
  }
  
  /**
   * 获取特定类型的事件
   */
  public getEventsByType(type: DataChangeEventType): DataChangeEvent[] {
    return this.eventHistory.filter(event => event.type === type);
  }
  
  /**
   * 获取特定实体的事件
   */
  public getEventsByEntity(entityType: 'note' | 'capsule' | 'settings', entityId?: number): DataChangeEvent[] {
    return this.eventHistory.filter(event => 
      event.entityType === entityType && 
      (entityId === undefined || event.entityId === entityId)
    );
  }
  
  /**
   * 获取统计信息
   */
  public getStatistics(): {
    totalEvents: number;
    eventsByType: Record<DataChangeEventType, number>;
    eventsByEntity: Record<'note' | 'capsule' | 'settings', number>;
    recentEvents: DataChangeEvent[];
  } {
    const eventsByType: Record<DataChangeEventType, number> = {
      'NOTE_CREATED': 0,
      'NOTE_UPDATED': 0,
      'NOTE_DELETED': 0,
      'CAPSULE_CREATED': 0,
      'CAPSULE_UPDATED': 0,
      'CAPSULE_DELETED': 0,
      'CAPSULE_OPENED': 0,
      'SETTINGS_CHANGED': 0,
      'BULK_OPERATION': 0,
      'DATA_SYNCED': 0,
      'DATA_BACKED_UP': 0,
      'DATA_RESTORED': 0
    };
    
    const eventsByEntity: Record<'note' | 'capsule' | 'settings', number> = {
      'note': 0,
      'capsule': 0,
      'settings': 0
    };
    
    this.eventHistory.forEach(event => {
      eventsByType[event.type]++;
      if (event.entityType) {
        eventsByEntity[event.entityType]++;
      }
    });
    
    const recentEvents = this.eventHistory.slice(-10);
    
    return {
      totalEvents: this.eventHistory.length,
      eventsByType,
      eventsByEntity,
      recentEvents
    };
  }
}

/**
 * UI数据绑定装饰器
 */
export function DataBinding(observedTypes: DataChangeEventType[] = [], observedEntities: ('note' | 'capsule' | 'settings')[] = []) {
  return function <T extends { new (...args: any[]): any }>(constructor: T) {
    return class extends constructor implements DataObserver {
      private dataChangeManager: DataChangeManager;
      private componentId: string;
      private isUpdating = false;
      
      constructor(...args: any[]) {
        super(...args);
        this.dataChangeManager = DataChangeManager.getInstance();
        this.componentId = `component_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        // 注册为数据观察者
        this.dataChangeManager.registerObserver(this.componentId, this);
      }
      
      /**
       * 数据变化回调
       */
      onDataChanged(event: DataChangeEvent): void {
        if (this.isUpdating) return;
        
        // 防抖处理
        clearTimeout(this.updateTimeout);
        this.updateTimeout = setTimeout(() => {
          this.handleDataChange(event);
        }, 100);
      }
      
      /**
       * 获取监听的数据变化类型
       */
      getObservedTypes(): DataChangeEventType[] {
        return observedTypes;
      }
      
      /**
       * 获取监听的实体类型
       */
      getObservedEntities(): ('note' | 'capsule' | 'settings')[] {
        return observedEntities;
      }
      
      /**
       * 处理数据变化（子类可重写）
       */
      protected handleDataChange(event: DataChangeEvent): void {
        // 默认实现：重新加载数据
        if (typeof this.reloadData === 'function') {
          this.reloadData();
        }
        
        // 触发UI更新
        if (typeof this.forceUpdate === 'function') {
          this.forceUpdate();
        }
      }
      
      /**
       * 重新加载数据（子类实现）
       */
      protected reloadData?(): void;
      
      /**
       * 强制更新UI（子类实现）
       */
      protected forceUpdate?(): void;
      
      /**
       * 组件销毁时清理
       */
      aboutToDisappear(): void {
        // 注销数据观察者
        this.dataChangeManager.unregisterObserver(this.componentId);
        
        // 清理定时器
        clearTimeout(this.updateTimeout);
        
        // 调用父类的销毁方法
        if (super.aboutToDisappear) {
          super.aboutToDisappear();
        }
      }
      
      private updateTimeout: any = null;
    };
  };
}

/**
 * 数据选择器装饰器
 */
export function DataSelector<T>(selector: (state: any) => T) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalGetter = descriptor.get;
    
    descriptor.get = function () {
      const stateManager = StateManager.getInstance();
      const state = stateManager.getState();
      return selector(state);
    };
    
    return descriptor;
  };
}

/**
 * 数据变化订阅工具类
 */
export class DataSubscription {
  private dataChangeManager: DataChangeManager;
  private subscriptionId: string;
  private callback: (event: DataChangeEvent) => void;
  private observedTypes: DataChangeEventType[];
  private observedEntities: ('note' | 'capsule' | 'settings')[];
  
  constructor(
    callback: (event: DataChangeEvent) => void,
    observedTypes: DataChangeEventType[] = [],
    observedEntities: ('note' | 'capsule' | 'settings')[] = []
  ) {
    this.dataChangeManager = DataChangeManager.getInstance();
    this.callback = callback;
    this.observedTypes = observedTypes;
    this.observedEntities = observedEntities;
    this.subscriptionId = `subscription_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // 创建临时观察者
    const observer: DataObserver = {
      onDataChanged: (event: DataChangeEvent) => {
        if (this.shouldNotify(event)) {
          callback(event);
        }
      },
      getObservedTypes: () => this.observedTypes,
      getObservedEntities: () => this.observedEntities
    };
    
    this.dataChangeManager.registerObserver(this.subscriptionId, observer);
  }
  
  /**
   * 判断是否应该通知
   */
  private shouldNotify(event: DataChangeEvent): boolean {
    const typeMatch = this.observedTypes.length === 0 || this.observedTypes.includes(event.type);
    const entityMatch = this.observedEntities.length === 0 || 
      (event.entityType && this.observedEntities.includes(event.entityType));
    
    return typeMatch && entityMatch;
  }
  
  /**
   * 取消订阅
   */
  public unsubscribe(): void {
    this.dataChangeManager.unregisterObserver(this.subscriptionId);
  }
}

/**
 * 数据查询工具类
 */
export class DataQuery {
  private dataChangeManager: DataChangeManager;
  private stateManager: StateManager;
  
  constructor() {
    this.dataChangeManager = DataChangeManager.getInstance();
    this.stateManager = StateManager.getInstance();
  }
  
  /**
   * 查询状态
   */
  public queryState<T>(selector: (state: any) => T): T {
    const state = this.stateManager.getState();
    return selector(state);
  }
  
  /**
   * 订阅状态变化
   */
  public subscribeState<T>(
    selector: (state: any) => T,
    callback: (value: T) => void,
    observedTypes: DataChangeEventType[] = []
  ): DataSubscription {
    let lastValue: T | null = null;
    
    const subscription = new DataSubscription((event) => {
      const currentValue = this.queryState(selector);
      if (currentValue !== lastValue) {
        lastValue = currentValue;
        callback(currentValue);
      }
    }, observedTypes);
    
    // 立即执行一次
    lastValue = this.queryState(selector);
    callback(lastValue);
    
    return subscription;
  }
  
  /**
   * 订阅数据变化
   */
  public subscribeData(
    callback: (event: DataChangeEvent) => void,
    observedTypes: DataChangeEventType[] = [],
    observedEntities: ('note' | 'capsule' | 'settings')[] = []
  ): DataSubscription {
    return new DataSubscription(callback, observedTypes, observedEntities);
  }
  
  /**
   * 获取事件历史
   */
  public getEventHistory(limit?: number): DataChangeEvent[] {
    return this.dataChangeManager.getEventHistory(limit);
  }
  
  /**
   * 获取统计信息
   */
  public getStatistics() {
    return this.dataChangeManager.getStatistics();
  }
}