<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时光拾光 - 完整应用原型</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .phone-container {
            width: 375px;
            height: 812px;
            margin: 20px auto;
            background: #fff;
            border-radius: 30px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            position: relative;
        }
        
        .status-bar {
            height: 44px;
            background: #fff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
        }
        
        .app-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 16px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .app-title {
            font-size: 24px;
            font-weight: 600;
        }
        
        .header-icons {
            display: flex;
            gap: 16px;
        }
        
        .icon {
            width: 24px;
            height: 24px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .icon:hover {
            background: rgba(255,255,255,0.3);
        }
        
        .content-area {
            height: calc(100% - 144px);
            overflow-y: auto;
            position: relative;
        }
        
        .page {
            display: none;
            height: 100%;
        }
        
        .page.active {
            display: block;
        }
        
        /* 时光流页面 */
        .category-tabs {
            background: white;
            padding: 12px 0;
            overflow-x: auto;
            white-space: nowrap;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .category-tab {
            display: inline-block;
            padding: 8px 20px;
            margin: 0 8px;
            background: #f0f0f0;
            border-radius: 20px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .category-tab.active {
            background: #667eea;
            color: white;
        }
        
        .notes-container {
            padding: 16px;
        }
        
        .note-card {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: transform 0.3s, box-shadow 0.3s;
            cursor: pointer;
        }
        
        .note-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        }
        
        .note-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .note-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }
        
        .note-time {
            font-size: 12px;
            color: #999;
        }
        
        .note-content {
            font-size: 14px;
            color: #666;
            line-height: 1.5;
            margin-bottom: 12px;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .note-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .note-category {
            display: inline-block;
            padding: 4px 12px;
            background: #e3f2fd;
            color: #1976d2;
            border-radius: 12px;
            font-size: 12px;
        }
        
        .note-mood {
            font-size: 20px;
        }
        
        /* 日历页面 */
        .calendar-header {
            background: white;
            padding: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .month-year {
            font-size: 18px;
            font-weight: 600;
        }
        
        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 1px;
            background: #f0f0f0;
            margin: 16px;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .calendar-day-header {
            background: #667eea;
            color: white;
            padding: 12px 0;
            text-align: center;
            font-size: 14px;
            font-weight: 600;
        }
        
        .calendar-day {
            background: white;
            padding: 12px 0;
            text-align: center;
            font-size: 14px;
            min-height: 50px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .calendar-day:hover {
            background: #f5f5f5;
        }
        
        .calendar-day.other-month {
            color: #ccc;
        }
        
        .calendar-day.has-notes {
            background: #e3f2fd;
            font-weight: 600;
        }
        
        .calendar-day.today {
            background: #667eea;
            color: white;
        }
        
        .calendar-note-count {
            font-size: 10px;
            color: #667eea;
            margin-top: 2px;
        }
        
        .calendar-day.today .calendar-note-count {
            color: white;
        }
        
        /* 统计页面 */
        .stats-container {
            padding: 16px;
        }
        
        .stats-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .stats-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 16px;
            color: #333;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
        }
        
        .stat-item {
            text-align: center;
            padding: 16px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: 600;
            color: #667eea;
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: 12px;
            color: #666;
        }
        
        .mood-chart {
            display: flex;
            justify-content: space-around;
            align-items: flex-end;
            height: 120px;
            margin-top: 16px;
        }
        
        .mood-bar {
            width: 30px;
            background: #667eea;
            border-radius: 4px 4px 0 0;
            transition: height 0.3s;
            cursor: pointer;
        }
        
        .mood-bar:hover {
            opacity: 0.8;
        }
        
        .mood-label {
            font-size: 12px;
            text-align: center;
            margin-top: 8px;
        }
        
        /* 我的页面 */
        .profile-container {
            padding: 16px;
        }
        
        .profile-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            padding: 24px;
            color: white;
            text-align: center;
            margin-bottom: 24px;
        }
        
        .profile-avatar {
            width: 80px;
            height: 80px;
            background: white;
            border-radius: 50%;
            margin: 0 auto 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
        }
        
        .profile-name {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .profile-email {
            font-size: 14px;
            opacity: 0.8;
        }
        
        .menu-list {
            background: white;
            border-radius: 12px;
            overflow: hidden;
        }
        
        .menu-item {
            display: flex;
            align-items: center;
            padding: 16px 20px;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .menu-item:last-child {
            border-bottom: none;
        }
        
        .menu-item:hover {
            background: #f8f9fa;
        }
        
        .menu-icon {
            width: 24px;
            height: 24px;
            margin-right: 16px;
            font-size: 20px;
        }
        
        .menu-text {
            flex: 1;
            font-size: 16px;
        }
        
        .menu-arrow {
            font-size: 14px;
            color: #999;
        }
        
        /* 搜索页面 */
        .search-container {
            padding: 16px;
        }
        
        .search-box {
            background: white;
            border-radius: 12px;
            padding: 12px 16px;
            display: flex;
            align-items: center;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .search-input {
            border: none;
            outline: none;
            flex: 1;
            font-size: 16px;
            margin-left: 8px;
        }
        
        .search-filters {
            display: flex;
            gap: 8px;
            margin-bottom: 16px;
            overflow-x: auto;
        }
        
        .filter-tag {
            padding: 6px 12px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 16px;
            font-size: 12px;
            white-space: nowrap;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .filter-tag.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }
        
        .search-history {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
        }
        
        .history-title {
            font-size: 14px;
            color: #666;
            margin-bottom: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .history-item {
            display: flex;
            align-items: center;
            padding: 8px 0;
            cursor: pointer;
        }
        
        .history-icon {
            width: 16px;
            height: 16px;
            margin-right: 8px;
            color: #999;
        }
        
        .history-text {
            font-size: 14px;
            color: #666;
        }
        
        .search-results {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }
        
        /* 时光胶囊页面 */
        .capsule-container {
            padding: 16px;
        }
        
        .capsule-hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            padding: 32px 20px;
            color: white;
            text-align: center;
            margin-bottom: 24px;
        }
        
        .capsule-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }
        
        .capsule-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .capsule-desc {
            font-size: 14px;
            opacity: 0.8;
            line-height: 1.5;
        }
        
        .capsule-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }
        
        .capsule-item {
            background: white;
            border-radius: 12px;
            padding: 16px;
            display: flex;
            align-items: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.3s;
        }
        
        .capsule-item:hover {
            transform: translateX(4px);
        }
        
        .capsule-item-icon {
            width: 48px;
            height: 48px;
            background: #f0f0f0;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            margin-right: 16px;
        }
        
        .capsule-item-content {
            flex: 1;
        }
        
        .capsule-item-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .capsule-item-date {
            font-size: 12px;
            color: #999;
        }
        
        .capsule-item-status {
            font-size: 12px;
            padding: 4px 8px;
            background: #e3f2fd;
            color: #1976d2;
            border-radius: 12px;
        }
        
        /* 设置页面 */
        .settings-container {
            padding: 16px;
        }
        
        .settings-group {
            background: white;
            border-radius: 12px;
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .settings-group-title {
            font-size: 12px;
            color: #999;
            padding: 16px 20px 8px;
            text-transform: uppercase;
        }
        
        .settings-item {
            display: flex;
            align-items: center;
            padding: 16px 20px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .settings-item:last-child {
            border-bottom: none;
        }
        
        .settings-icon {
            width: 24px;
            height: 24px;
            margin-right: 16px;
            font-size: 18px;
        }
        
        .settings-content {
            flex: 1;
        }
        
        .settings-title {
            font-size: 16px;
            margin-bottom: 2px;
        }
        
        .settings-desc {
            font-size: 12px;
            color: #999;
        }
        
        .switch {
            width: 44px;
            height: 24px;
            background: #ccc;
            border-radius: 12px;
            position: relative;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .switch.active {
            background: #667eea;
        }
        
        .switch-handle {
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            position: absolute;
            top: 2px;
            left: 2px;
            transition: left 0.3s;
        }
        
        .switch.active .switch-handle {
            left: 22px;
        }
        
        /* 通用组件 */
        .fab {
            position: absolute;
            bottom: 80px;
            right: 24px;
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
            cursor: pointer;
            transition: transform 0.3s;
            z-index: 100;
        }
        
        .fab:hover {
            transform: scale(1.1);
        }
        
        .bottom-nav {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: white;
            border-top: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-around;
            align-items: center;
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            cursor: pointer;
            transition: opacity 0.3s;
            flex: 1;
        }
        
        .nav-item:hover {
            opacity: 0.7;
        }
        
        .nav-icon {
            width: 24px;
            height: 24px;
            background: #ccc;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
        }
        
        .nav-icon.active {
            background: #667eea;
            color: white;
        }
        
        .nav-text {
            font-size: 10px;
            color: #666;
        }
        
        .nav-text.active {
            color: #667eea;
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <div class="status-bar">
            <span>9:41</span>
            <span>●●●●● WiFi 📶 🔋</span>
        </div>
        
        <!-- 顶部导航栏 -->
        <div class="app-header">
            <h1 class="app-title" id="pageTitle">时光拾光</h1>
            <div class="header-icons" id="headerIcons">
                <div class="icon" onclick="showPage('searchPage')">🔍</div>
                <div class="icon" onclick="showPage('capsulePage')">✉️</div>
                <div class="icon" onclick="showPage('settingsPage')">⋯</div>
            </div>
        </div>
        
        <!-- 内容区域 -->
        <div class="content-area">
            <!-- 时光流页面 -->
            <div id="timelinePage" class="page active">
                <div class="category-tabs">
                    <span class="category-tab active">全部时光</span>
                    <span class="category-tab">工作时光</span>
                    <span class="category-tab">生活时光</span>
                    <span class="category-tab">学习时光</span>
                    <span class="category-tab">心情日记</span>
                </div>
                
                <div class="notes-container">
                    <div class="note-card" onclick="showEditPage()">
                        <div class="note-header">
                            <h3 class="note-title">今天的好心情</h3>
                            <span class="note-time">10:30</span>
                        </div>
                        <div class="note-content">
                            今天天气真好，阳光明媚。早上起床后看到窗外的阳光，心情一下子就变好了。决定去公园散步，享受这美好的时光...
                        </div>
                        <div class="note-footer">
                            <span class="note-category">生活时光</span>
                            <span class="note-mood">😊</span>
                        </div>
                    </div>
                    
                    <div class="note-card" onclick="showEditPage()">
                        <div class="note-header">
                            <h3 class="note-title">项目会议纪要</h3>
                            <span class="note-time">14:15</span>
                        </div>
                        <div class="note-content">
                            今天的项目进展会议讨论了以下几个要点：
                            1. 前端开发进度需要加快
                            2. 后端API接口已经完成80%
                            3. 下周开始进行联调测试
                        </div>
                        <div class="note-footer">
                            <span class="note-category">工作时光</span>
                            <span class="note-mood">💼</span>
                        </div>
                    </div>
                    
                    <div class="note-card" onclick="showEditPage()">
                        <div class="note-header">
                            <h3 class="note-title">学习笔记 - ArkUI</h3>
                            <span class="note-time">20:45</span>
                        </div>
                        <div class="note-content">
                            今天学习了ArkUI的状态管理：
                            - @State: 组件内部状态
                            - @Prop: 父子组件传值
                            - @Link: 双向数据绑定
                            - @Observed: 对象状态监听
                        </div>
                        <div class="note-footer">
                            <span class="note-category">学习时光</span>
                            <span class="note-mood">📚</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 日历页面 -->
            <div id="calendarPage" class="page">
                <div class="calendar-header">
                    <span style="font-size: 20px; cursor: pointer;">‹</span>
                    <span class="month-year">2024年1月</span>
                    <span style="font-size: 20px; cursor: pointer;">›</span>
                </div>
                
                <div class="calendar-grid">
                    <div class="calendar-day-header">日</div>
                    <div class="calendar-day-header">一</div>
                    <div class="calendar-day-header">二</div>
                    <div class="calendar-day-header">三</div>
                    <div class="calendar-day-header">四</div>
                    <div class="calendar-day-header">五</div>
                    <div class="calendar-day-header">六</div>
                    
                    <!-- 日期示例 -->
                    <div class="calendar-day other-month">31</div>
                    <div class="calendar-day">1</div>
                    <div class="calendar-day">2</div>
                    <div class="calendar-day has-notes">
                        3
                        <div class="calendar-note-count">2</div>
                    </div>
                    <div class="calendar-day">4</div>
                    <div class="calendar-day has-notes">
                        5
                        <div class="calendar-note-count">1</div>
                    </div>
                    <div class="calendar-day">6</div>
                    <div class="calendar-day">7</div>
                    <div class="calendar-day today">8</div>
                    <div class="calendar-day">9</div>
                    <div class="calendar-day has-notes">
                        10
                        <div class="calendar-note-count">3</div>
                    </div>
                    <div class="calendar-day">11</div>
                    <div class="calendar-day">12</div>
                    <div class="calendar-day">13</div>
                    <div class="calendar-day">14</div>
                    <div class="calendar-day has-notes">
                        15
                        <div class="calendar-note-count">1</div>
                    </div>
                    <div class="calendar-day">16</div>
                    <div class="calendar-day">17</div>
                    <div class="calendar-day">18</div>
                    <div class="calendar-day">19</div>
                    <div class="calendar-day">20</div>
                </div>
                
                <div style="padding: 16px;">
                    <h3 style="font-size: 16px; margin-bottom: 12px;">今日时光</h3>
                    <div class="note-card">
                        <div class="note-header">
                            <h3 class="note-title">早起晨跑</h3>
                            <span class="note-time">06:30</span>
                        </div>
                        <div class="note-content">
                            今天早起去公园晨跑，空气清新，感觉整个人都充满了活力...
                        </div>
                        <div class="note-footer">
                            <span class="note-category">生活时光</span>
                            <span class="note-mood">🏃</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 统计页面 -->
            <div id="statsPage" class="page">
                <div class="stats-container">
                    <div class="stats-card">
                        <div class="stats-title">总体统计</div>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-number">128</div>
                                <div class="stat-label">总时光数</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">45</div>
                                <div class="stat-label">本月记录</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">12</div>
                                <div class="stat-label">连续天数</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">5</div>
                                <div class="stat-label">分类数量</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="stats-card">
                        <div class="stats-title">心情分布</div>
                        <div class="mood-chart">
                            <div style="display: flex; flex-direction: column; align-items: center;">
                                <div class="mood-bar" style="height: 80px;"></div>
                                <div class="mood-label">😊</div>
                            </div>
                            <div style="display: flex; flex-direction: column; align-items: center;">
                                <div class="mood-bar" style="height: 60px;"></div>
                                <div class="mood-label">😌</div>
                            </div>
                            <div style="display: flex; flex-direction: column; align-items: center;">
                                <div class="mood-bar" style="height: 40px;"></div>
                                <div class="mood-label">🤔</div>
                            </div>
                            <div style="display: flex; flex-direction: column; align-items: center;">
                                <div class="mood-bar" style="height: 20px;"></div>
                                <div class="mood-label">😢</div>
                            </div>
                            <div style="display: flex; flex-direction: column; align-items: center;">
                                <div class="mood-bar" style="height: 30px;"></div>
                                <div class="mood-label">😎</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="stats-card">
                        <div class="stats-title">分类占比</div>
                        <div style="margin-top: 16px;">
                            <div style="display: flex; justify-content: space-between; margin-bottom: 12px;">
                                <span style="font-size: 14px;">工作时光</span>
                                <span style="font-size: 14px; font-weight: 600;">35%</span>
                            </div>
                            <div style="height: 8px; background: #f0f0f0; border-radius: 4px; margin-bottom: 16px;">
                                <div style="height: 100%; width: 35%; background: #667eea; border-radius: 4px;"></div>
                            </div>
                            
                            <div style="display: flex; justify-content: space-between; margin-bottom: 12px;">
                                <span style="font-size: 14px;">生活时光</span>
                                <span style="font-size: 14px; font-weight: 600;">30%</span>
                            </div>
                            <div style="height: 8px; background: #f0f0f0; border-radius: 4px; margin-bottom: 16px;">
                                <div style="height: 100%; width: 30%; background: #764ba2; border-radius: 4px;"></div>
                            </div>
                            
                            <div style="display: flex; justify-content: space-between; margin-bottom: 12px;">
                                <span style="font-size: 14px;">学习时光</span>
                                <span style="font-size: 14px; font-weight: 600;">25%</span>
                            </div>
                            <div style="height: 8px; background: #f0f0f0; border-radius: 4px; margin-bottom: 16px;">
                                <div style="height: 100%; width: 25%; background: #e3f2fd; border-radius: 4px;"></div>
                            </div>
                            
                            <div style="display: flex; justify-content: space-between; margin-bottom: 12px;">
                                <span style="font-size: 14px;">心情日记</span>
                                <span style="font-size: 14px; font-weight: 600;">10%</span>
                            </div>
                            <div style="height: 8px; background: #f0f0f0; border-radius: 4px;">
                                <div style="height: 100%; width: 10%; background: #f3e5f5; border-radius: 4px;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 我的页面 -->
            <div id="profilePage" class="page">
                <div class="profile-container">
                    <div class="profile-header">
                        <div class="profile-avatar">👤</div>
                        <div class="profile-name">时光记录者</div>
                        <div class="profile-email"><EMAIL></div>
                    </div>
                    
                    <div class="menu-list">
                        <div class="menu-item" onclick="window.location.href='分类管理页-高保真原型.html'">
                            <span class="menu-icon">📁</span>
                            <span class="menu-text">分类管理</span>
                            <span class="menu-arrow">›</span>
                        </div>
                        <div class="menu-item" onclick="showPage('capsulePage')">
                            <span class="menu-icon">✉️</span>
                            <span class="menu-text">时光胶囊</span>
                            <span class="menu-arrow">›</span>
                        </div>
                        <div class="menu-item" onclick="alert('数据备份功能开发中...')">
                            <span class="menu-icon">💾</span>
                            <span class="menu-text">数据备份</span>
                            <span class="menu-arrow">›</span>
                        </div>
                        <div class="menu-item" onclick="alert('同步设置功能开发中...')">
                            <span class="menu-icon">🔄</span>
                            <span class="menu-text">同步设置</span>
                            <span class="menu-arrow">›</span>
                        </div>
                        <div class="menu-item" onclick="alert('AI管理功能开发中...')">
                            <span class="menu-icon">🤖</span>
                            <span class="menu-text">AI管理</span>
                            <span class="menu-arrow">›</span>
                        </div>
                        <div class="menu-item" onclick="showPage('settingsPage')">
                            <span class="menu-icon">⚙️</span>
                            <span class="menu-text">设置</span>
                            <span class="menu-arrow">›</span>
                        </div>
                        <div class="menu-item" onclick="alert('关于时光拾光 v1.0.0')">
                            <span class="menu-icon">ℹ️</span>
                            <span class="menu-text">关于</span>
                            <span class="menu-arrow">›</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 搜索页面 -->
            <div id="searchPage" class="page">
                <div class="search-container">
                    <div class="search-box">
                        <span>🔍</span>
                        <input type="text" class="search-input" placeholder="搜索时光内容..." autofocus>
                    </div>
                    
                    <div class="search-filters">
                        <span class="filter-tag active">全部</span>
                        <span class="filter-tag">标题</span>
                        <span class="filter-tag">内容</span>
                        <span class="filter-tag">标签</span>
                        <span class="filter-tag">心情</span>
                    </div>
                    
                    <div class="search-history">
                        <div class="history-title">
                            <span>搜索历史</span>
                            <span style="color: #667eea; cursor: pointer;">清空</span>
                        </div>
                        <div class="history-item">
                            <span class="history-icon">🕐</span>
                            <span class="history-text">ArkUI</span>
                        </div>
                        <div class="history-item">
                            <span class="history-icon">🕐</span>
                            <span class="history-text">会议纪要</span>
                        </div>
                        <div class="history-item">
                            <span class="history-icon">🕐</span>
                            <span class="history-text">心情</span>
                        </div>
                    </div>
                    
                    <div class="search-results">
                        <div class="note-card">
                            <div class="note-header">
                                <h3 class="note-title">学习笔记 - ArkUI</h3>
                                <span class="note-time">20:45</span>
                            </div>
                            <div class="note-content">
                                今天学习了ArkUI的状态管理：
                                - @State: 组件内部状态
                                - @Prop: 父子组件传值
                                - @Link: 双向数据绑定
                                - @Observed: 对象状态监听
                            </div>
                            <div class="note-footer">
                                <span class="note-category">学习时光</span>
                                <span class="note-mood">📚</span>
                            </div>
                        </div>
                        
                        <div class="note-card">
                            <div class="note-header">
                                <h3 class="note-title">项目会议纪要</h3>
                                <span class="note-time">14:15</span>
                            </div>
                            <div class="note-content">
                                今天的项目进展会议讨论了以下几个要点：
                                1. 前端开发进度需要加快
                                2. 后端API接口已经完成80%
                                3. 下周开始进行联调测试
                            </div>
                            <div class="note-footer">
                                <span class="note-category">工作时光</span>
                                <span class="note-mood">💼</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 时光胶囊页面 -->
            <div id="capsulePage" class="page">
                <div class="capsule-container">
                    <div class="capsule-hero">
                        <div class="capsule-icon">📦</div>
                        <div class="capsule-title">时光胶囊</div>
                        <div class="capsule-desc">将珍贵的回忆封存，在未来某个特定的时刻重新打开</div>
                    </div>
                    
                    <div class="capsule-list">
                        <div class="capsule-item">
                            <div class="capsule-item-icon">💝</div>
                            <div class="capsule-item-content">
                                <div class="capsule-item-title">写给一年后的自己</div>
                                <div class="capsule-item-date">创建于 2024-01-01</div>
                            </div>
                            <div class="capsule-item-status">封存中</div>
                        </div>
                        
                        <div class="capsule-item">
                            <div class="capsule-item-icon">🎓</div>
                            <div class="capsule-item-content">
                                <div class="capsule-item-title">毕业纪念</div>
                                <div class="capsule-item-date">开启于 2024-06-30</div>
                            </div>
                            <div class="capsule-item-status">已开启</div>
                        </div>
                        
                        <div class="capsule-item">
                            <div class="capsule-item-icon">💑</div>
                            <div class="capsule-item-content">
                                <div class="capsule-item-title">恋爱日记</div>
                                <div class="capsule-item-date">创建于 2024-02-14</div>
                            </div>
                            <div class="capsule-item-status">封存中</div>
                        </div>
                        
                        <div class="capsule-item">
                            <div class="capsule-item-icon">👶</div>
                            <div class="capsule-item-content">
                                <div class="capsule-item-title">宝宝成长记录</div>
                                <div class="capsule-item-date">创建于 2023-12-25</div>
                            </div>
                            <div class="capsule-item-status">封存中</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 设置页面 -->
            <div id="settingsPage" class="page">
                <div class="settings-container">
                    <div class="settings-group">
                        <div class="settings-group-title">通用设置</div>
                        <div class="settings-item">
                            <span class="settings-icon">🌙</span>
                            <div class="settings-content">
                                <div class="settings-title">深色模式</div>
                                <div class="settings-desc">跟随系统设置</div>
                            </div>
                            <div class="switch" onclick="toggleSwitch(this)"></div>
                        </div>
                        <div class="settings-item">
                            <span class="settings-icon">🎨</span>
                            <div class="settings-content">
                                <div class="settings-title">主题颜色</div>
                                <div class="settings-desc">紫色</div>
                            </div>
                            <span class="menu-arrow">›</span>
                        </div>
                        <div class="settings-item">
                            <span class="settings-icon">📝</span>
                            <div class="settings-content">
                                <div class="settings-title">默认字体大小</div>
                                <div class="settings-desc">标准</div>
                            </div>
                            <span class="menu-arrow">›</span>
                        </div>
                    </div>
                    
                    <div class="settings-group">
                        <div class="settings-group-title">隐私与安全</div>
                        <div class="settings-item">
                            <span class="settings-icon">🔒</span>
                            <div class="settings-content">
                                <div class="settings-title">应用锁</div>
                                <div class="settings-desc">未开启</div>
                            </div>
                            <div class="switch" onclick="toggleSwitch(this)"></div>
                        </div>
                        <div class="settings-item">
                            <span class="settings-icon">👁️</span>
                            <div class="settings-content">
                                <div class="settings-title">隐私模式</div>
                                <div class="settings-desc">隐藏敏感内容</div>
                            </div>
                            <div class="switch active" onclick="toggleSwitch(this)">
                                <div class="switch-handle"></div>
                            </div>
                        </div>
                        <div class="settings-item">
                            <span class="settings-icon">🗑️</span>
                            <div class="settings-content">
                                <div class="settings-title">清空回收站</div>
                                <div class="settings-desc">12条记录</div>
                            </div>
                            <span class="menu-arrow">›</span>
                        </div>
                    </div>
                    
                    <div class="settings-group">
                        <div class="settings-group-title">数据管理</div>
                        <div class="settings-item">
                            <span class="settings-icon">☁️</span>
                            <div class="settings-content">
                                <div class="settings-title">自动备份</div>
                                <div class="settings-desc">每天凌晨2点</div>
                            </div>
                            <div class="switch active" onclick="toggleSwitch(this)">
                                <div class="switch-handle"></div>
                            </div>
                        </div>
                        <div class="settings-item">
                            <span class="settings-icon">📱</span>
                            <div class="settings-content">
                                <div class="settings-title">跨设备同步</div>
                                <div class="settings-desc">通过华为账号</div>
                            </div>
                            <div class="switch active" onclick="toggleSwitch(this)">
                                <div class="switch-handle"></div>
                            </div>
                        </div>
                        <div class="settings-item">
                            <span class="settings-icon">📤</span>
                            <div class="settings-content">
                                <div class="settings-title">导出数据</div>
                                <div class="settings-desc">导出为JSON或PDF</div>
                            </div>
                            <span class="menu-arrow">›</span>
                        </div>
                    </div>
                    
                    <div class="settings-group">
                        <div class="settings-group-title">关于</div>
                        <div class="settings-item">
                            <span class="settings-icon">📖</span>
                            <div class="settings-content">
                                <div class="settings-title">用户协议</div>
                            </div>
                            <span class="menu-arrow">›</span>
                        </div>
                        <div class="settings-item">
                            <span class="settings-icon">🔐</span>
                            <div class="settings-content">
                                <div class="settings-title">隐私政策</div>
                            </div>
                            <span class="menu-arrow">›</span>
                        </div>
                        <div class="settings-item">
                            <span class="settings-icon">ℹ️</span>
                            <div class="settings-content">
                                <div class="settings-title">版本</div>
                                <div class="settings-desc">v1.0.0</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 悬浮按钮 -->
        <div class="fab" id="fab" onclick="alert('新建时光功能开发中...')">✎</div>
        
        <!-- 底部导航 -->
        <div class="bottom-nav">
            <div class="nav-item active" onclick="showPage('timelinePage', 0)">
                <div class="nav-icon active">📖</div>
                <span class="nav-text active">时光流</span>
            </div>
            <div class="nav-item" onclick="showPage('calendarPage', 1)">
                <div class="nav-icon">📅</div>
                <span class="nav-text">日历</span>
            </div>
            <div class="nav-item" onclick="showPage('statsPage', 2)">
                <div class="nav-icon">📊</div>
                <span class="nav-text">统计</span>
            </div>
            <div class="nav-item" onclick="showPage('profilePage', 3)">
                <div class="nav-icon">👤</div>
                <span class="nav-text">我的</span>
            </div>
        </div>
    </div>
    
    <script>
        let currentPage = 'timelinePage';
        
        // 页面切换
        function showPage(pageId, navIndex) {
            // 隐藏所有页面
            document.querySelectorAll('.page').forEach(page => {
                page.classList.remove('active');
            });
            
            // 显示选中页面
            document.getElementById(pageId).classList.add('active');
            
            // 更新导航栏
            document.querySelectorAll('.nav-item').forEach((item, index) => {
                if (index === navIndex) {
                    item.querySelector('.nav-icon').classList.add('active');
                    item.querySelector('.nav-text').classList.add('active');
                } else {
                    item.querySelector('.nav-icon').classList.remove('active');
                    item.querySelector('.nav-text').classList.remove('active');
                }
            });
            
            // 更新页面标题
            const titles = {
                'timelinePage': '时光拾光',
                'calendarPage': '日历视图',
                'statsPage': '统计分析',
                'profilePage': '我的',
                'searchPage': '搜索',
                'capsulePage': '时光胶囊',
                'settingsPage': '设置'
            };
            document.getElementById('pageTitle').textContent = titles[pageId];
            
            // 显示/隐藏悬浮按钮
            const fab = document.getElementById('fab');
            if (pageId === 'timelinePage' || pageId === 'calendarPage') {
                fab.style.display = 'flex';
            } else {
                fab.style.display = 'none';
            }
            
            // 特殊页面的处理
            if (pageId === 'searchPage') {
                // 聚焦搜索框
                setTimeout(() => {
                    document.querySelector('.search-input').focus();
                }, 100);
            }
            
            currentPage = pageId;
        }
        
        // 显示编辑页面
        function showEditPage() {
            alert('编辑功能开发中...');
        }
        
        // 开关切换
        function toggleSwitch(element) {
            element.classList.toggle('active');
            if (element.classList.contains('active')) {
                element.innerHTML = '<div class="switch-handle"></div>';
            } else {
                element.innerHTML = '';
            }
        }
        
        // 分类切换
        document.querySelectorAll('.category-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                document.querySelectorAll('.category-tab').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
            });
        });
        
        // 搜索过滤器
        document.querySelectorAll('.filter-tag').forEach(tag => {
            tag.addEventListener('click', function() {
                document.querySelectorAll('.filter-tag').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
            });
        });
        
        // 搜索历史点击
        document.querySelectorAll('.history-item').forEach(item => {
            item.addEventListener('click', function() {
                const searchText = this.querySelector('.history-text').textContent;
                document.querySelector('.search-input').value = searchText;
            });
        });
    </script>
</body>
</html>