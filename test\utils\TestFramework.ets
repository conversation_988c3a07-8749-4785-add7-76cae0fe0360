// 测试框架配置和基础工具类
import { hilog } from '@kit.PerformanceAnalysisKit';

// 测试结果接口
export interface TestResult {
  name: string;
  status: 'passed' | 'failed' | 'skipped';
  duration: number;
  error?: string;
  message?: string;
}

// 测试套件接口
export interface TestSuite {
  name: string;
  tests: TestResult[];
  totalTests: number;
  passedTests: number;
  failedTests: number;
  skippedTests: number;
  duration: number;
}

// 测试运行器接口
export interface TestRunner {
  name: string;
  run(): Promise<TestSuite>;
}

// 断言工具类
export class Assert {
  static assertTrue(condition: boolean, message?: string): void {
    if (!condition) {
      throw new Error(message || '断言失败: 条件不为真');
    }
  }

  static assertFalse(condition: boolean, message?: string): void {
    if (condition) {
      throw new Error(message || '断言失败: 条件为真');
    }
  }

  static assertEquals(actual: any, expected: any, message?: string): void {
    if (actual !== expected) {
      throw new Error(message || `断言失败: 期望 ${expected}, 实际 ${actual}`);
    }
  }

  static assertNotEquals(actual: any, expected: any, message?: string): void {
    if (actual === expected) {
      throw new Error(message || `断言失败: 不期望 ${expected}, 但实际是 ${actual}`);
    }
  }

  static assertNull(value: any, message?: string): void {
    if (value !== null && value !== undefined) {
      throw new Error(message || `断言失败: 期望 null, 实际 ${value}`);
    }
  }

  static assertNotNull(value: any, message?: string): void {
    if (value === null || value === undefined) {
      throw new Error(message || '断言失败: 期望非 null 值');
    }
  }

  static assertContains(array: any[], item: any, message?: string): void {
    if (!array.includes(item)) {
      throw new Error(message || `断言失败: 数组不包含 ${item}`);
    }
  }

  static assertNotContains(array: any[], item: any, message?: string): void {
    if (array.includes(item)) {
      throw new Error(message || `断言失败: 数组包含 ${item}`);
    }
  }

  static assertInstanceOf(value: any, constructor: Function, message?: string): void {
    if (!(value instanceof constructor)) {
      throw new Error(message || `断言失败: ${value} 不是 ${constructor.name} 的实例`);
    }
  }

  static assertThrows(fn: Function, message?: string): void {
    try {
      fn();
      throw new Error(message || '断言失败: 期望抛出异常，但没有抛出');
    } catch (error) {
      // 期望抛出异常，所以这里是正常的
    }
  }
}

// 测试工具类
export class TestUtils {
  private static performanceData: Map<string, number> = new Map();

  // 测量函数执行时间
  static async measurePerformance<T>(name: string, fn: () => Promise<T>): Promise<T> {
    const startTime = Date.now();
    try {
      const result = await fn();
      const endTime = Date.now();
      const duration = endTime - startTime;
      this.performanceData.set(name, duration);
      hilog.info(0x0000, 'Test', `${name} 执行时间: ${duration}ms`);
      return result;
    } catch (error) {
      const endTime = Date.now();
      const duration = endTime - startTime;
      this.performanceData.set(name, duration);
      hilog.error(0x0000, 'Test', `${name} 执行失败, 耗时: ${duration}ms, 错误: ${error}`);
      throw error;
    }
  }

  // 获取性能数据
  static getPerformanceData(): Map<string, number> {
    return new Map(this.performanceData);
  }

  // 生成测试数据
  static generateTestNote(id: number = 1): any {
    return {
      id: id,
      title: `测试笔记 ${id}`,
      content: `这是测试笔记 ${id} 的内容`,
      categoryId: 1,
      tags: '测试,单元测试',
      mood: 'happy',
      weather: 'sunny',
      location: '测试地点',
      images: '',
      voiceNote: '',
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString(),
      isDeleted: 0
    };
  }

  static generateTestCapsule(id: number = 1): any {
    return {
      id: id,
      title: `测试胶囊 ${id}`,
      content: `这是测试胶囊 ${id} 的内容`,
      openDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
      createTime: new Date().toISOString(),
      isOpened: 0,
      isOpenTime: true
    };
  }

  // 等待指定时间
  static async sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // 清理测试数据
  static async cleanupTestData(databaseService: any): Promise<void> {
    try {
      // 清理测试笔记
      await databaseService.store.executeSql("DELETE FROM notes WHERE title LIKE '测试笔记%'");
      // 清理测试胶囊
      await databaseService.store.executeSql("DELETE FROM capsules WHERE title LIKE '测试胶囊%'");
      hilog.info(0x0000, 'Test', '测试数据清理完成');
    } catch (error) {
      hilog.error(0x0000, 'Test', '清理测试数据失败:', error);
    }
  }
}

// 测试报告生成器
export class TestReportGenerator {
  static generateReport(suites: TestSuite[]): string {
    let report = '=== 测试报告 ===\n\n';
    
    let totalTests = 0;
    let totalPassed = 0;
    let totalFailed = 0;
    let totalSkipped = 0;
    let totalDuration = 0;

    suites.forEach(suite => {
      report += `## ${suite.name}\n`;
      report += `总测试数: ${suite.totalTests}\n`;
      report += `通过: ${suite.passedTests}\n`;
      report += `失败: ${suite.failedTests}\n`;
      report += `跳过: ${suite.skippedTests}\n`;
      report += `耗时: ${suite.duration}ms\n\n`;

      suite.tests.forEach(test => {
        const status = test.status === 'passed' ? '✓' : test.status === 'failed' ? '✗' : '-';
        report += `${status} ${test.name} (${test.duration}ms)\n`;
        if (test.error) {
          report += `  错误: ${test.error}\n`;
        }
      });

      report += '\n';

      totalTests += suite.totalTests;
      totalPassed += suite.passedTests;
      totalFailed += suite.failedTests;
      totalSkipped += suite.skippedTests;
      totalDuration += suite.duration;
    });

    report += '=== 总结 ===\n';
    report += `总测试数: ${totalTests}\n`;
    report += `通过: ${totalPassed}\n`;
    report += `失败: ${totalFailed}\n`;
    report += `跳过: ${totalSkipped}\n`;
    report += `总耗时: ${totalDuration}ms\n`;
    report += `通过率: ${((totalPassed / totalTests) * 100).toFixed(2)}%\n`;

    return report;
  }

  static generatePerformanceReport(performanceData: Map<string, number>): string {
    let report = '=== 性能测试报告 ===\n\n';
    
    const sortedData = Array.from(performanceData.entries())
      .sort((a, b) => b[1] - a[1]);

    sortedData.forEach(([name, duration]) => {
      report += `${name}: ${duration}ms\n`;
      if (duration > 1000) {
        report += `  ⚠️  超过1000ms，需要优化\n`;
      } else if (duration > 500) {
        report += `  ⚠️  超过500ms，建议优化\n`;
      }
    });

    return report;
  }
}

// 模拟数据库服务
export class MockDatabaseService {
  private testData: Map<string, any[]> = new Map();
  
  constructor() {
    // 初始化测试数据
    this.testData.set('notes', []);
    this.testData.set('capsules', []);
    this.testData.set('settings', [{
      id: 1,
      darkMode: 2,
      reminderTime: '21:00',
      reminderEnabled: 1,
      syncEnabled: 0,
      backupEnabled: 0,
      fontSize: 16
    }]);
  }

  async init(context: Context): Promise<void> {
    // 模拟初始化
    await TestUtils.sleep(10);
  }

  async saveNote(note: any): Promise<number> {
    const notes = this.testData.get('notes') || [];
    const id = note.id || notes.length + 1;
    const newNote = { ...note, id, createTime: new Date().toISOString(), updateTime: new Date().toISOString() };
    notes.push(newNote);
    this.testData.set('notes', notes);
    return id;
  }

  async getNotes(offset: number = 0, limit: number = 20): Promise<any[]> {
    const notes = this.testData.get('notes') || [];
    return notes.slice(offset, offset + limit);
  }

  async searchNotes(keyword: string): Promise<any[]> {
    const notes = this.testData.get('notes') || [];
    return notes.filter(note => 
      note.title.includes(keyword) || 
      note.content.includes(keyword) ||
      note.tags.includes(keyword)
    );
  }

  async deleteNote(id: number): Promise<void> {
    const notes = this.testData.get('notes') || [];
    const index = notes.findIndex(note => note.id === id);
    if (index !== -1) {
      notes.splice(index, 1);
      this.testData.set('notes', notes);
    }
  }

  async createCapsule(capsule: any): Promise<number> {
    const capsules = this.testData.get('capsules') || [];
    const id = capsules.length + 1;
    const newCapsule = { ...capsule, id, createTime: new Date().toISOString() };
    capsules.push(newCapsule);
    this.testData.set('capsules', capsules);
    return id;
  }

  async getCapsules(): Promise<any[]> {
    return this.testData.get('capsules') || [];
  }

  async getSettings(): Promise<any> {
    const settings = this.testData.get('settings') || [];
    return settings[0] || null;
  }

  // 清理测试数据
  async cleanup(): Promise<void> {
    this.testData.set('notes', []);
    this.testData.set('capsules', []);
  }
}