import { StateManager, StateObserver, StateChangeEvent } from './StateManager';

/**
 * 状态变化装饰器
 * 用于自动监听状态变化并更新组件
 */
export function StateObserver(observedTypes: string[] = []) {
  return function <T extends { new (...args: any[]): any }>(constructor: T) {
    return class extends constructor implements StateObserver {
      private stateManager: StateManager;
      private componentId: string;
      
      constructor(...args: any[]) {
        super(...args);
        this.stateManager = StateManager.getInstance();
        this.componentId = `component_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        // 注册为状态观察者
        this.stateManager.registerObserver(this.componentId, this);
      }
      
      /**
       * 状态变化回调
       */
      onStateChanged(event: StateChangeEvent): void {
        // 如果没有指定监听类型，则监听所有类型
        if (observedTypes.length === 0 || observedTypes.includes(event.type)) {
          this.handleStateChange(event);
        }
      }
      
      /**
       * 获取监听的状态类型
       */
      getObservedTypes(): string[] {
        return observedTypes;
      }
      
      /**
       * 处理状态变化（子类可重写）
       */
      protected handleStateChange(event: StateChangeEvent): void {
        // 默认实现：触发组件重新渲染
        if (typeof this.aboutToAppear === 'function') {
          this.aboutToAppear();
        }
      }
      
      /**
       * 组件销毁时清理
       */
      aboutToDisappear(): void {
        // 注销状态观察者
        this.stateManager.unregisterObserver(this.componentId);
        
        // 调用父类的销毁方法
        if (super.aboutToDisappear) {
          super.aboutToDisappear();
        }
      }
    };
  };
}

/**
 * 状态选择器装饰器
 * 用于从状态中提取特定数据
 */
export function StateSelector<T>(selector: (state: any) => T) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalGetter = descriptor.get;
    
    descriptor.get = function () {
      const stateManager = StateManager.getInstance();
      const state = stateManager.getState();
      return selector(state);
    };
    
    return descriptor;
  };
}

/**
 * 状态更新装饰器
 * 用于在方法调用后自动更新状态
 */
export function StateUpdate(changeType: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    
    descriptor.value = function (...args: any[]) {
      const result = originalMethod.apply(this, args);
      
      // 获取返回值并更新状态
      if (result && typeof result.then === 'function') {
        // 异步方法
        result.then((data: any) => {
          const stateManager = StateManager.getInstance();
          stateManager.updateState({ [propertyKey]: data }, changeType as any);
        });
      } else {
        // 同步方法
        const stateManager = StateManager.getInstance();
        stateManager.updateState({ [propertyKey]: result }, changeType as any);
      }
      
      return result;
    };
    
    return descriptor;
  };
}

/**
 * 状态计算属性
 * 用于创建计算属性
 */
export function Computed(getter: () => any) {
  return function (target: any, propertyKey: string) {
    let value: any;
    let dirty = true;
    
    Object.defineProperty(target, propertyKey, {
      get() {
        if (dirty) {
          value = getter.call(this);
          dirty = false;
        }
        return value;
      },
      set() {
        // 计算属性不可直接设置
        console.warn(`Computed property '${propertyKey}' is read-only`);
      },
      enumerable: true,
      configurable: true
    });
    
    // 监听状态变化，标记为需要重新计算
    const stateManager = StateManager.getInstance();
    const originalObserver = target.onStateChanged;
    
    target.onStateChanged = function(event: StateChangeEvent) {
      dirty = true;
      if (originalObserver) {
        originalObserver.call(this, event);
      }
    };
  };
}

/**
 * 状态持久化装饰器
 * 用于自动保存和恢复状态
 */
export function PersistState(storageKey: string) {
  return function <T extends { new (...args: any[]): any }>(constructor: T) {
    return class extends constructor {
      constructor(...args: any[]) {
        super(...args);
        this.loadPersistedState();
      }
      
      /**
       * 加载持久化的状态
       */
      private loadPersistedState(): void {
        try {
          const persistedData = localStorage.getItem(storageKey);
          if (persistedData) {
            const state = JSON.parse(persistedData);
            const stateManager = StateManager.getInstance();
            stateManager.restoreFromSnapshot(state);
          }
        } catch (error) {
          console.error('Failed to load persisted state:', error);
        }
      }
      
      /**
       * 保存状态到持久化存储
       */
      protected saveState(): void {
        try {
          const stateManager = StateManager.getInstance();
          const state = stateManager.createSnapshot();
          localStorage.setItem(storageKey, JSON.stringify(state));
        } catch (error) {
          console.error('Failed to save state:', error);
        }
      }
      
      /**
       * 监听状态变化并自动保存
       */
      protected handleStateChange(event: StateChangeEvent): void {
        // 延迟保存以避免频繁写入
        clearTimeout(this.saveTimeout);
        this.saveTimeout = setTimeout(() => {
          this.saveState();
        }, 1000);
        
        // 调用父类的处理方法
        if (super.handleStateChange) {
          super.handleStateChange(event);
        }
      }
      
      private saveTimeout: any = null;
    };
  };
}

/**
 * 状态防抖装饰器
 * 用于减少频繁的状态更新
 */
export function DebounceState(delay: number = 300) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    let timeoutId: any = null;
    
    descriptor.value = function (...args: any[]) {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
      
      timeoutId = setTimeout(() => {
        originalMethod.apply(this, args);
        timeoutId = null;
      }, delay);
    };
    
    return descriptor;
  };
}

/**
 * 状态节流装饰器
 * 用于限制状态更新的频率
 */
export function ThrottleState(delay: number = 300) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    let lastCallTime = 0;
    
    descriptor.value = function (...args: any[]) {
      const now = Date.now();
      if (now - lastCallTime >= delay) {
        lastCallTime = now;
        originalMethod.apply(this, args);
      }
    };
    
    return descriptor;
  };
}

/**
 * 状态验证装饰器
 * 用于验证状态更新的合法性
 */
export function ValidateState(validator: (state: any) => boolean) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    
    descriptor.value = function (...args: any[]) {
      const result = originalMethod.apply(this, args);
      
      if (result && typeof result.then === 'function') {
        // 异步方法
        result.then((data: any) => {
          const stateManager = StateManager.getInstance();
          const state = stateManager.getState();
          if (!validator(state)) {
            console.error('State validation failed');
            // 可以在这里添加恢复逻辑
          }
        });
      } else {
        // 同步方法
        const stateManager = StateManager.getInstance();
        const state = stateManager.getState();
        if (!validator(state)) {
          console.error('State validation failed');
          // 可以在这里添加恢复逻辑
        }
      }
      
      return result;
    };
    
    return descriptor;
  };
}

/**
 * 状态变化订阅工具类
 */
export class StateSubscription {
  private stateManager: StateManager;
  private subscriptionId: string;
  private callback: (event: StateChangeEvent) => void;
  private observedTypes: string[];
  
  constructor(
    callback: (event: StateChangeEvent) => void,
    observedTypes: string[] = []
  ) {
    this.stateManager = StateManager.getInstance();
    this.callback = callback;
    this.observedTypes = observedTypes;
    this.subscriptionId = `subscription_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // 创建临时观察者
    const observer: StateObserver = {
      onStateChanged: (event: StateChangeEvent) => {
        if (observedTypes.length === 0 || observedTypes.includes(event.type)) {
          callback(event);
        }
      },
      getObservedTypes: () => observedTypes
    };
    
    this.stateManager.registerObserver(this.subscriptionId, observer);
  }
  
  /**
   * 取消订阅
   */
  public unsubscribe(): void {
    this.stateManager.unregisterObserver(this.subscriptionId);
  }
}

/**
 * 状态查询工具类
 */
export class StateQuery {
  private stateManager: StateManager;
  
  constructor() {
    this.stateManager = StateManager.getInstance();
  }
  
  /**
   * 查询状态
   */
  public query<T>(selector: (state: any) => T): T {
    const state = this.stateManager.getState();
    return selector(state);
  }
  
  /**
   * 订阅状态变化
   */
  public subscribe<T>(
    selector: (state: any) => T,
    callback: (value: T) => void,
    observedTypes: string[] = []
  ): StateSubscription {
    let lastValue: T | null = null;
    
    const subscription = new StateSubscription((event) => {
      const currentValue = this.query(selector);
      if (currentValue !== lastValue) {
        lastValue = currentValue;
        callback(currentValue);
      }
    }, observedTypes);
    
    // 立即执行一次
    lastValue = this.query(selector);
    callback(lastValue);
    
    return subscription;
  }
  
  /**
   * 创建状态选择器
   */
  public createSelector<T>(selector: (state: any) => T): () => T {
    return () => this.query(selector);
  }
  
  /**
   * 创建记忆化选择器
   */
  public createMemoizedSelector<T>(selector: (state: any) => T): () => T {
    let lastState: any = null;
    let lastValue: T | null = null;
    
    return () => {
      const currentState = this.stateManager.getState();
      if (currentState !== lastState) {
        lastState = currentState;
        lastValue = selector(currentState);
      }
      return lastValue!;
    };
  }
}