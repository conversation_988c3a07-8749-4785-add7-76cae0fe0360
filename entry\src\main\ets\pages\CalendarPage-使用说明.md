# TimeNotes - 日历页面使用说明

## 功能概述

日历页面是TimeNotes应用的核心功能之一，提供了一个直观的月历视图，让用户可以按日期查看和管理笔记。

## 主要功能

### 1. 月份导航
- **左箭头按钮**：切换到上个月
- **右箭头按钮**：切换到下个月
- **月份年份显示**：显示当前查看的月份和年份
- **返回今天按钮**：快速返回到今天的日期（仅在不在当前月时显示）

### 2. 日历网格
- **7×6网格布局**：标准的月历布局，显示完整的月份
- **星期标题**：显示"日、一、二、三、四、五、六"
- **日期显示**：
  - 当前月日期：正常显示
  - 非当前月日期：淡色显示，不可点击
  - 今日日期：使用莫兰迪色系强调色背景显示
  - 选中日期：通过底部笔记列表显示

### 3. 笔记标记
- **有笔记的日期**：在日期下方显示小圆点标记
- **笔记数量**：如果某日有多条笔记，会显示具体的数字
- **标记颜色**：使用莫兰迪色系的强调色

### 4. 笔记列表
- **选中日期的笔记**：点击日期后，底部显示该日的所有笔记
- **笔记数量统计**：标题栏显示选中日期的笔记总数
- **笔记卡片**：显示笔记的时间、标题、内容和标签
- **空状态**：选中日期没有笔记时，显示友好的提示信息

### 5. 动画效果
- **月份切换动画**：平滑的透明度过渡效果
- **交互反馈**：点击日期和按钮时的视觉反馈

## 技术实现

### 数据库集成
- 使用RDB数据库存储笔记数据
- 提供按日期查询笔记的功能
- 支持获取有笔记的日期列表
- 支持获取某日期的笔记数量

### 日期处理
- 扩展了DateUtils工具类，提供丰富的日期处理功能
- 支持月历生成，包括上个月和下个月的部分日期
- 支持月份加减、格式化等操作

### 响应式设计
- 适配不同屏幕尺寸
- 使用Grid布局确保日历的响应式显示
- 支持横屏和竖屏模式

### 莫兰迪色系设计
- 使用预定义的莫兰迪色系颜色
- 今日日期使用强调色背景
- 非当前月日期使用淡色显示
- 保持整体视觉风格的一致性

## 使用场景

### 1. 查看历史笔记
用户可以通过日历快速查看任意日期的笔记，回顾过去的记录。

### 2. 计划未来
通过查看某日是否已有笔记，合理安排时间，避免时间冲突。

### 3. 数据统计
可以直观地看到哪些日期有笔记，了解自己的记录习惯。

### 4. 快速导航
通过返回今天按钮，可以快速回到当前日期，方便用户随时记录。

## 扩展功能

### 1. 搜索功能
可以集成搜索功能，在日历页面直接搜索特定内容的笔记。

### 2. 标签筛选
可以添加标签筛选功能，只显示特定标签的笔记。

### 3. 导出功能
可以添加导出功能，将某月或某段时间的笔记导出为PDF或其他格式。

### 4. 提醒功能
可以为重要日期添加提醒，确保不会错过重要事项。

## 注意事项

1. **性能优化**：月份切换时会重新加载数据，确保数据的实时性
2. **内存管理**：使用Set和Map来管理笔记日期和数量，避免内存泄漏
3. **用户体验**：添加了加载状态和错误处理，确保良好的用户体验
4. **数据同步**：支持离线使用，数据在本地存储

## 文件结构

```
entry/src/main/ets/
├── pages/
│   ├── CalendarPage.ets          # 日历页面组件
│   └── Main.ets                 # 主页面（包含日历页面引用）
├── services/
│   └── DatabaseService.ets      # 数据库服务（已扩展）
├── utils/
│   ├── DateUtils.ets            # 日期工具类（已扩展）
│   └── TestDataGenerator.ets    # 测试数据生成器
└── constants/
    └── MorandiColors.ets        # 莫兰迪色系定义
```

## 测试方法

1. **生成测试数据**：使用TestDataGenerator生成示例笔记数据
2. **功能测试**：测试月份切换、日期选择、笔记显示等功能
3. **性能测试**：测试大量数据下的加载性能
4. **兼容性测试**：在不同设备和屏幕尺寸下测试显示效果

日历页面已经完整实现，具备了所有要求的功能，并且具有良好的用户体验和扩展性。