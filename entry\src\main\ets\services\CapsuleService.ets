import { CapsuleModel } from '../model/NoteModel';
import { DatabaseService } from './DatabaseService';
import { DateUtils } from '../utils/DateUtils';
import { AppConstants } from '../constants/AppConstants';
import { MorandiColors } from '../constants/MorandiColors';

// 时间胶囊服务类
export class CapsuleService {
  private static instance: CapsuleService;
  private dbService = DatabaseService.getInstance();
  
  private constructor() {}
  
  static getInstance(): CapsuleService {
    if (!CapsuleService.instance) {
      CapsuleService.instance = new CapsuleService();
    }
    return CapsuleService.instance;
  }
  
  /**
   * 创建时间胶囊
   */
  async createCapsule(capsule: Omit<CapsuleModel, 'id' | 'createTime' | 'isOpened'>): Promise<number> {
    // 验证开启日期
    if (!this.validateOpenDate(capsule.openDate)) {
      throw new Error('开启日期无效，必须在当前时间之后');
    }
    
    const fullCapsule: CapsuleModel = {
      title: capsule.title,
      content: capsule.content,
      openDate: capsule.openDate,
      createTime: DateUtils.now(),
      isOpened: 0,
      isOpenTime: capsule.isOpenTime
    };
    
    return await this.dbService.createCapsule(fullCapsule);
  }
  
  /**
   * 更新时间胶囊
   */
  async updateCapsule(id: number, updates: Partial<CapsuleModel>): Promise<void> {
    const existingCapsule = await this.getCapsuleById(id);
    
    if (!existingCapsule) {
      throw new Error('时间胶囊不存在');
    }
    
    if (existingCapsule.isOpened) {
      throw new Error('已开启的时间胶囊不能修改');
    }
    
    // 验证开启日期
    if (updates.openDate && !this.validateOpenDate(updates.openDate)) {
      throw new Error('开启日期无效，必须在当前时间之后');
    }
    
    await this.dbService.updateCapsule(id, updates);
  }
  
  /**
   * 删除时间胶囊
   */
  async deleteCapsule(id: number): Promise<void> {
    const capsule = await this.getCapsuleById(id);
    
    if (!capsule) {
      throw new Error('时间胶囊不存在');
    }
    
    await this.dbService.deleteCapsule(id);
  }
  
  /**
   * 开启时间胶囊
   */
  async openCapsule(id: number): Promise<CapsuleModel> {
    const capsule = await this.getCapsuleById(id);
    
    if (!capsule) {
      throw new Error('时间胶囊不存在');
    }
    
    if (capsule.isOpened) {
      throw new Error('时间胶囊已经开启');
    }
    
    const now = new Date();
    const openDate = new Date(capsule.openDate);
    
    if (now < openDate) {
      throw new Error('时间胶囊还未到开启时间');
    }
    
    // 更新胶囊状态
    await this.dbService.updateCapsule(id, { isOpened: 1 });
    
    // 返回更新后的胶囊
    const updatedCapsule = await this.getCapsuleById(id);
    return updatedCapsule!;
  }
  
  /**
   * 获取时间胶囊列表
   */
  async getCapsules(): Promise<CapsuleModel[]> {
    return await this.dbService.getCapsules();
  }
  
  /**
   * 根据ID获取时间胶囊
   */
  async getCapsuleById(id: number): Promise<CapsuleModel | null> {
    const capsules = await this.dbService.getCapsules();
    return capsules.find(capsule => capsule.id === id) || null;
  }
  
  /**
   * 获取已过期可开启的时间胶囊
   */
  async getExpiredCapsules(): Promise<CapsuleModel[]> {
    return await this.dbService.getExpiredCapsules();
  }
  
  /**
   * 获取即将到期的时间胶囊
   */
  async getUpcomingCapsules(days: number = 7): Promise<CapsuleModel[]> {
    return await this.dbService.getUpcomingCapsules(days);
  }
  
  /**
   * 获取时间胶囊统计信息
   */
  async getCapsuleStats(): Promise<{
    total: number;
    opened: number;
    locked: number;
    expired: number;
  }> {
    return await this.dbService.getCapsuleStats();
  }
  
  /**
   * 获取时间胶囊状态
   */
  getCapsuleStatus(capsule: CapsuleModel): {
    isLocked: boolean;
    remainingDays: number;
    canOpen: boolean;
    statusText: string;
    countdownText: string;
  } {
    const now = new Date();
    const openDate = new Date(capsule.openDate);
    const nowTime = now.getTime();
    const openTime = openDate.getTime();
    
    const isLocked = !capsule.isOpened && nowTime < openTime;
    const canOpen = !capsule.isOpened && nowTime >= openTime;
    const remainingDays = Math.ceil((openTime - nowTime) / (1000 * 60 * 60 * 24));
    
    let statusText = '';
    if (capsule.isOpened) {
      statusText = '已开启';
    } else if (canOpen) {
      statusText = '可开启';
    } else if (remainingDays === 0) {
      statusText = '今日可开启';
    } else if (remainingDays === 1) {
      statusText = '明日可开启';
    } else {
      statusText = `${remainingDays}天后可开启`;
    }
    
    const countdownText = DateUtils.getCountdownText(capsule.openDate);
    
    return {
      isLocked,
      remainingDays,
      canOpen,
      statusText,
      countdownText
    };
  }
  
  /**
   * 智能分类时间胶囊
   */
  categorizeCapsule(capsule: CapsuleModel): {
    id: number;
    name: string;
    color: string;
    icon: string;
  } {
    const categories = [
      { id: 1, name: '回忆', color: MorandiColors.categoryLife, icon: '🕰️' },
      { id: 2, name: '目标', color: MorandiColors.categoryWork, icon: '🎯' },
      { id: 3, name: '感悟', color: MorandiColors.categoryStudy, icon: '💭' },
      { id: 4, name: '祝福', color: MorandiColors.categoryPlan, icon: '🌟' },
      { id: 5, name: '秘密', color: MorandiColors.accent, icon: '🔐' }
    ];
    
    const title = capsule.title.toLowerCase();
    const content = capsule.content.toLowerCase();
    
    // 智能分类逻辑
    if (title.includes('目标') || title.includes('计划') || content.includes('目标') || 
        title.includes('目标') || title.includes('计划') || content.includes('计划')) {
      return categories[1]; // 目标
    } else if (title.includes('回忆') || title.includes('过去') || content.includes('回忆') || 
               title.includes('记忆') || content.includes('记忆')) {
      return categories[0]; // 回忆
    } else if (title.includes('感悟') || title.includes('思考') || content.includes('感悟') || 
               title.includes('感想') || content.includes('感想')) {
      return categories[2]; // 感悟
    } else if (title.includes('祝福') || title.includes('希望') || content.includes('祝福') || 
               title.includes('愿望') || content.includes('愿望')) {
      return categories[3]; // 祝福
    } else {
      return categories[4]; // 秘密
    }
  }
  
  /**
   * 验证开启日期
   */
  private validateOpenDate(openDate: string): boolean {
    try {
      const now = new Date();
      const targetDate = new Date(openDate);
      
      // 检查日期格式是否正确
      if (isNaN(targetDate.getTime())) {
        return false;
      }
      
      // 检查日期是否在未来
      if (targetDate <= now) {
        return false;
      }
      
      // 检查是否在允许的时间范围内
      const maxDate = new Date(now.getTime() + AppConstants.CAPSULE_MAX_DAYS * 24 * 60 * 60 * 1000);
      if (targetDate > maxDate) {
        return false;
      }
      
      return true;
    } catch (error) {
      return false;
    }
  }
  
  /**
   * 批量检查并提醒可开启的胶囊
   */
  async checkAndNotifyOpenableCapsules(): Promise<CapsuleModel[]> {
    const expiredCapsules = await this.getExpiredCapsules();
    
    if (expiredCapsules.length > 0) {
      // 这里可以添加通知逻辑
      console.log(`发现 ${expiredCapsules.length} 个可开启的时间胶囊`);
    }
    
    return expiredCapsules;
  }
  
  /**
   * 获取时间胶囊搜索结果
   */
  async searchCapsules(keyword: string): Promise<CapsuleModel[]> {
    const allCapsules = await this.getCapsules();
    const lowerKeyword = keyword.toLowerCase();
    
    return allCapsules.filter(capsule => 
      capsule.title.toLowerCase().includes(lowerKeyword) ||
      capsule.content.toLowerCase().includes(lowerKeyword)
    );
  }
  
  /**
   * 获取按分类统计的胶囊数量
   */
  async getCapsuleStatsByCategory(): Promise<Array<{
    category: { id: number; name: string; color: string; icon: string };
    count: number;
  }>> {
    const capsules = await this.getCapsules();
    const stats = new Map();
    
    capsules.forEach(capsule => {
      const category = this.categorizeCapsule(capsule);
      const key = category.id;
      
      if (stats.has(key)) {
        stats.get(key).count++;
      } else {
        stats.set(key, {
          category,
          count: 1
        });
      }
    });
    
    return Array.from(stats.values());
  }
}