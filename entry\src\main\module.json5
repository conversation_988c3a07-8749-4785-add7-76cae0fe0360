{"module": {"name": "entry", "type": "entry", "description": "$string:module_desc", "mainElement": "EntryAbility", "deviceTypes": ["phone", "tablet", "wearable", "tv", "car"], "deliveryWithInstall": true, "installationFree": false, "pages": "$profile:main_pages", "abilities": [{"name": "EntryAbility", "srcEntry": "./ets/entryability/EntryAbility.ets", "description": "$string:EntryAbility_desc", "icon": "$media:icon", "label": "$string:EntryAbility_label", "startWindowIcon": "$media:icon", "startWindowBackground": "$color:start_window_background", "exported": true, "skills": [{"entities": ["entity.system.home"], "actions": ["action.system.home"]}]}], "requestPermissions": [{"name": "ohos.permission.INTERNET"}, {"name": "ohos.permission.WRITE_USER_STORAGE"}, {"name": "ohos.permission.READ_USER_STORAGE"}, {"name": "ohos.permission.DISTRIBUTED_DATASYNC"}]}}