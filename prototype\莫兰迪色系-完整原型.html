<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时光拾光 - 莫兰迪色系完整原型</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: #F5F2ED;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .phone-container {
            width: 375px;
            height: 812px;
            background: #F8F6F2;
            border-radius: 30px;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(176, 166, 149, 0.2);
            position: relative;
        }

        .status-bar {
            height: 44px;
            background: #F8F6F2;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 24px;
            font-size: 14px;
            color: #A8998A;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            z-index: 100;
        }

        .page {
            display: none;
            height: 100%;
            overflow-y: auto;
            padding-top: 44px;
        }

        .page.active {
            display: block;
        }

        /* 时间轴页面 */
        .timeline-header {
            padding: 20px 24px;
            background: #F8F6F2;
            position: sticky;
            top: 44px;
            z-index: 50;
            border-bottom: 1px solid #E6DDD3;
        }

        .timeline-header h1 {
            font-size: 28px;
            font-weight: 400;
            color: #7D6B83;
            letter-spacing: 1px;
            margin-bottom: 8px;
        }

        .timeline-header .date {
            font-size: 14px;
            color: #B8A391;
            letter-spacing: 0.5px;
        }

        .timeline-content {
            padding: 0 24px;
            padding-bottom: 100px;
        }

        .note-card {
            background: #F0E6DC;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            border: 1px solid #E6DDD3;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .note-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(176, 154, 133, 0.15);
        }

        .note-time {
            font-size: 12px;
            color: #C4B5A0;
            margin-bottom: 8px;
            letter-spacing: 0.5px;
        }

        .note-title {
            font-size: 18px;
            font-weight: 400;
            color: #7D6B83;
            margin-bottom: 8px;
            line-height: 1.4;
        }

        .note-content {
            font-size: 14px;
            color: #9B8A7E;
            line-height: 1.6;
            margin-bottom: 12px;
        }

        .note-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .tag {
            font-size: 11px;
            color: #A8998A;
            background: #E6DDD3;
            padding: 4px 12px;
            border-radius: 16px;
            letter-spacing: 0.5px;
        }

        /* 日历页面 */
        .calendar-page {
            background: #F8F6F2;
        }

        .calendar-header {
            padding: 20px 24px;
            background: #F8F6F2;
            border-bottom: 1px solid #E6DDD3;
        }

        .calendar-header h1 {
            font-size: 24px;
            font-weight: 400;
            color: #7D6B83;
            margin-bottom: 16px;
            text-align: center;
        }

        .month-nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .month-nav button {
            background: none;
            border: none;
            color: #A8998A;
            font-size: 20px;
            cursor: pointer;
            padding: 8px;
        }

        .current-month {
            font-size: 18px;
            color: #7D6B83;
            font-weight: 400;
        }

        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 8px;
            padding: 0 24px;
        }

        .weekday {
            text-align: center;
            font-size: 12px;
            color: #C4B5A0;
            padding: 8px 0;
        }

        .calendar-day {
            aspect-ratio: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            color: #7D6B83;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .calendar-day:hover {
            background: #F0E6DC;
        }

        .calendar-day.has-notes::after {
            content: '';
            position: absolute;
            bottom: 4px;
            left: 50%;
            transform: translateX(-50%);
            width: 4px;
            height: 4px;
            background: #D4A5A5;
            border-radius: 50%;
        }

        .calendar-day.today {
            background: #D4A5A5;
            color: #F8F6F2;
        }

        .calendar-day.selected {
            background: #A8B8A0;
            color: #F8F6F2;
        }

        /* 统计页面 */
        .stats-page {
            background: #F8F6F2;
        }

        .stats-header {
            padding: 20px 24px;
            background: #F8F6F2;
            border-bottom: 1px solid #E6DDD3;
        }

        .stats-header h1 {
            font-size: 24px;
            font-weight: 400;
            color: #7D6B83;
            margin-bottom: 20px;
        }

        .stats-overview {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
            padding: 0 24px;
            margin-bottom: 24px;
        }

        .stat-card {
            background: #F0E6DC;
            border-radius: 16px;
            padding: 20px;
            text-align: center;
            border: 1px solid #E6DDD3;
        }

        .stat-number {
            font-size: 32px;
            font-weight: 300;
            color: #7D6B83;
            margin-bottom: 8px;
        }

        .stat-label {
            font-size: 12px;
            color: #B8A391;
            letter-spacing: 0.5px;
        }

        .chart-container {
            background: #F0E6DC;
            border-radius: 16px;
            padding: 20px;
            margin: 0 24px;
            margin-bottom: 24px;
            border: 1px solid #E6DDD3;
            height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .chart-placeholder {
            color: #C4B5A0;
            font-size: 14px;
        }

        .category-stats {
            padding: 0 24px;
            padding-bottom: 100px;
        }

        .category-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 0;
            border-bottom: 1px solid #E6DDD3;
        }

        .category-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .category-color {
            width: 16px;
            height: 16px;
            border-radius: 50%;
        }

        .category-name {
            font-size: 14px;
            color: #7D6B83;
        }

        .category-count {
            font-size: 14px;
            color: #B8A391;
        }

        /* 搜索页面 */
        .search-page {
            background: #F8F6F2;
        }

        .search-header {
            padding: 20px 24px;
            background: #F8F6F2;
            border-bottom: 1px solid #E6DDD3;
        }

        .search-box {
            background: #F0E6DC;
            border: 1px solid #E6DDD3;
            border-radius: 20px;
            padding: 12px 20px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .search-icon {
            width: 20px;
            height: 20px;
            fill: #C4B5A0;
        }

        .search-input {
            flex: 1;
            border: none;
            background: none;
            outline: none;
            font-size: 16px;
            color: #7D6B83;
        }

        .search-input::placeholder {
            color: #C4B5A0;
        }

        .search-filters {
            display: flex;
            gap: 8px;
            margin-top: 16px;
            overflow-x: auto;
            padding-bottom: 8px;
        }

        .filter-tag {
            background: #E6DDD3;
            border: 1px solid #D4C4B0;
            border-radius: 16px;
            padding: 6px 16px;
            font-size: 12px;
            color: #A8998A;
            white-space: nowrap;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .filter-tag.active {
            background: #D4A5A5;
            color: #F8F6F2;
            border-color: #D4A5A5;
        }

        .search-results {
            padding: 0 24px;
            padding-bottom: 100px;
        }

        /* 时间胶囊页面 */
        .capsule-page {
            background: #F8F6F2;
        }

        .capsule-header {
            padding: 20px 24px;
            background: #F8F6F2;
            border-bottom: 1px solid #E6DDD3;
        }

        .capsule-header h1 {
            font-size: 24px;
            font-weight: 400;
            color: #7D6B83;
            margin-bottom: 8px;
        }

        .capsule-subtitle {
            font-size: 14px;
            color: #B8A391;
            margin-bottom: 20px;
        }

        .create-capsule-btn {
            background: #A5A5D4;
            color: #F8F6F2;
            border: none;
            border-radius: 20px;
            padding: 12px 24px;
            font-size: 14px;
            width: 100%;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .create-capsule-btn:hover {
            background: #9494C4;
        }

        .capsule-list {
            padding: 0 24px;
            padding-bottom: 100px;
        }

        .capsule-card {
            background: #F0E6DC;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            border: 1px solid #E6DDD3;
            position: relative;
            overflow: hidden;
        }

        .capsule-card.locked::before {
            content: '🔒';
            position: absolute;
            top: 20px;
            right: 20px;
            font-size: 20px;
        }

        .capsule-date {
            font-size: 12px;
            color: #C4B5A0;
            margin-bottom: 8px;
        }

        .capsule-title {
            font-size: 16px;
            color: #7D6B83;
            margin-bottom: 8px;
        }

        .capsule-countdown {
            font-size: 14px;
            color: #A5A5D4;
            font-weight: 500;
        }

        /* 设置页面 */
        .settings-page {
            background: #F8F6F2;
        }

        .settings-header {
            padding: 20px 24px;
            background: #F8F6F2;
            border-bottom: 1px solid #E6DDD3;
        }

        .settings-header h1 {
            font-size: 24px;
            font-weight: 400;
            color: #7D6B83;
        }

        .settings-list {
            padding-bottom: 100px;
        }

        .settings-group {
            margin-bottom: 24px;
        }

        .group-title {
            font-size: 12px;
            color: #C4B5A0;
            padding: 16px 24px 8px;
            letter-spacing: 0.5px;
        }

        .setting-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px 24px;
            background: #F8F6F2;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .setting-item:hover {
            background: #F0E6DC;
        }

        .setting-left {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .setting-icon {
            width: 24px;
            height: 24px;
            fill: #A8998A;
        }

        .setting-info {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .setting-title {
            font-size: 16px;
            color: #7D6B83;
        }

        .setting-desc {
            font-size: 12px;
            color: #C4B5A0;
        }

        .setting-right {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .setting-value {
            font-size: 14px;
            color: #B8A391;
        }

        .chevron {
            width: 20px;
            height: 20px;
            fill: #D4C4B0;
        }

        /* 个人中心页面 */
        .profile-page {
            background: #F8F6F2;
        }

        .profile-header {
            background: linear-gradient(180deg, #F0E6DC 0%, #F8F6F2 100%);
            padding: 40px 24px 24px;
            text-align: center;
            border-bottom: 1px solid #E6DDD3;
        }

        .avatar {
            width: 80px;
            height: 80px;
            background: #D4A5A5;
            border-radius: 50%;
            margin: 0 auto 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #F8F6F2;
            font-size: 32px;
        }

        .profile-name {
            font-size: 20px;
            color: #7D6B83;
            margin-bottom: 4px;
        }

        .profile-motto {
            font-size: 14px;
            color: #B8A391;
            font-style: italic;
        }

        .profile-stats {
            display: flex;
            justify-content: space-around;
            padding: 24px;
            border-bottom: 1px solid #E6DDD3;
        }

        .profile-stat {
            text-align: center;
        }

        .stat-value {
            font-size: 24px;
            color: #7D6B83;
            font-weight: 300;
        }

        .stat-name {
            font-size: 12px;
            color: #C4B5A0;
            margin-top: 4px;
        }

        .profile-menu {
            padding: 16px 0;
        }

        /* 底部导航 */
        .bottom-nav {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: #F8F6F2;
            border-top: 1px solid #E6DDD3;
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding-bottom: 20px;
            z-index: 100;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            color: #C4B5A0;
            transition: color 0.3s ease;
            cursor: pointer;
        }

        .nav-item.active {
            color: #D4A5A5;
        }

        .nav-icon {
            width: 24px;
            height: 24px;
            fill: currentColor;
        }

        .nav-text {
            font-size: 10px;
            letter-spacing: 0.5px;
        }

        /* 悬浮按钮 */
        .fab {
            position: absolute;
            bottom: 100px;
            right: 24px;
            width: 56px;
            height: 56px;
            background: #D4A5A5;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 8px 20px rgba(212, 165, 165, 0.3);
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 90;
        }

        .fab:hover {
            transform: scale(1.05);
            box-shadow: 0 12px 28px rgba(212, 165, 165, 0.4);
        }

        .fab-icon {
            width: 24px;
            height: 24px;
            fill: #F8F6F2;
        }

        /* 颜色定义 */
        .category-work { background: #A8B8A0; }
        .category-life { background: #D4A5A5; }
        .category-study { background: #A5A5D4; }
        .category-plan { background: #D4C4A0; }

        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 4px;
        }

        ::-webkit-scrollbar-track {
            background: #F8F6F2;
        }

        ::-webkit-scrollbar-thumb {
            background: #E6DDD3;
            border-radius: 2px;
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <div class="status-bar">
            <span>9:41</span>
            <span>●●●●●</span>
            <span>100%</span>
        </div>

        <!-- 时间轴页面 -->
        <div class="page active" id="timeline-page">
            <div class="timeline-header">
                <h1>时光拾光</h1>
                <div class="date">2024年3月15日 星期五</div>
            </div>
            <div class="timeline-content">
                <div class="note-card">
                    <div class="note-time">下午 2:30</div>
                    <div class="note-title">春日随笔</div>
                    <div class="note-content">今天在公园里看到了第一朵樱花，春天真的来了。阳光透过花瓣洒在地面上，形成斑驳的光影。</div>
                    <div class="note-tags">
                        <span class="tag">生活感悟</span>
                        <span class="tag">春天</span>
                    </div>
                </div>
                
                <div class="note-card">
                    <div class="note-time">上午 10:15</div>
                    <div class="note-title">项目会议纪要</div>
                    <div class="note-content">讨论了新产品的设计方案，确定了以用户体验为核心的设计理念。需要在下周完成原型设计。</div>
                    <div class="note-tags">
                        <span class="tag">工作</span>
                        <span class="tag">会议</span>
                    </div>
                </div>
                
                <div class="note-card">
                    <div class="note-time">昨天 晚上 8:45</div>
                    <div class="note-title">读书笔记</div>
                    <div class="note-content">《设计心理学》第三章：好的设计应该是直观的，用户不需要思考就能知道如何使用。</div>
                    <div class="note-tags">
                        <span class="tag">学习</span>
                        <span class="tag">设计</span>
                    </div>
                </div>
                
                <div class="note-card">
                    <div class="note-time">3月13日 下午 4:20</div>
                    <div class="note-title">周末计划</div>
                    <div class="note-content">1. 去美术馆看展 2. 整理房间 3. 给父母打电话 4. 准备下周的演讲稿</div>
                    <div class="note-tags">
                        <span class="tag">计划</span>
                        <span class="tag">周末</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 日历页面 -->
        <div class="page calendar-page" id="calendar-page">
            <div class="calendar-header">
                <div class="month-nav">
                    <button>&lt;</button>
                    <div class="current-month">2024年3月</div>
                    <button>&gt;</button>
                </div>
                <div class="calendar-grid">
                    <div class="weekday">日</div>
                    <div class="weekday">一</div>
                    <div class="weekday">二</div>
                    <div class="weekday">三</div>
                    <div class="weekday">四</div>
                    <div class="weekday">五</div>
                    <div class="weekday">六</div>
                    
                    <div class="calendar-day"></div>
                    <div class="calendar-day"></div>
                    <div class="calendar-day"></div>
                    <div class="calendar-day"></div>
                    <div class="calendar-day"></div>
                    <div class="calendar-day">1</div>
                    <div class="calendar-day">2</div>
                    <div class="calendar-day">3</div>
                    <div class="calendar-day">4</div>
                    <div class="calendar-day">5</div>
                    <div class="calendar-day has-notes">6</div>
                    <div class="calendar-day">7</div>
                    <div class="calendar-day">8</div>
                    <div class="calendar-day">9</div>
                    <div class="calendar-day">10</div>
                    <div class="calendar-day">11</div>
                    <div class="calendar-day has-notes">12</div>
                    <div class="calendar-day has-notes">13</div>
                    <div class="calendar-day">14</div>
                    <div class="calendar-day today">15</div>
                    <div class="calendar-day">16</div>
                    <div class="calendar-day">17</div>
                    <div class="calendar-day">18</div>
                    <div class="calendar-day">19</div>
                    <div class="calendar-day">20</div>
                    <div class="calendar-day">21</div>
                    <div class="calendar-day">22</div>
                    <div class="calendar-day">23</div>
                    <div class="calendar-day">24</div>
                    <div class="calendar-day">25</div>
                    <div class="calendar-day">26</div>
                    <div class="calendar-day">27</div>
                    <div class="calendar-day">28</div>
                    <div class="calendar-day">29</div>
                    <div class="calendar-day">30</div>
                    <div class="calendar-day">31</div>
                </div>
            </div>
            <div class="timeline-content">
                <div class="note-card">
                    <div class="note-time">3月15日 今天</div>
                    <div class="note-title">今日记录</div>
                    <div class="note-content">共2条记录</div>
                </div>
            </div>
        </div>

        <!-- 统计页面 -->
        <div class="page stats-page" id="stats-page">
            <div class="stats-header">
                <h1>统计</h1>
            </div>
            <div class="stats-overview">
                <div class="stat-card">
                    <div class="stat-number">128</div>
                    <div class="stat-label">总笔记数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">30</div>
                    <div class="stat-label">连续记录</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">4</div>
                    <div class="stat-label">分类数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">92%</div>
                    <div class="stat-label">完成率</div>
                </div>
            </div>
            <div class="chart-container">
                <div class="chart-placeholder">本月记录趋势图</div>
            </div>
            <div class="category-stats">
                <div class="category-item">
                    <div class="category-info">
                        <div class="category-color category-life"></div>
                        <div class="category-name">生活感悟</div>
                    </div>
                    <div class="category-count">45条</div>
                </div>
                <div class="category-item">
                    <div class="category-info">
                        <div class="category-color category-work"></div>
                        <div class="category-name">工作</div>
                    </div>
                    <div class="category-count">38条</div>
                </div>
                <div class="category-item">
                    <div class="category-info">
                        <div class="category-color category-study"></div>
                        <div class="category-name">学习</div>
                    </div>
                    <div class="category-count">28条</div>
                </div>
                <div class="category-item">
                    <div class="category-info">
                        <div class="category-color category-plan"></div>
                        <div class="category-name">计划</div>
                    </div>
                    <div class="category-count">17条</div>
                </div>
            </div>
        </div>

        <!-- 个人中心页面 -->
        <div class="page profile-page" id="profile-page">
            <div class="profile-header">
                <div class="avatar">张</div>
                <div class="profile-name">张三</div>
                <div class="profile-motto">记录生活，留住时光</div>
            </div>
            <div class="profile-stats">
                <div class="profile-stat">
                    <div class="stat-value">128</div>
                    <div class="stat-name">笔记</div>
                </div>
                <div class="profile-stat">
                    <div class="stat-value">30</div>
                    <div class="stat-name">天数</div>
                </div>
                <div class="profile-stat">
                    <div class="stat-value">15</div>
                    <div class="stat-name">胶囊</div>
                </div>
            </div>
            <div class="profile-menu">
                <div class="setting-item">
                    <div class="setting-left">
                        <svg class="setting-icon" viewBox="0 0 24 24">
                            <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                        </svg>
                        <div class="setting-info">
                            <div class="setting-title">个人资料</div>
                        </div>
                    </div>
                    <svg class="chevron" viewBox="0 0 24 24">
                        <path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"/>
                    </svg>
                </div>
                <div class="setting-item">
                    <div class="setting-left">
                        <svg class="setting-icon" viewBox="0 0 24 24">
                            <path d="M19.14 12.94c.04-.3.06-.61.06-.94 0-.32-.02-.64-.07-.94l2.03-1.58c.18-.14.23-.41.12-.61l-1.92-3.32c-.12-.22-.37-.29-.59-.22l-2.39.96c-.5-.38-1.03-.7-1.62-.94l-.36-2.54c-.04-.24-.24-.41-.48-.41h-3.84c-.24 0-.43.17-.47.41l-.36 2.54c-.59.24-1.13.57-1.62.94l-2.39-.96c-.22-.08-.47 0-.59.22L2.74 8.87c-.12.21-.08.47.12.61l2.03 1.58c-.05.3-.09.63-.09.94s.02.64.07.94l-2.03 1.58c-.18.14-.23.41-.12.61l1.92 3.32c.12.22.37.29.59.22l2.39-.96c.5.38 1.03.7 1.62.94l.36 2.54c.05.24.24.41.48.41h3.84c.24 0 .44-.17.47-.41l.36-2.54c.59-.24 1.13-.56 1.62-.94l2.39.96c.22.08.47 0 .59-.22l1.92-3.32c.12-.22.07-.47-.12-.61l-2.01-1.58zM12 15.6c-1.98 0-3.6-1.62-3.6-3.6s1.62-3.6 3.6-3.6 3.6 1.62 3.6 3.6-1.62 3.6-3.6 3.6z"/>
                        </svg>
                        <div class="setting-info">
                            <div class="setting-title">设置</div>
                        </div>
                    </div>
                    <svg class="chevron" viewBox="0 0 24 24">
                        <path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"/>
                    </svg>
                </div>
            </div>
        </div>

        <!-- 搜索页面 -->
        <div class="page search-page" id="search-page">
            <div class="search-header">
                <div class="search-box">
                    <svg class="search-icon" viewBox="0 0 24 24">
                        <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                    </svg>
                    <input type="text" class="search-input" placeholder="搜索笔记...">
                </div>
                <div class="search-filters">
                    <div class="filter-tag active">全部</div>
                    <div class="filter-tag">生活感悟</div>
                    <div class="filter-tag">工作</div>
                    <div class="filter-tag">学习</div>
                    <div class="filter-tag">计划</div>
                </div>
            </div>
            <div class="search-results">
                <div class="note-card">
                    <div class="note-time">3月15日 下午 2:30</div>
                    <div class="note-title">春日随笔</div>
                    <div class="note-content">今天在公园里看到了第一朵<span style="color: #D4A5A5;">樱花</span>，春天真的来了。</div>
                    <div class="note-tags">
                        <span class="tag">生活感悟</span>
                        <span class="tag">春天</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 时间胶囊页面 -->
        <div class="page capsule-page" id="capsule-page">
            <div class="capsule-header">
                <h1>时间胶囊</h1>
                <div class="capsule-subtitle">给未来的自己写一封信</div>
                <button class="create-capsule-btn">创建新的时间胶囊</button>
            </div>
            <div class="capsule-list">
                <div class="capsule-card locked">
                    <div class="capsule-date">创建于 2024年1月1日</div>
                    <div class="capsule-title">给一年后的自己</div>
                    <div class="capsule-countdown">还有 283 天开启</div>
                </div>
                <div class="capsule-card">
                    <div class="capsule-date">创建于 2023年12月25日</div>
                    <div class="capsule-title">2023年的总结</div>
                    <div class="capsule-countdown">已过期，可以开启</div>
                </div>
            </div>
        </div>

        <!-- 设置页面 -->
        <div class="page settings-page" id="settings-page">
            <div class="settings-header">
                <h1>设置</h1>
            </div>
            <div class="settings-list">
                <div class="settings-group">
                    <div class="group-title">通用</div>
                    <div class="setting-item">
                        <div class="setting-left">
                            <svg class="setting-icon" viewBox="0 0 24 24">
                                <path d="M12 3c-4.97 0-9 4.03-9 9s4.03 9 9 9 9-4.03 9-9c0-.46-.04-.92-.1-1.36-.98 1.37-2.58 2.26-4.4 2.26-2.98 0-5.4-2.42-5.4-5.4 0-1.81.89-3.42 2.26-4.4-.44-.06-.9-.1-1.36-.1z"/>
                            </svg>
                            <div class="setting-info">
                                <div class="setting-title">外观</div>
                                <div class="setting-desc">深色模式</div>
                            </div>
                        </div>
                        <div class="setting-right">
                            <div class="setting-value">跟随系统</div>
                            <svg class="chevron" viewBox="0 0 24 24">
                                <path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"/>
                            </svg>
                        </div>
                    </div>
                    <div class="setting-item">
                        <div class="setting-left">
                            <svg class="setting-icon" viewBox="0 0 24 24">
                                <path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"/>
                            </svg>
                            <div class="setting-info">
                                <div class="setting-title">提醒</div>
                                <div class="setting-desc">每日记录提醒</div>
                            </div>
                        </div>
                        <div class="setting-right">
                            <div class="setting-value">21:00</div>
                            <svg class="chevron" viewBox="0 0 24 24">
                                <path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"/>
                            </svg>
                        </div>
                    </div>
                </div>
                
                <div class="settings-group">
                    <div class="group-title">数据</div>
                    <div class="setting-item">
                        <div class="setting-left">
                            <svg class="setting-icon" viewBox="0 0 24 24">
                                <path d="M19.35 10.04C18.67 6.59 15.64 4 12 4 9.11 4 6.6 5.64 5.35 8.04 2.34 8.36 0 10.91 0 14c0 3.31 2.69 6 6 6h13c2.76 0 5-2.24 5-5 0-2.64-2.05-4.78-4.65-4.96zM14 13v4h-4v-4H7l5-5 5 5h-3z"/>
                            </svg>
                            <div class="setting-info">
                                <div class="setting-title">备份与恢复</div>
                            </div>
                        </div>
                        <svg class="chevron" viewBox="0 0 24 24">
                            <path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"/>
                        </svg>
                    </div>
                    <div class="setting-item">
                        <div class="setting-left">
                            <svg class="setting-icon" viewBox="0 0 24 24">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                            </svg>
                            <div class="setting-info">
                                <div class="setting-title">数据同步</div>
                                <div class="setting-desc">已开启</div>
                            </div>
                        </div>
                        <svg class="chevron" viewBox="0 0 24 24">
                            <path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"/>
                        </svg>
                    </div>
                </div>
                
                <div class="settings-group">
                    <div class="group-title">关于</div>
                    <div class="setting-item">
                        <div class="setting-left">
                            <svg class="setting-icon" viewBox="0 0 24 24">
                                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                            </svg>
                            <div class="setting-info">
                                <div class="setting-title">给我们评分</div>
                            </div>
                        </div>
                        <svg class="chevron" viewBox="0 0 24 24">
                            <path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"/>
                        </svg>
                    </div>
                    <div class="setting-item">
                        <div class="setting-left">
                            <svg class="setting-icon" viewBox="0 0 24 24">
                                <path d="M11 17h2v-6h-2v6zm1-15C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zM11 9h2V7h-2v2z"/>
                            </svg>
                            <div class="setting-info">
                                <div class="setting-title">关于时光拾光</div>
                                <div class="setting-desc">版本 1.0.0</div>
                            </div>
                        </div>
                        <svg class="chevron" viewBox="0 0 24 24">
                            <path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"/>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部导航 -->
        <div class="bottom-nav">
            <div class="nav-item active" onclick="switchPage('timeline')">
                <svg class="nav-icon" viewBox="0 0 24 24">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
                <span class="nav-text">时间轴</span>
            </div>
            <div class="nav-item" onclick="switchPage('calendar')">
                <svg class="nav-icon" viewBox="0 0 24 24">
                    <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"/>
                </svg>
                <span class="nav-text">日历</span>
            </div>
            <div class="nav-item" onclick="switchPage('stats')">
                <svg class="nav-icon" viewBox="0 0 24 24">
                    <path d="M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6z"/>
                </svg>
                <span class="nav-text">统计</span>
            </div>
            <div class="nav-item" onclick="switchPage('profile')">
                <svg class="nav-icon" viewBox="0 0 24 24">
                    <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                </svg>
                <span class="nav-text">我的</span>
            </div>
        </div>

        <!-- 悬浮按钮 -->
        <div class="fab" onclick="showMoreMenu()">
            <svg class="fab-icon" viewBox="0 0 24 24">
                <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
            </svg>
        </div>
    </div>

    <script>
        // 页面切换功能
        function switchPage(pageName) {
            // 隐藏所有页面
            const pages = document.querySelectorAll('.page');
            pages.forEach(page => page.classList.remove('active'));
            
            // 显示选中的页面
            const selectedPage = document.getElementById(pageName + '-page');
            if (selectedPage) {
                selectedPage.classList.add('active');
            }
            
            // 更新导航栏激活状态
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => item.classList.remove('active'));
            event.currentTarget.classList.add('active');
            
            // 如果切换到个人中心，隐藏悬浮按钮
            const fab = document.querySelector('.fab');
            if (pageName === 'profile') {
                fab.style.display = 'none';
            } else {
                fab.style.display = 'flex';
            }
        }

        // 显示更多菜单（搜索、时间胶囊、设置）
        function showMoreMenu() {
            // 这里可以显示一个底部弹窗菜单
            const menu = confirm('选择功能：\n确定 - 搜索页面\n取消 - 时间胶囊页面');
            if (menu) {
                switchPage('search');
            } else {
                switchPage('capsule');
            }
        }

        // 添加点击笔记卡片的功能
        document.querySelectorAll('.note-card').forEach(card => {
            card.addEventListener('click', function() {
                alert('查看笔记详情');
            });
        });

        // 添加设置页面跳转
        document.querySelector('.profile-page .setting-item:nth-child(2)').addEventListener('click', function() {
            switchPage('settings');
        });
    </script>
</body>
</html>