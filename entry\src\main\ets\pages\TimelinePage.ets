import { DatabaseService } from '../services/DatabaseService';
import { NoteModel } from '../model/NoteModel';
import { DateUtils } from '../utils/DateUtils';
import { MorandiColors } from '../constants/MorandiColors';
import { MyCard } from '../components/Basic/MyCard';
import { RouterUtil } from '../utils/RouterUtil';

@Component
struct TimelinePage {
  @State notes: NoteModel[] = [];
  @State isLoading: boolean = true;
  @State searchKeyword: string = '';
  @State currentPage: number = 1;
  @State hasMore: boolean = true;
  @State isRefreshing: boolean = false;
  @State screenWidth: number = 0;
  @State isTablet: boolean = false;
  @State paddingSize: number = 20;
  
  private databaseService = DatabaseService.getInstance();
  private context = getContext(this) as Context;

  aboutToAppear() {
    this.initDatabase();
    this.loadNotes();
  }

  /**
   * 初始化数据库
   */
  private async initDatabase() {
    try {
      await this.databaseService.init(this.context);
    } catch (error) {
      console.error('数据库初始化失败:', error);
    }
  }

  /**
   * 加载笔记数据
   */
  private async loadNotes(refresh: boolean = false) {
    if (refresh) {
      this.currentPage = 1;
      this.notes = [];
      this.hasMore = true;
    }

    if (!this.hasMore && !refresh) return;

    try {
      const offset = (this.currentPage - 1) * 20;
      const newNotes = await this.databaseService.getNotes(offset, 20);
      
      if (refresh) {
        this.notes = newNotes;
      } else {
        this.notes = [...this.notes, ...newNotes];
      }
      
      this.hasMore = newNotes.length === 20;
      this.currentPage++;
      
    } catch (error) {
      console.error('加载笔记失败:', error);
    } finally {
      this.isLoading = false;
      this.isRefreshing = false;
    }
  }

  /**
   * 搜索笔记
   */
  private async searchNotes() {
    if (!this.searchKeyword.trim()) {
      this.loadNotes(true);
      return;
    }

    try {
      this.isLoading = true;
      const searchResults = await this.databaseService.searchNotes(this.searchKeyword);
      this.notes = searchResults;
    } catch (error) {
      console.error('搜索笔记失败:', error);
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * 下拉刷新
   */
  private onRefresh() {
    this.isRefreshing = true;
    this.loadNotes(true);
  }

  /**
   * 创建新笔记
   */
  private createNewNote() {
    // 导航到笔记编辑页面
    console.log('创建新笔记');
  }

  /**
   * 点击笔记卡片
   */
  private onNoteClick(note: NoteModel) {
    // 导航到笔记详情页面
    console.log('点击笔记:', note.title);
  }

  /**
   * 根据屏幕尺寸调整布局
   */
  private adjustLayoutForScreenSize(width: number) {
    this.screenWidth = width;
    this.isTablet = width >= 600; // 平板设备判断
    this.paddingSize = this.isTablet ? 32 : 20; // 平板使用更大的内边距
  }

  build() {
    Column() {
      // 顶部栏
      this.HeaderBar();
      
      // 内容区域
      if (this.isLoading && this.notes.length === 0) {
        this.LoadingView();
      } else if (this.notes.length === 0) {
        this.EmptyView();
      } else {
        this.NotesList();
      }
      
      // 悬浮按钮
      this.FloatingButton();
    }
    .width('100%')
    .height('100%')
    .backgroundColor(MorandiColors.background)
    .onSizeChange((oldSize: Size, newSize: Size) => {
      // 响应式布局调整
      this.adjustLayoutForScreenSize(newSize.width);
    });
  }

  /**
   * 顶部栏组件
   */
  @Builder HeaderBar() {
    Column() {
      // 应用标题和日期
      Row() {
        Column() {
          Text('TimeNotes')
            .fontSize(this.isTablet ? 28 : 24)
            .fontWeight(600)
            .fontColor(MorandiColors.textPrimary);
          
          Text(`${DateUtils.formatDate(new Date(), 'YYYY年MM月DD日')} ${DateUtils.getWeekday(DateUtils.today())}`)
            .fontSize(this.isTablet ? 16 : 14)
            .fontColor(MorandiColors.textTertiary)
            .margin({ top: 4 });
        }
        .alignItems(HorizontalAlign.Start);
        
        Blank();
        
        // 搜索按钮
        Row() {
          Image($r('app.media.ic_search'))
            .width(this.isTablet ? 24 : 20)
            .height(this.isTablet ? 24 : 20)
            .fillColor(MorandiColors.textTertiary);
        }
        .width(this.isTablet ? 48 : 40)
        .height(this.isTablet ? 48 : 40)
        .borderRadius(this.isTablet ? 24 : 20)
        .backgroundColor(MorandiColors.cardBackground)
        .justifyContent(FlexAlign.Center)
        .onClick(() => {
          // 显示搜索框
          console.log('显示搜索');
        });
      }
      .width('100%')
      .padding({ 
        left: this.paddingSize, 
        right: this.paddingSize, 
        top: this.isTablet ? 16 : 12, 
        bottom: this.isTablet ? 16 : 12 
      });
      
      // 搜索框（可扩展为显示/隐藏）
      if (this.searchKeyword.length > 0) {
        Row() {
          TextInput({ placeholder: '搜索笔记...' })
            .width('100%')
            .height(this.isTablet ? 48 : 40)
            .backgroundColor(MorandiColors.cardBackground)
            .borderRadius(this.isTablet ? 24 : 20)
            .padding({ left: 16, right: 16 })
            .fontColor(MorandiColors.textPrimary)
            .placeholderColor(MorandiColors.textHint)
            .onChange((value: string) => {
              this.searchKeyword = value;
            })
            .onSubmit(() => {
              this.searchNotes();
            });
        }
        .width('100%')
        .padding({ 
          left: this.paddingSize, 
          right: this.paddingSize, 
          bottom: this.isTablet ? 16 : 12 
        });
      }
    }
    .width('100%')
    .backgroundColor(MorandiColors.background);
  }

  /**
   * 加载视图
   */
  @Builder LoadingView() {
    Column() {
      LoadingProgress()
        .width(40)
        .height(40)
        .color(MorandiColors.primary);
      
      Text('正在加载笔记...')
        .fontSize(16)
        .fontColor(MorandiColors.textTertiary)
        .margin({ top: 16 });
    }
    .width('100%')
    .height('100%')
    .justifyContent(FlexAlign.Center);
  }

  /**
   * 空视图
   */
  @Builder EmptyView() {
    Column() {
      Image($r('app.media.ic_empty'))
        .width(120)
        .height(120)
        .fillColor(MorandiColors.textTertiary)
        .opacity(0.3);
      
      Text('还没有笔记')
        .fontSize(18)
        .fontColor(MorandiColors.textTertiary)
        .margin({ top: 24 });
      
      Text('点击右下角按钮开始记录你的时光')
        .fontSize(14)
        .fontColor(MorandiColors.textHint)
        .margin({ top: 8 });
    }
    .width('100%')
    .height('100%')
    .justifyContent(FlexAlign.Center);
  }

  /**
   * 笔记列表
   */
  @Builder NotesList() {
    Refresh({ refreshing: $$this.isRefreshing, friction: 100 }) {
      List() {
        ForEach(this.notes, (note: NoteModel, index: number) => {
          ListItem() {
            this.NoteCard(note);
          }
          .padding({ 
            left: this.paddingSize, 
            right: this.paddingSize, 
            top: this.isTablet ? 12 : 8, 
            bottom: this.isTablet ? 12 : 8 
          });
        });
      }
      .width('100%')
      .layoutWeight(1)
      .listDirection(Axis.Vertical)
      .edgeEffect(EdgeEffect.Spring)
      .onReachEnd(() => {
        if (!this.isLoading && this.hasMore) {
          this.loadNotes();
        }
      });
    }
    .onRefreshing(() => {
      this.onRefresh();
    });
  }

  /**
   * 笔记卡片
   */
  @Builder NoteCard(note: NoteModel) {
    MyCard({
      title: note.title,
      subtitle: DateUtils.getFriendlyTime(note.createTime),
      clickable: true,
      onClick: () => this.onNoteClick(note)
    }) {
      Column() {
        // 内容预览
        Text(note.content)
          .fontSize(this.isTablet ? 18 : 16)
          .fontColor(MorandiColors.textSecondary)
          .maxLines(this.isTablet ? 4 : 3)
          .textOverflow({ overflow: TextOverflow.Ellipsis })
          .textAlign(TextAlign.Start)
          .width('100%')
          .lineHeight(this.isTablet ? 28 : 24);
        
        // 标签
        if (note.tags && note.tags.trim()) {
          Row() {
            ForEach(note.tags.split(','), (tag: string) => {
              if (tag.trim()) {
                Text(tag.trim())
                  .fontSize(this.isTablet ? 14 : 12)
                  .fontColor(MorandiColors.textTertiary)
                  .padding({ 
                    left: this.isTablet ? 10 : 8, 
                    right: this.isTablet ? 10 : 8, 
                    top: this.isTablet ? 6 : 4, 
                    bottom: this.isTablet ? 6 : 4 
                  })
                  .backgroundColor(MorandiColors.cardBackground)
                  .borderRadius(this.isTablet ? 14 : 12)
                  .border({ width: 1, color: MorandiColors.border });
              }
            });
          }
          .width('100%')
          .margin({ top: this.isTablet ? 16 : 12 })
          .wrapContent(WrapAlignment.Start);
        }
        
        // 时间信息
        Row() {
          Text(DateUtils.formatDate(new Date(note.createTime), 'HH:mm'))
            .fontSize(this.isTablet ? 14 : 12)
            .fontColor(MorandiColors.textHint);
          
          Blank();
          
          // 分类标识（根据categoryId显示不同颜色）
          Row() {
            Text(this.getCategoryName(note.categoryId))
              .fontSize(this.isTablet ? 14 : 12)
              .fontColor(MorandiColors.textTertiary);
          }
          .padding({ 
            left: this.isTablet ? 10 : 8, 
            right: this.isTablet ? 10 : 8, 
            top: this.isTablet ? 4 : 2, 
            bottom: this.isTablet ? 4 : 2 
          })
          .backgroundColor(this.getCategoryColor(note.categoryId))
          .borderRadius(this.isTablet ? 10 : 8);
        }
        .width('100%')
        .margin({ top: this.isTablet ? 16 : 12 });
      }
      .width('100%');
    }
    .onHover((isHover: boolean) => {
      // 悬浮效果通过状态管理实现
    })
    .animation({
      duration: 200,
      curve: Curve.EaseInOut,
      delay: 0,
      iterations: 1,
      playMode: PlayMode.Normal
    });
  }

  /**
   * 悬浮按钮
   */
  @Builder FloatingButton() {
    Stack() {
      Button() {
        Row() {
          Image($r('app.media.ic_add'))
            .width(this.isTablet ? 28 : 24)
            .height(this.isTablet ? 28 : 24)
            .fillColor(Color.White)
            .rotate({ angle: 0 });
        }
        .width(this.isTablet ? 64 : 56)
        .height(this.isTablet ? 64 : 56)
        .borderRadius(this.isTablet ? 32 : 28)
        .backgroundColor(MorandiColors.primary)
        .shadow({
          radius: this.isTablet ? 16 : 12,
          color: MorandiColors.shadow,
          offsetX: 0,
          offsetY: this.isTablet ? 8 : 6
        })
        .onClick(() => {
          this.createNewNote();
        });
      }
      .position({ x: this.isTablet ? '88%' : '85%', y: this.isTablet ? '88%' : '85%' })
      .zIndex(100)
      .onHover((isHover: boolean) => {
        // 悬浮效果
      })
      .animation({
        duration: 300,
        curve: Curve.EaseInOut,
        delay: 0,
        iterations: 1,
        playMode: PlayMode.Normal
      });
    }
    .width('100%')
    .height('100%')
    .clip(false);
  }

  /**
   * 获取分类名称
   */
  private getCategoryName(categoryId: number): string {
    const categories = [
      { id: 1, name: '工作' },
      { id: 2, name: '生活' },
      { id: 3, name: '学习' },
      { id: 4, name: '计划' }
    ];
    const category = categories.find(c => c.id === categoryId);
    return category ? category.name : '其他';
  }

  /**
   * 获取分类颜色
   */
  private getCategoryColor(categoryId: number): string {
    switch (categoryId) {
      case 1: return MorandiColors.categoryWork;
      case 2: return MorandiColors.categoryLife;
      case 3: return MorandiColors.categoryStudy;
      case 4: return MorandiColors.categoryPlan;
      default: return MorandiColors.cardBackground;
    }
  }
}