import { MorandiColors } from '../constants/MorandiColors';

@Entry
@Component
struct Index {
  @State message: string = 'TimeNotes';
  @State currentTime: string = this.getCurrentTime();

  aboutToAppear() {
    // 更新当前时间
    setInterval(() => {
      this.currentTime = this.getCurrentTime();
    }, 1000);
  }

  build() {
    Row() {
      Column() {
        Text(this.message)
          .id('HelloWorld')
          .fontSize(50)
          .fontWeight(FontWeight.Bold)
          .fontColor(MorandiColors.primary)
        
        Text(this.currentTime)
          .fontSize(20)
          .fontColor(MorandiColors.textSecondary)
          .margin({ top: 20 })
        
        Divider().margin({ vertical: 30 })
        
        Text('项目正在开发中...')
          .fontSize(16)
          .fontColor(MorandiColors.textTertiary)
      }
      .width('100%')
    }
    .height('100%')
    .backgroundColor(MorandiColors.background)
  }

  /**
   * 获取当前时间字符串
   */
  private getCurrentTime(): string {
    const date = new Date();
    return `${date.getFullYear()}-${this.padZero(date.getMonth() + 1)}-${this.padZero(date.getDate())} ${this.padZero(date.getHours())}:${this.padZero(date.getMinutes())}:${this.padZero(date.getSeconds())}`;
  }

  /**
   * 数字补零
   */
  private padZero(num: number): string {
    return num < 10 ? `0${num}` : `${num}`;
  }
}