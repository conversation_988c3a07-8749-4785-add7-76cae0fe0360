// 工具类单元测试
import { DateUtils } from '../../utils/DateUtils';
import { TestUtils, Assert, TestResult } from '../utils/TestFramework';
import { hilog } from '@kit.PerformanceAnalysisKit';

export class UtilsTest {
  private name = 'UtilsTest';
  private results: TestResult[] = [];

  async run(): Promise<TestResult[]> {
    hilog.info(0x0000, 'Test', '开始运行工具类测试...');
    
    try {
      await this.testDateUtilsFormatDate();
      await this.testDateUtilsNow();
      await this.testDateUtilsToday();
      await this.testDateUtilsPadZero();
      await this.testDateUtilsDaysBetween();
      await this.testDateUtilsGetFriendlyTime();
      await this.testDateUtilsGetWeekday();
      await this.testDateUtilsGetMonthCalendar();
      await this.testDateUtilsIsToday();
      await this.testDateUtilsCountdown();
      await this.testDateUtilsPerformance();
    } catch (error) {
      hilog.error(0x0000, 'Test', '工具类测试失败:', error);
    }

    return this.results;
  }

  private async testDateUtilsFormatDate(): Promise<void> {
    const testName = 'testDateUtilsFormatDate';
    const startTime = Date.now();
    
    try {
      await TestUtils.measurePerformance(testName, async () => {
        const testDate = new Date(2024, 0, 15, 14, 30, 45); // 2024-01-15 14:30:45
        
        // 测试基本格式化
        const result1 = DateUtils.formatDate(testDate, 'YYYY-MM-DD');
        Assert.assertEquals(result1, '2024-01-15', '基本日期格式化正确');
        
        // 测试时间格式化
        const result2 = DateUtils.formatDate(testDate, 'YYYY-MM-DD HH:mm:ss');
        Assert.assertEquals(result2, '2024-01-15 14:30:45', '日期时间格式化正确');
        
        // 测试自定义格式
        const result3 = DateUtils.formatDate(testDate, 'YYYY年MM月DD日 HH时mm分ss秒');
        Assert.assertEquals(result3, '2024年01月15日 14时30分45秒', '自定义格式化正确');
      });
      
      this.results.push({
        name: testName,
        status: 'passed',
        duration: Date.now() - startTime
      });
    } catch (error) {
      this.results.push({
        name: testName,
        status: 'failed',
        duration: Date.now() - startTime,
        error: error.message
      });
    }
  }

  private async testDateUtilsNow(): Promise<void> {
    const testName = 'testDateUtilsNow';
    const startTime = Date.now();
    
    try {
      await TestUtils.measurePerformance(testName, async () => {
        const now = DateUtils.now();
        const expected = new Date().toISOString().replace('T', ' ').substring(0, 19);
        
        Assert.assertTrue(now.length === 19, '当前时间格式正确');
        Assert.assertTrue(now.includes('-'), '当前时间包含连字符');
        Assert.assertTrue(now.includes(':'), '当前时间包含冒号');
        Assert.assertTrue(now.includes(' '), '当前时间包含空格');
      });
      
      this.results.push({
        name: testName,
        status: 'passed',
        duration: Date.now() - startTime
      });
    } catch (error) {
      this.results.push({
        name: testName,
        status: 'failed',
        duration: Date.now() - startTime,
        error: error.message
      });
    }
  }

  private async testDateUtilsToday(): Promise<void> {
    const testName = 'testDateUtilsToday';
    const startTime = Date.now();
    
    try {
      await TestUtils.measurePerformance(testName, async () => {
        const today = DateUtils.today();
        const expected = new Date().toISOString().substring(0, 10);
        
        Assert.assertEquals(today, expected, '今天日期正确');
        Assert.assertTrue(today.length === 10, '今天日期格式正确');
      });
      
      this.results.push({
        name: testName,
        status: 'passed',
        duration: Date.now() - startTime
      });
    } catch (error) {
      this.results.push({
        name: testName,
        status: 'failed',
        duration: Date.now() - startTime,
        error: error.message
      });
    }
  }

  private async testDateUtilsPadZero(): Promise<void> {
    const testName = 'testDateUtilsPadZero';
    const startTime = Date.now();
    
    try {
      await TestUtils.measurePerformance(testName, async () => {
        // 测试个位数
        Assert.assertEquals(DateUtils.padZero(5), '05', '个位数补零正确');
        Assert.assertEquals(DateUtils.padZero(9), '09', '个位数补零正确');
        
        // 测试两位数
        Assert.assertEquals(DateUtils.padZero(10), '10', '两位数不补零');
        Assert.assertEquals(DateUtils.padZero(25), '25', '两位数不补零');
        
        // 测试边界值
        Assert.assertEquals(DateUtils.padZero(0), '00', '0补零正确');
        Assert.assertEquals(DateUtils.padZero(99), '99', '99不补零');
      });
      
      this.results.push({
        name: testName,
        status: 'passed',
        duration: Date.now() - startTime
      });
    } catch (error) {
      this.results.push({
        name: testName,
        status: 'failed',
        duration: Date.now() - startTime,
        error: error.message
      });
    }
  }

  private async testDateUtilsDaysBetween(): Promise<void> {
    const testName = 'testDateUtilsDaysBetween';
    const startTime = Date.now();
    
    try {
      await TestUtils.measurePerformance(testName, async () => {
        // 测试同一天
        const days1 = DateUtils.daysBetween('2024-01-15', '2024-01-15');
        Assert.assertEquals(days1, 0, '同一天相差0天');
        
        // 测试相差1天
        const days2 = DateUtils.daysBetween('2024-01-15', '2024-01-16');
        Assert.assertEquals(days2, 1, '相差1天计算正确');
        
        // 测试相差多天
        const days3 = DateUtils.daysBetween('2024-01-01', '2024-01-31');
        Assert.assertEquals(days3, 30, '相差30天计算正确');
        
        // 测试跨年
        const days4 = DateUtils.daysBetween('2023-12-31', '2024-01-01');
        Assert.assertEquals(days4, 1, '跨年计算正确');
      });
      
      this.results.push({
        name: testName,
        status: 'passed',
        duration: Date.now() - startTime
      });
    } catch (error) {
      this.results.push({
        name: testName,
        status: 'failed',
        duration: Date.now() - startTime,
        error: error.message
      });
    }
  }

  private async testDateUtilsGetFriendlyTime(): Promise<void> {
    const testName = 'testDateUtilsGetFriendlyTime';
    const startTime = Date.now();
    
    try {
      await TestUtils.measurePerformance(testName, async () => {
        const now = new Date();
        
        // 测试刚刚
        const justNow = new Date(now.getTime() - 30 * 1000); // 30秒前
        const friendly1 = DateUtils.getFriendlyTime(justNow.toISOString());
        Assert.assertEquals(friendly1, '刚刚', '刚刚显示正确');
        
        // 测试分钟前
        const minutesAgo = new Date(now.getTime() - 5 * 60 * 1000); // 5分钟前
        const friendly2 = DateUtils.getFriendlyTime(minutesAgo.toISOString());
        Assert.assertEquals(friendly2, '5分钟前', '分钟前显示正确');
        
        // 测试小时前
        const hoursAgo = new Date(now.getTime() - 3 * 60 * 60 * 1000); // 3小时前
        const friendly3 = DateUtils.getFriendlyTime(hoursAgo.toISOString());
        Assert.assertEquals(friendly3, '3小时前', '小时前显示正确');
        
        // 测试天前
        const daysAgo = new Date(now.getTime() - 5 * 24 * 60 * 60 * 1000); // 5天前
        const friendly4 = DateUtils.getFriendlyTime(daysAgo.toISOString());
        Assert.assertEquals(friendly4, '5天前', '天前显示正确');
        
        // 测试更早时间
        const longAgo = new Date(now.getTime() - 10 * 24 * 60 * 60 * 1000); // 10天前
        const friendly5 = DateUtils.getFriendlyTime(longAgo.toISOString());
        Assert.assertTrue(friendly5.includes('月'), '更早时间显示月份');
        Assert.assertTrue(friendly5.includes('日'), '更早时间显示日期');
      });
      
      this.results.push({
        name: testName,
        status: 'passed',
        duration: Date.now() - startTime
      });
    } catch (error) {
      this.results.push({
        name: testName,
        status: 'failed',
        duration: Date.now() - startTime,
        error: error.message
      });
    }
  }

  private async testDateUtilsGetWeekday(): Promise<void> {
    const testName = 'testDateUtilsGetWeekday';
    const startTime = Date.now();
    
    try {
      await TestUtils.measurePerformance(testName, async () => {
        // 测试已知的星期
        Assert.assertEquals(DateUtils.getWeekday('2024-01-01'), '星期一', '2024-01-01是星期一');
        Assert.assertEquals(DateUtils.getWeekday('2024-01-07'), '星期日', '2024-01-07是星期日');
        Assert.assertEquals(DateUtils.getWeekday('2024-01-15'), '星期一', '2024-01-15是星期一');
        
        // 测试边界情况
        Assert.assertEquals(DateUtils.getWeekday('2023-12-31'), '星期日', '2023-12-31是星期日');
        Assert.assertEquals(DateUtils.getWeekday('2024-02-29'), '星期四', '2024-02-29是星期四（闰年）');
      });
      
      this.results.push({
        name: testName,
        status: 'passed',
        duration: Date.now() - startTime
      });
    } catch (error) {
      this.results.push({
        name: testName,
        status: 'failed',
        duration: Date.now() - startTime,
        error: error.message
      });
    }
  }

  private async testDateUtilsGetMonthCalendar(): Promise<void> {
    const testName = 'testDateUtilsGetMonthCalendar';
    const startTime = Date.now();
    
    try {
      await TestUtils.measurePerformance(testName, async () => {
        const testDate = new Date(2024, 0, 15); // 2024年1月15日
        const calendar = DateUtils.getMonthCalendar(testDate);
        
        // 验证日历数组长度
        Assert.assertEquals(calendar.length, 42, '日历数组长度为42（6周x7天）');
        
        // 验证包含当前月日期
        const currentMonthDays = calendar.filter(day => day.isCurrentMonth);
        Assert.assertTrue(currentMonthDays.length === 31, '2024年1月有31天');
        
        // 验证包含今天
        const today = calendar.find(day => day.isToday);
        Assert.assertNotNull(today, '日历中包含今天');
        
        // 验证日期顺序
        for (let i = 1; i < calendar.length; i++) {
          Assert.assertTrue(calendar[i].date >= calendar[i-1].date, '日期顺序正确');
        }
      });
      
      this.results.push({
        name: testName,
        status: 'passed',
        duration: Date.now() - startTime
      });
    } catch (error) {
      this.results.push({
        name: testName,
        status: 'failed',
        duration: Date.now() - startTime,
        error: error.message
      });
    }
  }

  private async testDateUtilsIsToday(): Promise<void> {
    const testName = 'testDateUtilsIsToday';
    const startTime = Date.now();
    
    try {
      await TestUtils.measurePerformance(testName, async () => {
        const today = new Date();
        
        // 测试今天
        Assert.assertTrue(DateUtils.isToday(today), '今天识别正确');
        
        // 测试昨天
        const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
        Assert.assertFalse(DateUtils.isToday(yesterday), '昨天识别正确');
        
        // 测试明天
        const tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000);
        Assert.assertFalse(DateUtils.isToday(tomorrow), '明天识别正确');
      });
      
      this.results.push({
        name: testName,
        status: 'passed',
        duration: Date.now() - startTime
      });
    } catch (error) {
      this.results.push({
        name: testName,
        status: 'failed',
        duration: Date.now() - startTime,
        error: error.message
      });
    }
  }

  private async testDateUtilsCountdown(): Promise<void> {
    const testName = 'testDateUtilsCountdown';
    const startTime = Date.now();
    
    try {
      await TestUtils.measurePerformance(testName, async () => {
        const now = new Date();
        
        // 测试已过期
        const pastDate = new Date(now.getTime() - 24 * 60 * 60 * 1000).toISOString();
        const countdown1 = DateUtils.getCountdownText(pastDate);
        Assert.assertEquals(countdown1, '已到期', '已到期显示正确');
        
        // 测试即将到期（分钟级）
        const futureMinutes = new Date(now.getTime() + 5 * 60 * 1000).toISOString();
        const countdown2 = DateUtils.getCountdownText(futureMinutes);
        Assert.assertTrue(countdown2.includes('分钟'), '分钟级倒计时显示正确');
        
        // 测试即将到期（小时级）
        const futureHours = new Date(now.getTime() + 3 * 60 * 60 * 1000).toISOString();
        const countdown3 = DateUtils.getCountdownText(futureHours);
        Assert.assertTrue(countdown3.includes('小时'), '小时级倒计时显示正确');
        
        // 测试即将到期（天级）
        const futureDays = new Date(now.getTime() + 2 * 24 * 60 * 60 * 1000).toISOString();
        const countdown4 = DateUtils.getCountdownText(futureDays);
        Assert.assertTrue(countdown4.includes('天'), '天级倒计时显示正确');
      });
      
      this.results.push({
        name: testName,
        status: 'passed',
        duration: Date.now() - startTime
      });
    } catch (error) {
      this.results.push({
        name: testName,
        status: 'failed',
        duration: Date.now() - startTime,
        error: error.message
      });
    }
  }

  private async testDateUtilsPerformance(): Promise<void> {
    const testName = 'testDateUtilsPerformance';
    const startTime = Date.now();
    
    try {
      await TestUtils.measurePerformance(testName, async () => {
        // 测试批量日期格式化性能
        const performanceStartTime = Date.now();
        
        for (let i = 0; i < 1000; i++) {
          const testDate = new Date(2024, 0, 1 + i);
          DateUtils.formatDate(testDate, 'YYYY-MM-DD');
        }
        
        const formatDuration = Date.now() - performanceStartTime;
        hilog.info(0x0000, 'Test', `格式化1000个日期耗时: ${formatDuration}ms`);
        
        // 测试批量友好时间计算性能
        const friendlyStartTime = Date.now();
        const now = new Date();
        
        for (let i = 0; i < 1000; i++) {
          const pastDate = new Date(now.getTime() - i * 60 * 1000);
          DateUtils.getFriendlyTime(pastDate.toISOString());
        }
        
        const friendlyDuration = Date.now() - friendlyStartTime;
        hilog.info(0x0000, 'Test', `计算1000个友好时间耗时: ${friendlyDuration}ms`);
        
        // 测试批量日历生成性能
        const calendarStartTime = Date.now();
        
        for (let i = 0; i < 12; i++) {
          const monthDate = new Date(2024, i, 1);
          DateUtils.getMonthCalendar(monthDate);
        }
        
        const calendarDuration = Date.now() - calendarStartTime;
        hilog.info(0x0000, 'Test', `生成12个月日历耗时: ${calendarDuration}ms`);
        
        // 性能断言
        Assert.assertTrue(formatDuration < 1000, `格式化1000个日期应在1秒内完成，实际耗时: ${formatDuration}ms`);
        Assert.assertTrue(friendlyDuration < 1000, `计算1000个友好时间应在1秒内完成，实际耗时: ${friendlyDuration}ms`);
        Assert.assertTrue(calendarDuration < 1000, `生成12个月日历应在1秒内完成，实际耗时: ${calendarDuration}ms`);
      });
      
      this.results.push({
        name: testName,
        status: 'passed',
        duration: Date.now() - startTime
      });
    } catch (error) {
      this.results.push({
        name: testName,
        status: 'failed',
        duration: Date.now() - startTime,
        error: error.message
      });
    }
  }
}