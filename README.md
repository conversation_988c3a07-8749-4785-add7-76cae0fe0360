# TimeNotes (TimeNotes)

一款基于鸿蒙系统的优雅笔记应用，专注于记录生活点滴，捕捉每一个灵感瞬间。

## 项目特性

- 📝 **优雅简洁的界面设计**：让记录成为一种享受
- 🔄 **实时跨设备同步**：灵感不会丢失
- 🏷️ **智能分类和搜索**：快速找到所需内容
- 💾 **离线可用**：联网自动同步
- 🔒 **数据安全加密存储**：保护隐私

## 技术栈

- **开发平台**：HarmonyOS 5.0 (API 12)
- **UI框架**：ArkUI (声明式UI)
- **开发语言**：ArkTS
- **数据存储**：RDB数据库
- **网络同步**：分布式数据服务

## 项目结构

```
TimeNotes/
├── entry/                     # 主模块入口
│   ├── src/main/ets/          # ArkTS源码
│   │   ├── entryability/      # 应用入口
│   │   ├── pages/            # 页面
│   │   ├── components/       # 自定义组件
│   │   ├── model/            # 数据模型
│   │   ├── viewmodel/        # 视图模型
│   │   ├── services/         # 业务服务
│   │   ├── utils/            # 工具类
│   │   └── constants/        # 常量定义
│   └── src/main/resources/   # 资源文件
└── features/                 # 功能模块（待开发）
```

## 开发环境

- DevEco Studio 4.0+
- JDK 17
- HarmonyOS SDK 12+
- Node.js 16+

## 构建和运行

1. 使用 DevEco Studio 打开项目
2. 配置模拟器或连接真机
3. 点击运行按钮或使用快捷键 Shift + F10

## 开发状态

🚧 **当前阶段**：基础框架搭建

- ✅ 项目目录结构创建
- ✅ 基础配置文件完成
- ⏳ 数据模型实现（进行中）
- ⏳ UI组件开发（待进行）
- ⏳ 核心功能开发（待进行）

## 许可证

MIT License