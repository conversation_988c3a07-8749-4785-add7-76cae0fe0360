// 图片懒加载组件
import { hilog } from '@kit.PerformanceAnalysisKit';

// 图片加载状态
export enum ImageLoadState {
  IDLE = 'idle',
  LOADING = 'loading',
  LOADED = 'loaded',
  FAILED = 'failed'
}

// 图片懒加载配置
export interface LazyImageConfig {
  placeholder?: string; // 占位图片
  errorImage?: string; // 错误图片
  threshold?: number; // 预加载阈值
  retryCount?: number; // 重试次数
  cacheSize?: number; // 缓存大小
  enableMemoryCache?: boolean; // 启用内存缓存
  enableDiskCache?: boolean; // 启用磁盘缓存
}

// 图片缓存项
interface ImageCacheItem {
  url: string;
  data: string; // base64数据或本地路径
  timestamp: number;
  size: number;
}

@Component
export struct LazyImage {
  @Prop src: string;
  @Prop config: LazyImageConfig;
  @State private loadState: ImageLoadState = ImageLoadState.IDLE;
  @State private currentSrc: string = '';
  @State private retryCount: number = 0;
  @State private imageWidth: number = 0;
  @State private imageHeight: number = 0;
  
  private observer: IntersectionObserver | null = null;
  private imageCache: Map<string, ImageCacheItem> = new Map();
  private loadQueue: string[] = [];
  private isLoading: boolean = false;

  aboutToAppear() {
    this.initializeLazyImage();
    this.setupIntersectionObserver();
  }

  aboutToDisappear() {
    this.cleanup();
  }

  /**
   * 初始化懒加载图片
   */
  private initializeLazyImage() {
    const defaultConfig: LazyImageConfig = {
      placeholder: 'app.media.ic_image_placeholder',
      errorImage: 'app.media.ic_image_error',
      threshold: 100,
      retryCount: 3,
      cacheSize: 50,
      enableMemoryCache: true,
      enableDiskCache: true
    };
    
    this.config = { ...defaultConfig, ...this.config };
    
    // 如果图片已经在缓存中，直接使用
    if (this.config.enableMemoryCache && this.imageCache.has(this.src)) {
      this.currentSrc = this.src;
      this.loadState = ImageLoadState.LOADED;
    }
  }

  /**
   * 设置交叉观察器
   */
  private setupIntersectionObserver() {
    // 在实际环境中，这里会设置IntersectionObserver来监听元素是否进入视口
    hilog.info(0x0000, 'LazyImage', `设置懒加载观察器: ${this.src}`);
  }

  /**
   * 清理资源
   */
  private cleanup() {
    if (this.observer) {
      this.observer.disconnect();
    }
  }

  /**
   * 开始加载图片
   */
  private async loadImage() {
    if (this.loadState === ImageLoadState.LOADING || this.loadState === ImageLoadState.LOADED) {
      return;
    }

    this.loadState = ImageLoadState.LOADING;
    
    try {
      // 检查缓存
      if (this.config.enableMemoryCache) {
        const cached = this.imageCache.get(this.src);
        if (cached) {
          this.currentSrc = cached.data;
          this.loadState = ImageLoadState.LOADED;
          hilog.info(0x0000, 'LazyImage', `从缓存加载图片: ${this.src}`);
          return;
        }
      }

      // 模拟异步加载图片
      await this.simulateImageLoad();
      
      this.currentSrc = this.src;
      this.loadState = ImageLoadState.LOADED;
      this.retryCount = 0;
      
      // 缓存图片
      if (this.config.enableMemoryCache) {
        this.cacheImage(this.src, this.currentSrc);
      }
      
      hilog.info(0x0000, 'LazyImage', `图片加载成功: ${this.src}`);
      
    } catch (error) {
      this.loadState = ImageLoadState.FAILED;
      hilog.error(0x0000, 'LazyImage', `图片加载失败: ${this.src}`, error);
      
      // 重试机制
      if (this.retryCount < (this.config.retryCount || 3)) {
        this.retryCount++;
        hilog.info(0x0000, 'LazyImage', `重试加载图片 (${this.retryCount}/${this.config.retryCount}): ${this.src}`);
        
        setTimeout(() => {
          this.loadImage();
        }, 1000 * this.retryCount); // 指数退避
      }
    }
  }

  /**
   * 模拟图片加载
   */
  private simulateImageLoad(): Promise<void> {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // 模拟90%的成功率
        if (Math.random() < 0.9) {
          resolve();
        } else {
          reject(new Error('图片加载失败'));
        }
      }, Math.random() * 2000 + 500); // 500-2500ms随机延迟
    });
  }

  /**
   * 缓存图片
   */
  private cacheImage(url: string, data: string) {
    // 清理过期缓存
    this.cleanupCache();
    
    const cacheItem: ImageCacheItem = {
      url,
      data,
      timestamp: Date.now(),
      size: data.length
    };
    
    this.imageCache.set(url, cacheItem);
    hilog.info(0x0000, 'LazyImage', `图片已缓存: ${url}, 大小: ${data.length} bytes`);
  }

  /**
   * 清理缓存
   */
  private cleanupCache() {
    const maxCacheSize = this.config.cacheSize || 50;
    
    if (this.imageCache.size > maxCacheSize) {
      // 按LRU策略清理缓存
      const sortedItems = Array.from(this.imageCache.entries())
        .sort((a, b) => a[1].timestamp - b[1].timestamp);
      
      const itemsToRemove = sortedItems.slice(0, this.imageCache.size - maxCacheSize);
      itemsToRemove.forEach(([url]) => {
        this.imageCache.delete(url);
      });
      
      hilog.info(0x0000, 'LazyImage', `清理了 ${itemsToRemove.length} 个过期缓存项`);
    }
  }

  /**
   * 预加载图片
   */
  private preloadImage(url: string) {
    if (this.imageCache.has(url)) {
      return;
    }
    
    this.loadQueue.push(url);
    this.processLoadQueue();
  }

  /**
   * 处理加载队列
   */
  private async processLoadQueue() {
    if (this.isLoading || this.loadQueue.length === 0) {
      return;
    }
    
    this.isLoading = true;
    
    while (this.loadQueue.length > 0) {
      const url = this.loadQueue.shift();
      if (url && !this.imageCache.has(url)) {
        try {
          await this.simulateImageLoad();
          this.cacheImage(url, url);
          hilog.info(0x0000, 'LazyImage', `预加载图片成功: ${url}`);
        } catch (error) {
          hilog.error(0x0000, 'LazyImage', `预加载图片失败: ${url}`, error);
        }
      }
    }
    
    this.isLoading = false;
  }

  /**
   * 重新加载图片
   */
  private reloadImage() {
    this.retryCount = 0;
    this.loadState = ImageLoadState.IDLE;
    this.loadImage();
  }

  build() {
    Column() {
      // 图片容器
      Stack() {
        // 占位图片或错误图片
        if (this.loadState === ImageLoadState.LOADING || this.loadState === ImageLoadState.FAILED) {
          Image(this.loadState === ImageLoadState.LOADING ? 
            (this.config.placeholder || 'app.media.ic_image_placeholder') : 
            (this.config.errorImage || 'app.media.ic_image_error')
          )
            .width('100%')
            .height('100%')
            .objectFit(ImageFit.Cover)
            .opacity(this.loadState === ImageLoadState.LOADING ? 0.5 : 1);
        }
        
        // 实际图片
        if (this.loadState === ImageLoadState.LOADED && this.currentSrc) {
          Image(this.currentSrc)
            .width('100%')
            .height('100%')
            .objectFit(ImageFit.Cover)
            .onLoad((event) => {
              this.imageWidth = event.width;
              this.imageHeight = event.height;
              hilog.info(0x0000, 'LazyImage', `图片加载完成: ${this.currentSrc}, 尺寸: ${event.width}x${event.height}`);
            })
            .onError((error) => {
              hilog.error(0x0000, 'LazyImage', `图片渲染错误: ${this.currentSrc}`, error);
              this.loadState = ImageLoadState.FAILED;
            });
        }
        
        // 加载状态指示器
        if (this.loadState === ImageLoadState.LOADING) {
          Column() {
            LoadingProgress()
              .width(24)
              .height(24)
              .color('#007AFF');
            
            Text('加载中...')
              .fontSize(12)
              .fontColor('#666666')
              .margin({ top: 4 });
          }
          .width('100%')
          .height('100%')
          .backgroundColor('rgba(0, 0, 0, 0.1)')
          .justifyContent(FlexAlign.Center);
        }
        
        // 错误状态和重试按钮
        if (this.loadState === ImageLoadState.FAILED) {
          Column() {
            Text('加载失败')
              .fontSize(14)
              .fontColor('#666666');
            
            Button('重试')
              .fontSize(12)
              .fontColor(Color.White)
              .backgroundColor('#007AFF')
              .borderRadius(4)
              .margin({ top: 8 })
              .onClick(() => {
                this.reloadImage();
              });
          }
          .width('100%')
          .height('100%')
          .backgroundColor('rgba(0, 0, 0, 0.1)')
          .justifyContent(FlexAlign.Center);
        }
      }
      .width('100%')
      .height('100%')
      .borderRadius(8)
      .overflow Hidden)
      .onClick(() => {
        // 点击触发加载
        if (this.loadState === ImageLoadState.IDLE) {
          this.loadImage();
        }
      });
    }
    .width('100%')
    .height('100%')
    .backgroundColor(Color.Transparent)
    .onAppear(() => {
      // 元素出现时触发加载
      setTimeout(() => {
        this.loadImage();
      }, 100);
    });
  }
}

// 图片预加载器
export class ImagePreloader {
  private static instance: ImagePreloader;
  private preloadQueue: string[] = [];
  private isPreloading: boolean = false;
  private cache: Map<string, string> = new Map();
  
  private constructor() {}
  
  static getInstance(): ImagePreloader {
    if (!ImagePreloader.instance) {
      ImagePreloader.instance = new ImagePreloader();
    }
    return ImagePreloader.instance;
  }
  
  /**
   * 预加载图片
   */
  async preloadImages(urls: string[]): Promise<void> {
    this.preloadQueue.push(...urls);
    await this.processPreloadQueue();
  }
  
  /**
   * 处理预加载队列
   */
  private async processPreloadQueue(): Promise<void> {
    if (this.isPreloading || this.preloadQueue.length === 0) {
      return;
    }
    
    this.isPreloading = true;
    
    while (this.preloadQueue.length > 0) {
      const url = this.preloadQueue.shift();
      if (url && !this.cache.has(url)) {
        try {
          await this.simulateImageLoad(url);
          this.cache.set(url, url);
          hilog.info(0x0000, 'ImagePreloader', `预加载成功: ${url}`);
        } catch (error) {
          hilog.error(0x0000, 'ImagePreloader', `预加载失败: ${url}`, error);
        }
      }
      
      // 避免阻塞主线程
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    this.isPreloading = false;
  }
  
  /**
   * 模拟图片加载
   */
  private simulateImageLoad(url: string): Promise<void> {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        if (Math.random() < 0.95) { // 95%成功率
          resolve();
        } else {
          reject(new Error('预加载失败'));
        }
      }, Math.random() * 1000 + 200); // 200-1200ms
    });
  }
  
  /**
   * 检查图片是否已预加载
   */
  isPreloaded(url: string): boolean {
    return this.cache.has(url);
  }
  
  /**
   * 清理预加载缓存
   */
  clearCache(): void {
    this.cache.clear();
    hilog.info(0x0000, 'ImagePreloader', '预加载缓存已清理');
  }
}

// 使用示例组件
@Component
export struct LazyImageExample {
  @State private images: string[] = [];
  @State private loading: boolean = false;
  
  aboutToAppear() {
    this.generateImageUrls();
    this.preloadImages();
  }
  
  /**
   * 生成图片URL列表
   */
  private generateImageUrls() {
    const imageUrls: string[] = [];
    for (let i = 1; i <= 20; i++) {
      imageUrls.push(`https://example.com/image${i}.jpg`);
    }
    this.images = imageUrls;
  }
  
  /**
   * 预加载图片
   */
  private async preloadImages() {
    this.loading = true;
    
    try {
      const preloader = ImagePreloader.getInstance();
      // 预加载前10张图片
      await preloader.preloadImages(this.images.slice(0, 10));
      hilog.info(0x0000, 'LazyImageExample', '图片预加载完成');
    } catch (error) {
      hilog.error(0x0000, 'LazyImageExample', '图片预加载失败:', error);
    } finally {
      this.loading = false;
    }
  }
  
  build() {
    Column() {
      // 标题
      Text('图片懒加载示例')
        .fontSize(20)
        .fontWeight(600)
        .margin({ bottom: 16 });
      
      // 预加载状态
      if (this.loading) {
        Row() {
          LoadingProgress()
            .width(16)
            .height(16)
            .color('#007AFF');
          
          Text('正在预加载图片...')
            .fontSize(14)
            .fontColor('#666666')
            .margin({ left: 8 });
        }
        .margin({ bottom: 16 });
      }
      
      // 图片网格
      Grid() {
        ForEach(this.images, (url: string, index: number) => {
          GridItem() {
            LazyImage({
              src: url,
              config: {
                placeholder: 'app.media.ic_image_placeholder',
                errorImage: 'app.media.ic_image_error',
                threshold: 100,
                retryCount: 3,
                cacheSize: 50,
                enableMemoryCache: true,
                enableDiskCache: true
              }
            })
            .width('100%')
            .height(120)
            .borderRadius(8);
          }
          .columnSpan(1)
          .rowSpan(1);
        });
      }
      .columnsTemplate('1fr 1fr 1fr')
      .rowsGap(8)
      .columnsGap(8)
      .layoutWeight(1);
      
      // 缓存信息
      Button('清理缓存')
        .fontSize(14)
        .fontColor(Color.White)
        .backgroundColor('#FF3B30')
        .borderRadius(6)
        .margin({ top: 16 })
        .onClick(() => {
          const preloader = ImagePreloader.getInstance();
          preloader.clearCache();
          hilog.info(0x0000, 'LazyImageExample', '缓存已清理');
        });
    }
    .width('100%')
    .height('100%')
    .padding(16)
    .backgroundColor('#F5F5F5');
  }
}