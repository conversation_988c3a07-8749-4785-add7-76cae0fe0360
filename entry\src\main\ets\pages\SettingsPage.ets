import { MorandiColors } from '../constants/MorandiColors';
import { AppConstants } from '../constants/AppConstants';
import router from '@ohos.router';

// 设置项类型
interface SettingItem {
  id: string;
  icon: string;
  title: string;
  description: string;
  type: 'navigation' | 'toggle' | 'action';
  value?: boolean;
  onClick?: () => void;
}

// 设置分组类型
interface SettingGroup {
  title: string;
  items: SettingItem[];
}

// 主题类型
type ThemeMode = 'light' | 'dark' | 'auto';

// 字体大小类型
type FontSize = 'small' | 'medium' | 'large';


@Component
struct SettingsPage {
  @State currentTheme: ThemeMode = 'light';
  @State fontSize: FontSize = 'medium';
  @State notificationsEnabled: boolean = true;
  @State autoSync: boolean = true;
  @State showThemeDialog: boolean = false;
  @State showFontSizeDialog: boolean = false;
  @State showClearCacheDialog: boolean = false;
  @State cacheSize: string = '12.5 MB';
  
  // 获取设置分组数据
  private getSettingGroups(): SettingGroup[] {
    return [
      {
        title: '通用',
        items: [
          {
            id: 'theme',
            icon: '🎨',
            title: '主题设置',
            description: this.getThemeDescription(),
            type: 'navigation',
            onClick: () => this.showThemeDialog = true
          },
          {
            id: 'fontSize',
            icon: '📝',
            title: '字体大小',
            description: this.getFontSizeDescription(),
            type: 'navigation',
            onClick: () => this.showFontSizeDialog = true
          },
          {
            id: 'notifications',
            icon: '🔔',
            title: '通知提醒',
            description: '接收笔记提醒和更新通知',
            type: 'toggle',
            value: this.notificationsEnabled
          }
        ]
      },
      {
        title: '数据',
        items: [
          {
            id: 'backup',
            icon: '💾',
            title: '备份恢复',
            description: '备份和恢复笔记数据',
            type: 'navigation',
            onClick: () => this.handleBackup()
          },
          {
            id: 'cache',
            icon: '🗑️',
            title: '清除缓存',
            description: `当前缓存：${this.cacheSize}`,
            type: 'action',
            onClick: () => this.showClearCacheDialog = true
          },
          {
            id: 'sync',
            icon: '🔄',
            title: '数据同步',
            description: '自动同步数据到云端',
            type: 'toggle',
            value: this.autoSync
          }
        ]
      },
      {
        title: '关于',
        items: [
          {
            id: 'version',
            icon: 'ℹ️',
            title: '版本信息',
            description: 'TimeNotes v1.0.0',
            type: 'navigation',
            onClick: () => this.handleVersion()
          },
          {
            id: 'privacy',
            icon: '🔒',
            title: '隐私政策',
            description: '查看隐私政策内容',
            type: 'navigation',
            onClick: () => this.handlePrivacy()
          },
          {
            id: 'agreement',
            icon: '📋',
            title: '用户协议',
            description: '查看用户服务协议',
            type: 'navigation',
            onClick: () => this.handleAgreement()
          }
        ]
      }
    ];
  }

  aboutToAppear() {
    this.loadSettings();
  }

  build() {
    Stack() {
      Column() {
        // 顶部导航栏
        this.HeaderBar();
        
        // 设置列表
        Scroll() {
          Column() {
            ForEach(this.getSettingGroups(), (group: SettingGroup) => {
              this.SettingGroup(group);
            });
          }
          .padding({ left: 16, right: 16, top: 8, bottom: 8 });
        }
        .layoutWeight(1);
      }
      .width('100%')
      .height('100%')
      .backgroundColor(MorandiColors.background);
      
      // 主题对话框
      if (this.showThemeDialog) {
        Column() {
          Text('')
            .width('100%')
            .height('100%')
            .backgroundColor('rgba(0, 0, 0, 0.5)')
            .onClick(() => this.showThemeDialog = false);
          
          this.ThemeDialog();
        }
        .width('100%')
        .height('100%')
        .justifyContent(FlexAlign.Center)
        .alignItems(HorizontalAlign.Center);
      }
      
      // 字体大小对话框
      if (this.showFontSizeDialog) {
        Column() {
          Text('')
            .width('100%')
            .height('100%')
            .backgroundColor('rgba(0, 0, 0, 0.5)')
            .onClick(() => this.showFontSizeDialog = false);
          
          this.FontSizeDialog();
        }
        .width('100%')
        .height('100%')
        .justifyContent(FlexAlign.Center)
        .alignItems(HorizontalAlign.Center);
      }
      
      // 清除缓存对话框
      if (this.showClearCacheDialog) {
        Column() {
          Text('')
            .width('100%')
            .height('100%')
            .backgroundColor('rgba(0, 0, 0, 0.5)')
            .onClick(() => this.showClearCacheDialog = false);
          
          this.ClearCacheDialog();
        }
        .width('100%')
        .height('100%')
        .justifyContent(FlexAlign.Center)
        .alignItems(HorizontalAlign.Center);
      }
    }
    .width('100%')
    .height('100%');
  }

  /**
   * 顶部导航栏
   */
  @Builder HeaderBar() {
    Row() {
      // 返回按钮
      Text('←')
        .fontSize(20)
        .fontColor(MorandiColors.textPrimary)
        .onClick(() => {
          router.back();
        });
      
      // 标题
      Text('设置')
        .fontSize(18)
        .fontWeight(500)
        .fontColor(MorandiColors.textPrimary)
        .layoutWeight(1)
        .textAlign(TextAlign.Center);
      
      // 占位
      Text('←')
        .fontSize(20)
        .fontColor(MorandiColors.background);
    }
    .width('100%')
    .height(56)
    .padding({ left: 16, right: 16 })
    .backgroundColor(MorandiColors.background)
    .border({ width: { bottom: 1 }, color: MorandiColors.border });
  }

  /**
   * 设置分组
   */
  @Builder SettingGroup(group: SettingGroup) {
    Column() {
      // 分组标题
      Text(group.title)
        .fontSize(12)
        .fontColor(MorandiColors.textTertiary)
        .fontWeight(500)
        .margin({ left: 4, bottom: 8, top: 16 });
      
      // 设置项列表
      Column() {
        ForEach(group.items, (item: SettingItem, index: number) => {
          this.SettingItem(item, index === group.items.length - 1);
        });
      }
      .backgroundColor(MorandiColors.cardBackground)
      .borderRadius(12)
      .border({ width: 1, color: MorandiColors.border });
    }
    .width('100%');
  }

  /**
   * 设置项
   */
  @Builder SettingItem(item: SettingItem, isLast: boolean) {
    Row() {
      // 图标
      Text(item.icon)
        .fontSize(20)
        .margin({ right: 12 });
      
      // 内容区域
      Column() {
        Text(item.title)
          .fontSize(16)
          .fontWeight(400)
          .fontColor(MorandiColors.textPrimary)
          .alignSelf(ItemAlign.Start);
        
        Text(item.description)
          .fontSize(12)
          .fontColor(MorandiColors.textTertiary)
          .margin({ top: 2 })
          .maxLines(2)
          .textOverflow({ overflow: TextOverflow.Ellipsis })
          .alignSelf(ItemAlign.Start);
      }
      .layoutWeight(1)
      .alignItems(HorizontalAlign.Start);
      
      // 右侧控件
      if (item.type === 'toggle') {
        Toggle({ type: ToggleType.Switch, isOn: this.getToggleValue(item.id) })
          .onChange((isOn: boolean) => {
            this.handleToggleChange(item.id, isOn);
          });
      } else {
        Text('>')
          .fontSize(16)
          .fontColor(MorandiColors.textHint);
      }
    }
    .width('100%')
    .padding({ left: 16, right: 16, top: 16, bottom: 16 })
    .onClick(() => {
      if (item.onClick) {
        item.onClick();
      } else if (item.type === 'navigation') {
        this.handleNavigation(item.id);
      }
    });
    
    // 分隔线（除了最后一项）
    if (!isLast) {
      Divider()
        .height(1)
        .color(MorandiColors.divider)
        .margin({ left: 16, right: 16 });
    }
  }

  /**
   * 主题选择对话框
   */
  @Builder ThemeDialog() {
    Column() {
      Text('主题设置')
        .fontSize(18)
        .fontWeight(500)
        .fontColor(MorandiColors.textPrimary)
        .margin({ bottom: 24 });
      
      Column() {
        this.ThemeOption('light', '浅色主题', '☀️ 适合白天使用', this.currentTheme === 'light');
        this.ThemeOption('dark', '深色主题', '🌙 适合夜间使用', this.currentTheme === 'dark');
        this.ThemeOption('auto', '跟随系统', '🌓 自动切换主题', this.currentTheme === 'auto');
      }
      .width('100%');
      
      Row() {
        Text('取消')
          .fontSize(16)
          .fontColor(MorandiColors.textSecondary)
          .layoutWeight(1)
          .textAlign(TextAlign.Center)
          .onClick(() => this.showThemeDialog = false);
        
        Text('确定')
          .fontSize(16)
          .fontColor(MorandiColors.accent)
          .layoutWeight(1)
          .textAlign(TextAlign.Center)
          .onClick(() => {
            this.showThemeDialog = false;
            this.saveTheme();
          });
      }
      .width('100%')
      .margin({ top: 24 });
    }
    .width(280)
    .padding(24)
    .backgroundColor(MorandiColors.cardBackground)
    .borderRadius(16)
    .border({ width: 1, color: MorandiColors.border });
  }

  /**
   * 主题选项
   */
  @Builder ThemeOption(mode: ThemeMode, title: string, description: string, isSelected: boolean) {
    Row() {
      Text(isSelected ? '●' : '○')
        .fontSize(16)
        .fontColor(isSelected ? MorandiColors.accent : MorandiColors.textHint)
        .margin({ right: 12 });
      
      Column() {
        Text(title)
          .fontSize(16)
          .fontWeight(400)
          .fontColor(MorandiColors.textPrimary)
          .alignSelf(ItemAlign.Start);
        
        Text(description)
          .fontSize(12)
          .fontColor(MorandiColors.textTertiary)
          .margin({ top: 2 })
          .alignSelf(ItemAlign.Start);
      }
      .layoutWeight(1)
      .alignItems(HorizontalAlign.Start);
    }
    .width('100%')
    .padding(12)
    .borderRadius(8)
    .backgroundColor(isSelected ? MorandiColors.border : 'transparent')
    .onClick(() => {
      this.currentTheme = mode;
    });
  }

  /**
   * 字体大小选择对话框
   */
  @Builder FontSizeDialog() {
    Column() {
      Text('字体大小')
        .fontSize(18)
        .fontWeight(500)
        .fontColor(MorandiColors.textPrimary)
        .margin({ bottom: 24 });
      
      Column() {
        this.FontSizeOption('small', '小号', 'A', 14, this.fontSize === 'small');
        this.FontSizeOption('medium', '中号', 'A', 16, this.fontSize === 'medium');
        this.FontSizeOption('large', '大号', 'A', 18, this.fontSize === 'large');
      }
      .width('100%');
      
      Row() {
        Text('取消')
          .fontSize(16)
          .fontColor(MorandiColors.textSecondary)
          .layoutWeight(1)
          .textAlign(TextAlign.Center)
          .onClick(() => this.showFontSizeDialog = false);
        
        Text('确定')
          .fontSize(16)
          .fontColor(MorandiColors.accent)
          .layoutWeight(1)
          .textAlign(TextAlign.Center)
          .onClick(() => {
            this.showFontSizeDialog = false;
            this.saveFontSize();
          });
      }
      .width('100%')
      .margin({ top: 24 });
    }
    .width(280)
    .padding(24)
    .backgroundColor(MorandiColors.cardBackground)
    .borderRadius(16)
    .border({ width: 1, color: MorandiColors.border });
  }

  /**
   * 字体大小选项
   */
  @Builder FontSizeOption(size: FontSize, title: string, text: string, fontSize: number, isSelected: boolean) {
    Row() {
      Text(isSelected ? '●' : '○')
        .fontSize(16)
        .fontColor(isSelected ? MorandiColors.accent : MorandiColors.textHint)
        .margin({ right: 12 });
      
      Text(text)
        .fontSize(fontSize)
        .fontWeight(500)
        .fontColor(MorandiColors.textPrimary)
        .margin({ right: 12 });
      
      Text(title)
        .fontSize(16)
        .fontWeight(400)
        .fontColor(MorandiColors.textPrimary)
        .layoutWeight(1);
    }
    .width('100%')
    .padding(12)
    .borderRadius(8)
    .backgroundColor(isSelected ? MorandiColors.border : 'transparent')
    .onClick(() => {
      this.fontSize = size;
    });
  }

  /**
   * 清除缓存确认对话框
   */
  @Builder ClearCacheDialog() {
    Column() {
      Text('清除缓存')
        .fontSize(18)
        .fontWeight(500)
        .fontColor(MorandiColors.textPrimary)
        .margin({ bottom: 16 });
      
      Text(`确定要清除缓存吗？这将删除 ${this.cacheSize} 的临时数据，不会影响您的笔记内容。`)
        .fontSize(14)
        .fontColor(MorandiColors.textSecondary)
        .lineHeight(20)
        .margin({ bottom: 24 });
      
      Row() {
        Text('取消')
          .fontSize(16)
          .fontColor(MorandiColors.textSecondary)
          .layoutWeight(1)
          .textAlign(TextAlign.Center)
          .onClick(() => this.showClearCacheDialog = false);
        
        Text('清除')
          .fontSize(16)
          .fontColor(MorandiColors.error)
          .layoutWeight(1)
          .textAlign(TextAlign.Center)
          .onClick(() => {
            this.clearCache();
            this.showClearCacheDialog = false;
          });
      }
      .width('100%');
    }
    .width(280)
    .padding(24)
    .backgroundColor(MorandiColors.cardBackground)
    .borderRadius(16)
    .border({ width: 1, color: MorandiColors.border });
  }

  /**
   * 加载设置
   */
  private loadSettings() {
    // TODO: 从本地存储加载设置
    // 这里使用默认值
  }

  /**
   * 保存主题设置
   */
  private saveTheme() {
    // TODO: 保存主题设置到本地存储
    console.log('保存主题设置:', this.currentTheme);
  }

  /**
   * 保存字体大小设置
   */
  private saveFontSize() {
    // TODO: 保存字体大小设置到本地存储
    console.log('保存字体大小设置:', this.fontSize);
  }

  /**
   * 获取主题描述
   */
  private getThemeDescription(): string {
    switch (this.currentTheme) {
      case 'light': return '浅色主题';
      case 'dark': return '深色主题';
      case 'auto': return '跟随系统';
      default: return '浅色主题';
    }
  }

  /**
   * 获取字体大小描述
   */
  private getFontSizeDescription(): string {
    switch (this.fontSize) {
      case 'small': return '小号字体';
      case 'medium': return '中号字体';
      case 'large': return '大号字体';
      default: return '中号字体';
    }
  }

  /**
   * 获取切换开关的值
   */
  private getToggleValue(itemId: string): boolean {
    switch (itemId) {
      case 'notifications': return this.notificationsEnabled;
      case 'sync': return this.autoSync;
      default: return false;
    }
  }

  /**
   * 处理开关变化
   */
  private handleToggleChange(itemId: string, value: boolean) {
    switch (itemId) {
      case 'notifications':
        this.notificationsEnabled = value;
        console.log('通知设置:', value);
        break;
      case 'sync':
        this.autoSync = value;
        console.log('同步设置:', value);
        break;
    }
  }

  /**
   * 处理导航跳转
   */
  private handleNavigation(itemId: string) {
    console.log('导航到:', itemId);
    // TODO: 实现具体的页面跳转逻辑
  }

  /**
   * 处理备份
   */
  private handleBackup() {
    console.log('处理备份');
    // TODO: 实现备份功能
  }

  /**
   * 处理版本信息
   */
  private handleVersion() {
    console.log('显示版本信息');
    // TODO: 显示版本信息页面
  }

  /**
   * 处理隐私政策
   */
  private handlePrivacy() {
    console.log('显示隐私政策');
    // TODO: 显示隐私政策页面
  }

  /**
   * 处理用户协议
   */
  private handleAgreement() {
    console.log('显示用户协议');
    // TODO: 显示用户协议页面
  }

  /**
   * 清除缓存
   */
  private clearCache() {
    console.log('清除缓存');
    // TODO: 实现清除缓存功能
    this.cacheSize = '0 MB';
  }
}