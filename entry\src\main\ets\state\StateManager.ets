import { NoteModel, CapsuleModel, SettingsModel } from '../model/NoteModel';

/**
 * 应用状态类型定义
 */
export interface AppState {
  // 用户状态
  userSettings: SettingsModel | null;
  
  // 笔记状态
  notes: NoteModel[];
  selectedNote: NoteModel | null;
  searchResults: NoteModel[];
  currentFilter: {
    category?: number;
    mood?: string;
    date?: string;
    tag?: string;
  };
  
  // 时间胶囊状态
  capsules: CapsuleModel[];
  expiredCapsules: CapsuleModel[];
  
  // UI状态
  isLoading: boolean;
  isRefreshing: boolean;
  isEditing: boolean;
  currentView: 'timeline' | 'calendar' | 'search' | 'capsule' | 'stats' | 'settings';
  
  // 缓存状态
  lastSyncTime: string;
  hasUnsavedChanges: boolean;
  
  // 统计状态
  stats: {
    totalNotes: number;
    totalCapsules: number;
    writingStreak: number;
    thisMonthNotes: number;
  };
}

/**
 * 状态变更类型
 */
export type StateChangeType = 
  | 'USER_SETTINGS_CHANGED'
  | 'NOTES_LOADED'
  | 'NOTE_CREATED'
  | 'NOTE_UPDATED'
  | 'NOTE_DELETED'
  | 'NOTE_SELECTED'
  | 'SEARCH_RESULTS_UPDATED'
  | 'FILTER_CHANGED'
  | 'CAPSULES_LOADED'
  | 'CAPSULE_CREATED'
  | 'CAPSULE_UPDATED'
  | 'CAPSULE_DELETED'
  | 'UI_STATE_CHANGED'
  | 'CACHE_UPDATED'
  | 'STATS_UPDATED';

/**
 * 状态变更事件
 */
export interface StateChangeEvent {
  type: StateChangeType;
  payload?: any;
  timestamp: string;
}

/**
 * 状态观察者接口
 */
export interface StateObserver {
  onStateChanged(event: StateChangeEvent): void;
  getObservedTypes(): StateChangeType[];
}

/**
 * 全局状态管理器
 */
export class StateManager {
  private static instance: StateManager;
  private state: AppState;
  private observers: Map<string, StateObserver> = new Map();
  private changeHistory: StateChangeEvent[] = [];
  private readonly maxHistorySize = 100;
  
  // 初始状态
  private readonly initialState: AppState = {
    userSettings: null,
    notes: [],
    selectedNote: null,
    searchResults: [],
    currentFilter: {},
    capsules: [],
    expiredCapsules: [],
    isLoading: false,
    isRefreshing: false,
    isEditing: false,
    currentView: 'timeline',
    lastSyncTime: '',
    hasUnsavedChanges: false,
    stats: {
      totalNotes: 0,
      totalCapsules: 0,
      writingStreak: 0,
      thisMonthNotes: 0
    }
  };
  
  private constructor() {
    this.state = { ...this.initialState };
  }
  
  /**
   * 获取单例实例
   */
  public static getInstance(): StateManager {
    if (!StateManager.instance) {
      StateManager.instance = new StateManager();
    }
    return StateManager.instance;
  }
  
  /**
   * 获取当前状态
   */
  public getState(): AppState {
    return { ...this.state };
  }
  
  /**
   * 更新状态
   */
  public updateState(updates: Partial<AppState>, changeType?: StateChangeType): void {
    const previousState = { ...this.state };
    this.state = { ...this.state, ...updates };
    
    if (changeType) {
      const event: StateChangeEvent = {
        type: changeType,
        payload: updates,
        timestamp: new Date().toISOString()
      };
      
      this.recordChangeEvent(event);
      this.notifyObservers(event);
    }
    
    // 验证状态一致性
    this.validateStateConsistency();
  }
  
  /**
   * 重置状态
   */
  public resetState(): void {
    this.state = { ...this.initialState };
    this.changeHistory = [];
    
    const event: StateChangeEvent = {
      type: 'UI_STATE_CHANGED',
      payload: { reset: true },
      timestamp: new Date().toISOString()
    };
    
    this.notifyObservers(event);
  }
  
  /**
   * 注册状态观察者
   */
  public registerObserver(id: string, observer: StateObserver): void {
    this.observers.set(id, observer);
  }
  
  /**
   * 注销状态观察者
   */
  public unregisterObserver(id: string): void {
    this.observers.delete(id);
  }
  
  /**
   * 通知观察者状态变化
   */
  private notifyObservers(event: StateChangeEvent): void {
    for (const observer of this.observers.values()) {
      if (observer.getObservedTypes().includes(event.type)) {
        try {
          observer.onStateChanged(event);
        } catch (error) {
          console.error(`Error notifying observer:`, error);
        }
      }
    }
  }
  
  /**
   * 记录状态变更历史
   */
  private recordChangeEvent(event: StateChangeEvent): void {
    this.changeHistory.push(event);
    
    // 保持历史记录在限制范围内
    if (this.changeHistory.length > this.maxHistorySize) {
      this.changeHistory.shift();
    }
  }
  
  /**
   * 获取状态变更历史
   */
  public getChangeHistory(): StateChangeEvent[] {
    return [...this.changeHistory];
  }
  
  /**
   * 验证状态一致性
   */
  private validateStateConsistency(): void {
    // 检查笔记数量统计
    const actualNoteCount = this.state.notes.filter(note => !note.isDeleted).length;
    if (this.state.stats.totalNotes !== actualNoteCount) {
      console.warn(`Note count inconsistency: stats=${this.state.stats.totalNotes}, actual=${actualNoteCount}`);
    }
    
    // 检查时间胶囊数量统计
    const actualCapsuleCount = this.state.capsules.length;
    if (this.state.stats.totalCapsules !== actualCapsuleCount) {
      console.warn(`Capsule count inconsistency: stats=${this.state.stats.totalCapsules}, actual=${actualCapsuleCount}`);
    }
  }
  
  /**
   * 获取特定状态片段
   */
  public getStateSlice<T extends keyof AppState>(key: T): AppState[T] {
    return this.state[key];
  }
  
  /**
   * 批量更新状态
   */
  public batchUpdate(updates: Array<{
    stateUpdate: Partial<AppState>;
    changeType: StateChangeType;
  }>): void {
    updates.forEach(({ stateUpdate, changeType }) => {
      this.updateState(stateUpdate, changeType);
    });
  }
  
  /**
   * 检查是否有未保存的更改
   */
  public hasUnsavedChanges(): boolean {
    return this.state.hasUnsavedChanges;
  }
  
  /**
   * 标记未保存的更改
   */
  public markUnsavedChanges(hasChanges: boolean): void {
    this.updateState({ hasUnsavedChanges: hasChanges }, 'UI_STATE_CHANGED');
  }
  
  /**
   * 获取当前过滤器
   */
  public getCurrentFilter(): AppState['currentFilter'] {
    return { ...this.state.currentFilter };
  }
  
  /**
   * 设置过滤器
   */
  public setFilter(filter: AppState['currentFilter']): void {
    this.updateState({ currentFilter: filter }, 'FILTER_CHANGED');
  }
  
  /**
   * 清除过滤器
   */
  public clearFilter(): void {
    this.updateState({ currentFilter: {} }, 'FILTER_CHANGED');
  }
  
  /**
   * 获取选中的笔记
   */
  public getSelectedNote(): NoteModel | null {
    return this.state.selectedNote;
  }
  
  /**
   * 选择笔记
   */
  public selectNote(note: NoteModel | null): void {
    this.updateState({ selectedNote: note }, 'NOTE_SELECTED');
  }
  
  /**
   * 获取搜索结果
   */
  public getSearchResults(): NoteModel[] {
    return [...this.state.searchResults];
  }
  
  /**
   * 设置搜索结果
   */
  public setSearchResults(results: NoteModel[]): void {
    this.updateState({ searchResults: results }, 'SEARCH_RESULTS_UPDATED');
  }
  
  /**
   * 获取过期的胶囊
   */
  public getExpiredCapsules(): CapsuleModel[] {
    return [...this.state.expiredCapsules];
  }
  
  /**
   * 设置过期的胶囊
   */
  public setExpiredCapsules(capsules: CapsuleModel[]): void {
    this.updateState({ expiredCapsules: capsules }, 'CAPSULES_LOADED');
  }
  
  /**
   * 获取加载状态
   */
  public getLoadingState(): { isLoading: boolean; isRefreshing: boolean } {
    return {
      isLoading: this.state.isLoading,
      isRefreshing: this.state.isRefreshing
    };
  }
  
  /**
   * 设置加载状态
   */
  public setLoadingState(isLoading: boolean, isRefreshing: boolean = false): void {
    this.updateState({ isLoading, isRefreshing }, 'UI_STATE_CHANGED');
  }
  
  /**
   * 获取编辑状态
   */
  public getEditingState(): boolean {
    return this.state.isEditing;
  }
  
  /**
   * 设置编辑状态
   */
  public setEditingState(isEditing: boolean): void {
    this.updateState({ isEditing }, 'UI_STATE_CHANGED');
  }
  
  /**
   * 获取当前视图
   */
  public getCurrentView(): AppState['currentView'] {
    return this.state.currentView;
  }
  
  /**
   * 切换视图
   */
  public switchView(view: AppState['currentView']): void {
    this.updateState({ currentView: view }, 'UI_STATE_CHANGED');
  }
  
  /**
   * 获取统计信息
   */
  public getStats(): AppState['stats'] {
    return { ...this.state.stats };
  }
  
  /**
   * 更新统计信息
   */
  public updateStats(stats: Partial<AppState['stats']>): void {
    this.updateState({ 
      stats: { ...this.state.stats, ...stats } 
    }, 'STATS_UPDATED');
  }
  
  /**
   * 获取同步时间
   */
  public getLastSyncTime(): string {
    return this.state.lastSyncTime;
  }
  
  /**
   * 更新同步时间
   */
  public updateLastSyncTime(time: string): void {
    this.updateState({ lastSyncTime: time }, 'CACHE_UPDATED');
  }
  
  /**
   * 检查是否需要同步
   */
  public needsSync(): boolean {
    if (!this.state.lastSyncTime) return true;
    
    const lastSync = new Date(this.state.lastSyncTime);
    const now = new Date();
    const hoursSinceLastSync = (now.getTime() - lastSync.getTime()) / (1000 * 60 * 60);
    
    return hoursSinceLastSync > 1; // 超过1小时需要同步
  }
  
  /**
   * 创建状态快照
   */
  public createSnapshot(): AppState {
    return JSON.parse(JSON.stringify(this.state));
  }
  
  /**
   * 从快照恢复状态
   */
  public restoreFromSnapshot(snapshot: AppState): void {
    this.state = { ...snapshot };
    
    const event: StateChangeEvent = {
      type: 'UI_STATE_CHANGED',
      payload: { restored: true },
      timestamp: new Date().toISOString()
    };
    
    this.notifyObservers(event);
  }
}