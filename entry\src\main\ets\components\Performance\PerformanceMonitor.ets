// 性能监控系统
import { hilog } from '@kit.PerformanceAnalysisKit';

// 性能指标接口
export interface PerformanceMetrics {
  timestamp: number;
  fps: number;
  memory: {
    used: number;
    total: number;
    percentage: number;
  };
  cpu: {
    usage: number;
  };
  network: {
    latency: number;
    throughput: number;
  };
  render: {
    frameTime: number;
    jsTime: number;
    layoutTime: number;
  };
  battery: {
    level: number;
    charging: boolean;
  };
}

// 性能事件类型
export enum PerformanceEventType {
  PAGE_LOAD = 'page_load',
  NAVIGATION = 'navigation',
  USER_INTERACTION = 'user_interaction',
  API_CALL = 'api_call',
  RENDER = 'render',
  MEMORY_WARNING = 'memory_warning',
  ERROR = 'error'
}

// 性能事件接口
export interface PerformanceEvent {
  id: string;
  type: PerformanceEventType;
  timestamp: number;
  duration: number;
  metadata: any;
  stack?: string;
}

// 性能告警级别
export enum PerformanceAlertLevel {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  CRITICAL = 'critical'
}

// 性能告警接口
export interface PerformanceAlert {
  id: string;
  level: PerformanceAlertLevel;
  type: string;
  message: string;
  timestamp: number;
  metrics: PerformanceMetrics;
  recommendations: string[];
}

// 性能监控配置
export interface PerformanceMonitorConfig {
  sampleInterval: number; // 采样间隔（毫秒）
  enableFPS: boolean; // 启用FPS监控
  enableMemory: boolean; // 启用内存监控
  enableCPU: boolean; // 启用CPU监控
  enableNetwork: boolean; // 启用网络监控
  enableRender: boolean; // 启用渲染监控
  enableBattery: boolean; // 启用电池监控
  alertThresholds: {
    fps: number; // FPS阈值
    memory: number; // 内存阈值
    cpu: number; // CPU阈值
    renderTime: number; // 渲染时间阈值
  };
  maxMetricsHistory: number; // 最大指标历史记录数
  maxEventsHistory: number; // 最大事件历史记录数
  enableAutoReporting: boolean; // 启用自动上报
  enableRealtimeAnalysis: boolean; // 启用实时分析
}

// 性能分析器
export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private config: PerformanceMonitorConfig;
  private isRunning: boolean = false;
  private metrics: PerformanceMetrics[] = [];
  private events: PerformanceEvent[] = [];
  private alerts: PerformanceAlert[] = [];
  private sampleTimer: number | null = null;
  private frameCount: number = 0;
  private lastFrameTime: number = 0;
  private currentFPS: number = 0;
  private listeners: Set<(metrics: PerformanceMetrics) => void> = new Set();
  private eventListeners: Set<(event: PerformanceEvent) => void> = new Set();
  private alertListeners: Set<(alert: PerformanceAlert) => void> = new Set();
  
  private constructor(config: PerformanceMonitorConfig = {}) {
    const defaultConfig: PerformanceMonitorConfig = {
      sampleInterval: 1000,
      enableFPS: true,
      enableMemory: true,
      enableCPU: true,
      enableNetwork: true,
      enableRender: true,
      enableBattery: true,
      alertThresholds: {
        fps: 30,
        memory: 80,
        cpu: 70,
        renderTime: 16
      },
      maxMetricsHistory: 100,
      maxEventsHistory: 200,
      enableAutoReporting: false,
      enableRealtimeAnalysis: true
    };
    
    this.config = { ...defaultConfig, ...config };
  }
  
  static getInstance(config?: PerformanceMonitorConfig): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor(config);
    }
    return PerformanceMonitor.instance;
  }
  
  /**
   * 启动性能监控
   */
  start(): void {
    if (this.isRunning) {
      hilog.warn(0x0000, 'PerformanceMonitor', '性能监控已在运行');
      return;
    }
    
    this.isRunning = true;
    this.startSampling();
    this.startFPSMonitoring();
    
    hilog.info(0x0000, 'PerformanceMonitor', '性能监控已启动');
  }
  
  /**
   * 停止性能监控
   */
  stop(): void {
    if (!this.isRunning) {
      return;
    }
    
    this.isRunning = false;
    
    if (this.sampleTimer) {
      clearInterval(this.sampleTimer);
      this.sampleTimer = null;
    }
    
    hilog.info(0x0000, 'PerformanceMonitor', '性能监控已停止');
  }
  
  /**
   * 开始采样
   */
  private startSampling(): void {
    this.sampleTimer = setInterval(() => {
      this.collectMetrics();
    }, this.config.sampleInterval);
  }
  
  /**
   * 开始FPS监控
   */
  private startFPSMonitoring(): void {
    this.lastFrameTime = Date.now();
    this.frameCount = 0;
    
    const updateFPS = () => {
      const now = Date.now();
      const delta = now - this.lastFrameTime;
      
      if (delta >= 1000) {
        this.currentFPS = (this.frameCount * 1000) / delta;
        this.frameCount = 0;
        this.lastFrameTime = now;
      }
      
      this.frameCount++;
      requestAnimationFrame(updateFPS);
    };
    
    requestAnimationFrame(updateFPS);
  }
  
  /**
   * 收集性能指标
   */
  private async collectMetrics(): Promise<void> {
    try {
      const metrics: PerformanceMetrics = {
        timestamp: Date.now(),
        fps: this.currentFPS,
        memory: this.config.enableMemory ? await this.collectMemoryMetrics() : { used: 0, total: 0, percentage: 0 },
        cpu: this.config.enableCPU ? await this.collectCPUMetrics() : { usage: 0 },
        network: this.config.enableNetwork ? await this.collectNetworkMetrics() : { latency: 0, throughput: 0 },
        render: this.config.enableRender ? await this.collectRenderMetrics() : { frameTime: 0, jsTime: 0, layoutTime: 0 },
        battery: this.config.enableBattery ? await this.collectBatteryMetrics() : { level: 0, charging: false }
      };
      
      this.metrics.push(metrics);
      
      // 限制历史记录数量
      if (this.metrics.length > this.config.maxMetricsHistory) {
        this.metrics = this.metrics.slice(-this.config.maxMetricsHistory);
      }
      
      // 通知监听器
      this.notifyListeners(metrics);
      
      // 实时分析
      if (this.config.enableRealtimeAnalysis) {
        this.analyzePerformance(metrics);
      }
      
    } catch (error) {
      hilog.error(0x0000, 'PerformanceMonitor', '收集性能指标失败:', error);
    }
  }
  
  /**
   * 收集内存指标
   */
  private async collectMemoryMetrics(): Promise<{ used: number; total: number; percentage: number }> {
    // 模拟内存指标收集
    const total = 128; // 128MB
    const used = Math.random() * 80 + 20; // 20-100MB
    
    return {
      used,
      total,
      percentage: (used / total) * 100
    };
  }
  
  /**
   * 收集CPU指标
   */
  private async collectCPUMetrics(): Promise<{ usage: number }> {
    // 模拟CPU指标收集
    return {
      usage: Math.random() * 100 // 0-100%
    };
  }
  
  /**
   * 收集网络指标
   */
  private async collectNetworkMetrics(): Promise<{ latency: number; throughput: number }> {
    // 模拟网络指标收集
    return {
      latency: Math.random() * 100 + 10, // 10-110ms
      throughput: Math.random() * 10 + 1 // 1-11MB/s
    };
  }
  
  /**
   * 收集渲染指标
   */
  private async collectRenderMetrics(): Promise<{ frameTime: number; jsTime: number; layoutTime: number }> {
    // 模拟渲染指标收集
    return {
      frameTime: Math.random() * 20 + 2, // 2-22ms
      jsTime: Math.random() * 10 + 1, // 1-11ms
      layoutTime: Math.random() * 5 + 1 // 1-6ms
    };
  }
  
  /**
   * 收集电池指标
   */
  private async collectBatteryMetrics(): Promise<{ level: number; charging: boolean }> {
    // 模拟电池指标收集
    return {
      level: Math.random() * 100, // 0-100%
      charging: Math.random() > 0.7 // 30%概率在充电
    };
  }
  
  /**
   * 分析性能
   */
  private analyzePerformance(metrics: PerformanceMetrics): void {
    const alerts: PerformanceAlert[] = [];
    
    // FPS分析
    if (metrics.fps < this.config.alertThresholds.fps) {
      alerts.push({
        id: `fps_${Date.now()}`,
        level: PerformanceAlertLevel.WARNING,
        type: 'low_fps',
        message: `FPS过低: ${metrics.fps.toFixed(1)} (阈值: ${this.config.alertThresholds.fps})`,
        timestamp: Date.now(),
        metrics,
        recommendations: [
          '减少DOM操作',
          '优化动画效果',
          '使用虚拟滚动',
          '避免频繁重排'
        ]
      });
    }
    
    // 内存分析
    if (metrics.memory.percentage > this.config.alertThresholds.memory) {
      alerts.push({
        id: `memory_${Date.now()}`,
        level: PerformanceAlertLevel.WARNING,
        type: 'high_memory',
        message: `内存使用率过高: ${metrics.memory.percentage.toFixed(1)}% (阈值: ${this.config.alertThresholds.memory}%)`,
        timestamp: Date.now(),
        metrics,
        recommendations: [
          '清理不必要的对象',
          '使用对象池',
          '优化数据结构',
          '启用内存压缩'
        ]
      });
    }
    
    // CPU分析
    if (metrics.cpu.usage > this.config.alertThresholds.cpu) {
      alerts.push({
        id: `cpu_${Date.now()}`,
        level: PerformanceAlertLevel.WARNING,
        type: 'high_cpu',
        message: `CPU使用率过高: ${metrics.cpu.usage.toFixed(1)}% (阈值: ${this.config.alertThresholds.cpu}%)`,
        timestamp: Date.now(),
        metrics,
        recommendations: [
          '减少计算密集型操作',
          '使用Web Worker',
          '优化算法复杂度',
          '分批处理任务'
        ]
      });
    }
    
    // 渲染分析
    if (metrics.render.frameTime > this.config.alertThresholds.renderTime) {
      alerts.push({
        id: `render_${Date.now()}`,
        level: PerformanceAlertLevel.WARNING,
        type: 'slow_render',
        message: `渲染时间过长: ${metrics.render.frameTime.toFixed(1)}ms (阈值: ${this.config.alertThresholds.renderTime}ms)`,
        timestamp: Date.now(),
        metrics,
        recommendations: [
          '优化CSS选择器',
          '减少重排重绘',
          '使用硬件加速',
          '避免强制同步布局'
        ]
      });
    }
    
    // 处理告警
    alerts.forEach(alert => {
      this.handleAlert(alert);
    });
  }
  
  /**
   * 处理告警
   */
  private handleAlert(alert: PerformanceAlert): void {
    this.alerts.push(alert);
    
    // 限制告警历史
    if (this.alerts.length > 50) {
      this.alerts = this.alerts.slice(-50);
    }
    
    // 通知告警监听器
    this.notifyAlertListeners(alert);
    
    // 记录告警事件
    this.recordEvent({
      type: PerformanceEventType.ERROR,
      duration: 0,
      metadata: {
        alertType: alert.type,
        alertLevel: alert.level,
        message: alert.message
      }
    });
    
    if (this.config.enableAutoReporting) {
      this.reportAlert(alert);
    }
  }
  
  /**
   * 记录性能事件
   */
  recordEvent(event: {
    type: PerformanceEventType;
    duration: number;
    metadata: any;
    stack?: string;
  }): void {
    const performanceEvent: PerformanceEvent = {
      id: `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: event.type,
      timestamp: Date.now(),
      duration: event.duration,
      metadata: event.metadata,
      stack: event.stack
    };
    
    this.events.push(performanceEvent);
    
    // 限制事件历史
    if (this.events.length > this.config.maxEventsHistory) {
      this.events = this.events.slice(-this.config.maxEventsHistory);
    }
    
    // 通知事件监听器
    this.notifyEventListeners(performanceEvent);
  }
  
  /**
   * 上报告警
   */
  private reportAlert(alert: PerformanceAlert): void {
    hilog.info(0x0000, 'PerformanceMonitor', '上报性能告警:', JSON.stringify(alert));
    // 在实际环境中，这里会上报到分析服务
  }
  
  /**
   * 通知监听器
   */
  private notifyListeners(metrics: PerformanceMetrics): void {
    this.listeners.forEach(listener => {
      try {
        listener(metrics);
      } catch (error) {
        hilog.error(0x0000, 'PerformanceMonitor', '监听器调用失败:', error);
      }
    });
  }
  
  /**
   * 通知事件监听器
   */
  private notifyEventListeners(event: PerformanceEvent): void {
    this.eventListeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        hilog.error(0x0000, 'PerformanceMonitor', '事件监听器调用失败:', error);
      }
    });
  }
  
  /**
   * 通知告警监听器
   */
  private notifyAlertListeners(alert: PerformanceAlert): void {
    this.alertListeners.forEach(listener => {
      try {
        listener(alert);
      } catch (error) {
        hilog.error(0x0000, 'PerformanceMonitor', '告警监听器调用失败:', error);
      }
    });
  }
  
  /**
   * 添加指标监听器
   */
  addMetricsListener(listener: (metrics: PerformanceMetrics) => void): void {
    this.listeners.add(listener);
  }
  
  /**
   * 移除指标监听器
   */
  removeMetricsListener(listener: (metrics: PerformanceMetrics) => void): void {
    this.listeners.delete(listener);
  }
  
  /**
   * 添加事件监听器
   */
  addEventListener(listener: (event: PerformanceEvent) => void): void {
    this.eventListeners.add(listener);
  }
  
  /**
   * 移除事件监听器
   */
  removeEventListener(listener: (event: PerformanceEvent) => void): void {
    this.eventListeners.delete(listener);
  }
  
  /**
   * 添加告警监听器
   */
  addAlertListener(listener: (alert: PerformanceAlert) => void): void {
    this.alertListeners.add(listener);
  }
  
  /**
   * 移除告警监听器
   */
  removeAlertListener(listener: (alert: PerformanceAlert) => void): void {
    this.alertListeners.delete(listener);
  }
  
  /**
   * 获取当前性能指标
   */
  getCurrentMetrics(): PerformanceMetrics | null {
    return this.metrics.length > 0 ? this.metrics[this.metrics.length - 1] : null;
  }
  
  /**
   * 获取性能统计
   */
  getPerformanceStats(): {
    averageFPS: number;
    averageMemory: number;
    averageCPU: number;
    averageRenderTime: number;
    totalEvents: number;
    totalAlerts: number;
    uptime: number;
  } {
    if (this.metrics.length === 0) {
      return {
        averageFPS: 0,
        averageMemory: 0,
        averageCPU: 0,
        averageRenderTime: 0,
        totalEvents: this.events.length,
        totalAlerts: this.alerts.length,
        uptime: 0
      };
    }
    
    const startTime = this.metrics[0].timestamp;
    const uptime = Date.now() - startTime;
    
    const averageFPS = this.metrics.reduce((sum, m) => sum + m.fps, 0) / this.metrics.length;
    const averageMemory = this.metrics.reduce((sum, m) => sum + m.memory.percentage, 0) / this.metrics.length;
    const averageCPU = this.metrics.reduce((sum, m) => sum + m.cpu.usage, 0) / this.metrics.length;
    const averageRenderTime = this.metrics.reduce((sum, m) => sum + m.render.frameTime, 0) / this.metrics.length;
    
    return {
      averageFPS,
      averageMemory,
      averageCPU,
      averageRenderTime,
      totalEvents: this.events.length,
      totalAlerts: this.alerts.length,
      uptime
    };
  }
  
  /**
   * 获取性能报告
   */
  getPerformanceReport(): {
    summary: any;
    metrics: PerformanceMetrics[];
    events: PerformanceEvent[];
    alerts: PerformanceAlert[];
    recommendations: string[];
  } {
    const stats = this.getPerformanceStats();
    const currentMetrics = this.getCurrentMetrics();
    
    const recommendations: string[] = [];
    
    // 生成建议
    if (stats.averageFPS < 45) {
      recommendations.push('优化页面渲染性能，减少不必要的重绘');
    }
    
    if (stats.averageMemory > 70) {
      recommendations.push('优化内存使用，及时释放不需要的对象');
    }
    
    if (stats.averageCPU > 60) {
      recommendations.push('减少CPU密集型操作，考虑使用Web Worker');
    }
    
    if (stats.averageRenderTime > 12) {
      recommendations.push('优化渲染管道，减少布局计算');
    }
    
    return {
      summary: {
        ...stats,
        currentMetrics,
        health: this.calculateHealthScore(stats)
      },
      metrics: [...this.metrics],
      events: [...this.events],
      alerts: [...this.alerts],
      recommendations
    };
  }
  
  /**
   * 计算健康度评分
   */
  private calculateHealthScore(stats: any): number {
    let score = 100;
    
    // FPS评分
    score -= Math.max(0, 60 - stats.averageFPS) * 0.5;
    
    // 内存评分
    score -= Math.max(0, stats.averageMemory - 50) * 0.3;
    
    // CPU评分
    score -= Math.max(0, stats.averageCPU - 30) * 0.2;
    
    // 渲染评分
    score -= Math.max(0, stats.averageRenderTime - 8) * 0.3;
    
    return Math.max(0, Math.min(100, score));
  }
  
  /**
   * 清理历史数据
   */
  clearHistory(): void {
    this.metrics = [];
    this.events = [];
    this.alerts = [];
    hilog.info(0x0000, 'PerformanceMonitor', '性能历史数据已清理');
  }
}

// 性能监控装饰器
export function PerformanceTracked(operationName: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    
    descriptor.value = async function (...args: any[]) {
      const startTime = Date.now();
      const monitor = PerformanceMonitor.getInstance();
      
      try {
        const result = await originalMethod.apply(this, args);
        const duration = Date.now() - startTime;
        
        monitor.recordEvent({
          type: PerformanceEventType.USER_INTERACTION,
          duration,
          metadata: {
            operation: operationName,
            success: true
          }
        });
        
        return result;
      } catch (error) {
        const duration = Date.now() - startTime;
        
        monitor.recordEvent({
          type: PerformanceEventType.ERROR,
          duration,
          metadata: {
            operation: operationName,
            success: false,
            error: error.message
          }
        });
        
        throw error;
      }
    };
  };
}

// 使用示例
@Component
export struct PerformanceMonitorExample {
  @State private currentMetrics: any = null;
  @State private performanceStats: any = null;
  @State private alerts: any[] = [];
  @State private isMonitoring: boolean = false;
  @State private showReport: boolean = false;
  
  private monitor: PerformanceMonitor = PerformanceMonitor.getInstance();
  
  aboutToAppear() {
    this.setupPerformanceMonitoring();
  }
  
  /**
   * 设置性能监控
   */
  private setupPerformanceMonitoring() {
    // 添加指标监听器
    this.monitor.addMetricsListener((metrics) => {
      this.currentMetrics = metrics;
    });
    
    // 添加事件监听器
    this.monitor.addEventListener((event) => {
      hilog.info(0x0000, 'PerformanceExample', '性能事件:', JSON.stringify(event));
    });
    
    // 添加告警监听器
    this.monitor.addAlertListener((alert) => {
      this.alerts.push(alert);
      // 限制告警显示数量
      if (this.alerts.length > 10) {
        this.alerts = this.alerts.slice(-10);
      }
    });
  }
  
  /**
   * 开始监控
   */
  private startMonitoring() {
    this.monitor.start();
    this.isMonitoring = true;
    this.alerts = [];
  }
  
  /**
   * 停止监控
   */
  private stopMonitoring() {
    this.monitor.stop();
    this.isMonitoring = false;
  }
  
  /**
   * 获取性能报告
   */
  private getPerformanceReport() {
    this.performanceStats = this.monitor.getPerformanceReport();
    this.showReport = true;
  }
  
  /**
   * 清理历史数据
   */
  private clearHistory() {
    this.monitor.clearHistory();
    this.alerts = [];
    this.performanceStats = null;
    this.showReport = false;
  }
  
  /**
   * 模拟性能密集型操作
   */
  @PerformanceTracked('intensive_calculation')
  private async simulateIntensiveOperation() {
    // 模拟计算密集型操作
    const startTime = Date.now();
    let result = 0;
    
    for (let i = 0; i < 1000000; i++) {
      result += Math.sqrt(i) * Math.random();
    }
    
    await new Promise(resolve => setTimeout(resolve, 100));
    
    hilog.info(0x0000, 'PerformanceExample', `密集型操作完成，结果: ${result.toFixed(2)}`);
    return result;
  }
  
  build() {
    Column() {
      // 标题
      Text('性能监控示例')
        .fontSize(20)
        .fontWeight(600)
        .margin({ bottom: 16 });
      
      // 控制按钮
      Row() {
        Button(this.isMonitoring ? '停止监控' : '开始监控')
          .fontSize(14)
          .fontColor(Color.White)
          .backgroundColor(this.isMonitoring ? '#FF3B30' : '#007AFF')
          .borderRadius(6)
          .onClick(() => {
            if (this.isMonitoring) {
              this.stopMonitoring();
            } else {
              this.startMonitoring();
            }
          });
        
        Button('性能报告')
          .fontSize(14)
          .fontColor(Color.White)
          .backgroundColor('#34C759')
          .borderRadius(6)
          .margin({ left: 8 })
          .onClick(() => {
            this.getPerformanceReport();
          });
      }
      .margin({ bottom: 16 });
      
      Row() {
        Button('密集操作')
          .fontSize(14)
          .fontColor(Color.White)
          .backgroundColor('#FF9500')
          .borderRadius(6)
          .onClick(() => {
            this.simulateIntensiveOperation();
          });
        
        Button('清理历史')
          .fontSize(14)
          .fontColor(Color.White)
          .backgroundColor('#AF52DE')
          .borderRadius(6)
          .margin({ left: 8 })
          .onClick(() => {
            this.clearHistory();
          });
      }
      .margin({ bottom: 16 });
      
      // 当前指标
      if (this.currentMetrics) {
        Column() {
          Text('当前性能指标')
            .fontSize(16)
            .fontWeight(500)
            .margin({ bottom: 8 });
          
          Row() {
            Text(`FPS: ${this.currentMetrics.fps.toFixed(1)}`)
              .fontSize(12)
              .fontColor('#666666');
            
            Text(`内存: ${this.currentMetrics.memory.percentage.toFixed(1)}%`)
              .fontSize(12)
              .fontColor('#666666')
              .margin({ left: 16 });
            
            Text(`CPU: ${this.currentMetrics.cpu.usage.toFixed(1)}%`)
              .fontSize(12)
              .fontColor('#666666')
              .margin({ left: 16 });
          }
          
          Row() {
            Text(`渲染: ${this.currentMetrics.render.frameTime.toFixed(1)}ms`)
              .fontSize(12)
              .fontColor('#666666');
            
            Text(`网络: ${this.currentMetrics.network.latency.toFixed(0)}ms`)
              .fontSize(12)
              .fontColor('#666666')
              .margin({ left: 16 });
          }
          .margin({ top: 4 });
        }
        .width('100%')
        .padding(12)
        .backgroundColor('#E8F4FD')
        .borderRadius(6)
        .margin({ bottom: 16 });
      }
      
      // 性能告警
      if (this.alerts.length > 0) {
        Column() {
          Text('⚠️ 性能告警')
            .fontSize(16)
            .fontWeight(500)
            .fontColor('#FF3B30')
            .margin({ bottom: 8 });
          
          ForEach(this.alerts.slice(-3), (alert: any) => {
            Text(`• ${alert.message}`)
              .fontSize(12)
              .fontColor('#666666')
              .margin({ bottom: 4 });
          });
        }
        .width('100%')
        .padding(12)
        .backgroundColor('#FFE8E8')
        .borderRadius(6)
        .margin({ bottom: 16 });
      }
      
      // 性能报告
      if (this.showReport && this.performanceStats) {
        Column() {
          Text('📊 性能报告')
            .fontSize(16)
            .fontWeight(500)
            .margin({ bottom: 8 });
          
          Text(`健康度: ${this.performanceStats.summary.health.toFixed(1)}/100`)
            .fontSize(14)
            .fontColor('#666666');
          
          Text(`平均FPS: ${this.performanceStats.summary.averageFPS.toFixed(1)}`)
            .fontSize(12)
            .fontColor('#666666');
          
          Text(`平均内存: ${this.performanceStats.summary.averageMemory.toFixed(1)}%`)
            .fontSize(12)
            .fontColor('#666666');
          
          Text(`平均CPU: ${this.performanceStats.summary.averageCPU.toFixed(1)}%`)
            .fontSize(12)
            .fontColor('#666666');
          
          Text(`运行时间: ${Math.floor(this.performanceStats.summary.uptime / 1000)}s`)
            .fontSize(12)
            .fontColor('#666666');
          
          if (this.performanceStats.recommendations.length > 0) {
            Text('优化建议:')
              .fontSize(14)
              .fontWeight(500)
              .margin({ top: 8, bottom: 4 });
            
            ForEach(this.performanceStats.recommendations, (rec: string) => {
              Text(`• ${rec}`)
                .fontSize(12)
                .fontColor('#666666');
            });
          }
        }
        .width('100%')
        .padding(12)
        .backgroundColor('#F0F8FF')
        .borderRadius(6)
        .margin({ bottom: 16 });
      }
      
      // 监控状态
      Text(`监控状态: ${this.isMonitoring ? '运行中' : '已停止'}`)
        .fontSize(14)
        .fontColor('#666666');
    }
    .width('100%')
    .height('100%')
    .padding(16)
    .backgroundColor('#F5F5F5');
  }
}