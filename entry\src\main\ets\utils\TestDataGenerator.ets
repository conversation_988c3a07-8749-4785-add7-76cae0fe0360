import { NoteModel } from '../model/NoteModel';
import { DatabaseService } from '../services/DatabaseService';
import { DateUtils } from '../utils/DateUtils';

/**
 * 测试数据生成器
 */
export class TestDataGenerator {
  private database: DatabaseService = DatabaseService.getInstance();
  
  /**
   * 生成测试数据
   */
  async generateTestData(): Promise<void> {
    try {
      const testNotes: NoteModel[] = [
        {
          id: 0,
          title: '今天的心情很好',
          content: '今天天气晴朗，心情也特别好。早上起床后，看到窗外的阳光，感觉整个世界都充满了希望。准备开始新的一天的工作，相信会有不错的收获。',
          categoryId: 1,
          tags: '心情,生活,感悟',
          createTime: DateUtils.now(),
          updateTime: DateUtils.now(),
          isDeleted: 0
        },
        {
          id: 0,
          title: '工作总结',
          content: '本周完成了项目的第一阶段开发，包括用户界面设计和基础功能实现。下周需要开始后端API的开发，同时进行单元测试。',
          categoryId: 2,
          tags: '工作,项目,总结',
          createTime: DateUtils.now(),
          updateTime: DateUtils.now(),
          isDeleted: 0
        },
        {
          id: 0,
          title: '学习笔记',
          content: '今天学习了HarmonyOS应用开发，了解了ArkUI框架的基本概念。ArkUI采用声明式UI编程范式，让界面开发更加简洁高效。',
          categoryId: 3,
          tags: '学习,HarmonyOS,ArkUI',
          createTime: DateUtils.now(),
          updateTime: DateUtils.now(),
          isDeleted: 0
        },
        {
          id: 0,
          title: '周末计划',
          content: '这个周末计划：1. 完成剩余的工作任务 2. 整理房间 3. 看一部电影 4. 准备下周的学习材料 5. 约朋友聚餐',
          categoryId: 4,
          tags: '计划,周末,生活',
          createTime: DateUtils.now(),
          updateTime: DateUtils.now(),
          isDeleted: 0
        },
        {
          id: 0,
          title: '生活感悟',
          content: '时间过得真快，转眼间又到了年底。回顾这一年，经历了很多事情，有快乐也有烦恼。但总的来说，收获满满，成长了很多。',
          categoryId: 1,
          tags: '感悟,生活,成长',
          createTime: DateUtils.now(),
          updateTime: DateUtils.now(),
          isDeleted: 0
        },
        {
          id: 0,
          title: '技术学习',
          content: '深入学习了TypeScript的高级特性，包括泛型、装饰器、模块系统等。这些知识对提高代码质量和开发效率很有帮助。',
          categoryId: 3,
          tags: '技术,TypeScript,学习',
          createTime: DateUtils.now(),
          updateTime: DateUtils.now(),
          isDeleted: 0
        },
        {
          id: 0,
          title: '项目进展',
          content: '移动应用开发进展顺利，已经完成了用户注册、登录、数据展示等核心功能。下一步需要实现搜索功能和数据同步。',
          categoryId: 2,
          tags: '项目,开发,进展',
          createTime: DateUtils.now(),
          updateTime: DateUtils.now(),
          isDeleted: 0
        },
        {
          id: 0,
          title: '读书笔记',
          content: '《人类简史》这本书让我对人类文明的发展有了新的认识。作者从认知革命、农业革命、科学革命等角度，生动地描述了人类历史的进程。',
          categoryId: 3,
          tags: '读书,笔记,历史',
          createTime: DateUtils.now(),
          updateTime: DateUtils.now(),
          isDeleted: 0
        },
        {
          id: 0,
          title: '健康计划',
          content: '制定新的健康计划：每天早上6点起床，进行30分钟的运动；晚上11点前睡觉；每天喝足够的水；减少垃圾食品的摄入。',
          categoryId: 4,
          tags: '健康,计划,生活',
          createTime: DateUtils.now(),
          updateTime: DateUtils.now(),
          isDeleted: 0
        },
        {
          id: 0,
          title: '工作反思',
          content: '最近在工作中遇到了一些挑战，主要是时间管理方面的问题。需要更好地规划工作时间，提高工作效率，避免拖延。',
          categoryId: 2,
          tags: '工作,反思,改进',
          createTime: DateUtils.now(),
          updateTime: DateUtils.now(),
          isDeleted: 0
        }
      ];
      
      // 保存测试数据
      for (const note of testNotes) {
        await this.database.saveNote(note);
      }
      
      console.info('Test data generated successfully');
    } catch (error) {
      console.error('Failed to generate test data:', error);
    }
  }
  
  /**
   * 清空测试数据
   */
  async clearTestData(): Promise<void> {
    try {
      const notes = await this.database.getNotes();
      for (const note of notes) {
        await this.database.deleteNote(note.id);
      }
      console.info('Test data cleared successfully');
    } catch (error) {
      console.error('Failed to clear test data:', error);
    }
  }
}