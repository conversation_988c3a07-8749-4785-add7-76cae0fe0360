// 内存泄漏检测和修复系统
import { hilog } from '@kit.PerformanceAnalysisKit';

// 内存监控数据接口
export interface MemoryData {
  timestamp: number;
  used: number;
  total: number;
  percentage: number;
  heapUsed: number;
  heapTotal: number;
  heapLimit: number;
}

// 内存泄漏警告级别
export enum MemoryWarningLevel {
  NORMAL = 'normal',
  WARNING = 'warning',
  CRITICAL = 'critical',
  DANGER = 'danger'
}

// 内存泄漏检测配置
export interface MemoryLeakDetectorConfig {
  checkInterval: number; // 检查间隔（毫秒）
  warningThreshold: number; // 警告阈值（百分比）
  criticalThreshold: number; // 严重阈值（百分比）
  dangerThreshold: number; // 危险阈值（百分比）
  maxMemorySamples: number; // 最大内存样本数
  enableAutoCleanup: boolean; // 启用自动清理
  enableDetailedTracking: boolean; // 启用详细跟踪
  logToConsole: boolean; // 记录到控制台
  reportToAnalytics: boolean; // 上报到分析服务
}

// 对象引用跟踪器
interface ObjectTracker {
  id: string;
  type: string;
  createTime: number;
  lastAccessTime: number;
  accessCount: number;
  size: number;
  stack?: string;
  markedForCleanup: boolean;
}

// 内存泄漏检测器
export class MemoryLeakDetector {
  private static instance: MemoryLeakDetector;
  private config: MemoryLeakDetectorConfig;
  private memoryData: MemoryData[] = [];
  private objectTrackers: Map<string, ObjectTracker> = new Map();
  private checkTimer: number | null = null;
  private isRunning: boolean = false;
  private listeners: Set<(data: MemoryData) => void> = new Set();
  private leakListeners: Set<(report: MemoryLeakReport) => void> = new Set();
  
  private constructor(config: MemoryLeakDetectorConfig = {}) {
    const defaultConfig: MemoryLeakDetectorConfig = {
      checkInterval: 5000,
      warningThreshold: 70,
      criticalThreshold: 85,
      dangerThreshold: 95,
      maxMemorySamples: 100,
      enableAutoCleanup: true,
      enableDetailedTracking: true,
      logToConsole: true,
      reportToAnalytics: false
    };
    
    this.config = { ...defaultConfig, ...config };
  }
  
  static getInstance(config?: MemoryLeakDetectorConfig): MemoryLeakDetector {
    if (!MemoryLeakDetector.instance) {
      MemoryLeakDetector.instance = new MemoryLeakDetector(config);
    }
    return MemoryLeakDetector.instance;
  }
  
  /**
   * 启动内存监控
   */
  start(): void {
    if (this.isRunning) {
      hilog.warn(0x0000, 'MemoryLeakDetector', '内存监控已在运行');
      return;
    }
    
    this.isRunning = true;
    this.startMonitoring();
    
    hilog.info(0x0000, 'MemoryLeakDetector', '内存泄漏检测已启动');
  }
  
  /**
   * 停止内存监控
   */
  stop(): void {
    if (!this.isRunning) {
      return;
    }
    
    this.isRunning = false;
    
    if (this.checkTimer) {
      clearInterval(this.checkTimer);
      this.checkTimer = null;
    }
    
    hilog.info(0x0000, 'MemoryLeakDetector', '内存泄漏检测已停止');
  }
  
  /**
   * 开始监控
   */
  private startMonitoring(): void {
    this.checkTimer = setInterval(() => {
      this.checkMemory();
    }, this.config.checkInterval);
    
    // 立即执行一次检查
    this.checkMemory();
  }
  
  /**
   * 检查内存使用情况
   */
  private async checkMemory(): Promise<void> {
    try {
      const memoryData = await this.collectMemoryData();
      this.memoryData.push(memoryData);
      
      // 限制数据样本数量
      if (this.memoryData.length > this.config.maxMemorySamples) {
        this.memoryData = this.memoryData.slice(-this.config.maxMemorySamples);
      }
      
      // 通知监听器
      this.notifyListeners(memoryData);
      
      // 检查内存泄漏
      const leakReport = this.detectMemoryLeaks(memoryData);
      if (leakReport.hasLeak) {
        this.notifyLeakListeners(leakReport);
        this.handleMemoryLeak(leakReport);
      }
      
      // 检查内存警告级别
      const warningLevel = this.getMemoryWarningLevel(memoryData);
      if (warningLevel !== MemoryWarningLevel.NORMAL) {
        this.handleMemoryWarning(memoryData, warningLevel);
      }
      
    } catch (error) {
      hilog.error(0x0000, 'MemoryLeakDetector', '内存检查失败:', error);
    }
  }
  
  /**
   * 收集内存数据
   */
  private async collectMemoryData(): Promise<MemoryData> {
    // 模拟内存数据收集
    // 在实际环境中，这里会调用系统API获取真实的内存使用情况
    const totalMemory = 128; // 总内存 128MB
    const usedMemory = Math.random() * 80 + 20; // 使用内存 20-100MB
    const heapTotal = Math.random() * 64 + 32; // 堆内存 32-96MB
    const heapUsed = heapTotal * (Math.random() * 0.8 + 0.2); // 堆使用 20%-100%
    
    return {
      timestamp: Date.now(),
      used: usedMemory,
      total: totalMemory,
      percentage: (usedMemory / totalMemory) * 100,
      heapUsed: heapUsed,
      heapTotal: heapTotal,
      heapLimit: 128
    };
  }
  
  /**
   * 检测内存泄漏
   */
  private detectMemoryLeaks(currentData: MemoryData): MemoryLeakReport {
    if (this.memoryData.length < 3) {
      return { hasLeak: false, details: [] };
    }
    
    const recentData = this.memoryData.slice(-5);
    const memoryTrend = this.calculateMemoryTrend(recentData);
    
    const details: MemoryLeakDetail[] = [];
    
    // 检查持续增长趋势
    if (memoryTrend.slope > 0.5) { // 每次检查增长超过0.5%
      details.push({
        type: 'continuous_growth',
        severity: 'high',
        message: `内存持续增长，增长速度: ${memoryTrend.slope.toFixed(2)}%/检查`,
        data: { slope: memoryTrend.slope, rSquared: memoryTrend.rSquared }
      });
    }
    
    // 检查内存使用率过高
    if (currentData.percentage > this.config.warningThreshold) {
      details.push({
        type: 'high_usage',
        severity: currentData.percentage > this.config.criticalThreshold ? 'critical' : 'medium',
        message: `内存使用率过高: ${currentData.percentage.toFixed(1)}%`,
        data: { percentage: currentData.percentage }
      });
    }
    
    // 检查对象引用泄漏
    if (this.config.enableDetailedTracking) {
      const objectLeaks = this.detectObjectLeaks();
      details.push(...objectLeaks);
    }
    
    return {
      hasLeak: details.length > 0,
      timestamp: Date.now(),
      currentMemory: currentData,
      details: details
    };
  }
  
  /**
   * 计算内存趋势
   */
  private calculateMemoryTrend(data: MemoryData[]): { slope: number; rSquared: number } {
    if (data.length < 2) {
      return { slope: 0, rSquared: 0 };
    }
    
    const n = data.length;
    const xValues = data.map((_, i) => i);
    const yValues = data.map(d => d.percentage);
    
    const xMean = xValues.reduce((a, b) => a + b) / n;
    const yMean = yValues.reduce((a, b) => a + b) / n;
    
    const numerator = xValues.reduce((sum, x, i) => sum + (x - xMean) * (yValues[i] - yMean), 0);
    const denominator = xValues.reduce((sum, x) => sum + Math.pow(x - xMean, 2), 0);
    
    const slope = denominator !== 0 ? numerator / denominator : 0;
    
    // 计算R²
    const ssRes = yValues.reduce((sum, y, i) => {
      const yPred = slope * (xValues[i] - xMean) + yMean;
      return sum + Math.pow(y - yPred, 2);
    }, 0);
    
    const ssTot = yValues.reduce((sum, y) => sum + Math.pow(y - yMean, 2), 0);
    const rSquared = ssTot !== 0 ? 1 - (ssRes / ssTot) : 0;
    
    return { slope, rSquared };
  }
  
  /**
   * 检测对象引用泄漏
   */
  private detectObjectLeaks(): MemoryLeakDetail[] {
    const details: MemoryLeakDetail[] = [];
    const now = Date.now();
    
    // 检查长时间未访问的对象
    for (const [id, tracker] of this.objectTrackers) {
      const timeSinceLastAccess = now - tracker.lastAccessTime;
      
      if (timeSinceLastAccess > 30000 && !tracker.markedForCleanup) { // 30秒未访问
        details.push({
          type: 'stale_object',
          severity: 'low',
          message: `发现长时间未访问的对象: ${tracker.type} (ID: ${id})`,
          data: {
            objectId: id,
            type: tracker.type,
            timeSinceLastAccess: timeSinceLastAccess,
            accessCount: tracker.accessCount
          }
        });
        
        // 标记为可清理
        tracker.markedForCleanup = true;
      }
    }
    
    return details;
  }
  
  /**
   * 获取内存警告级别
   */
  private getMemoryWarningLevel(data: MemoryData): MemoryWarningLevel {
    if (data.percentage >= this.config.dangerThreshold) {
      return MemoryWarningLevel.DANGER;
    } else if (data.percentage >= this.config.criticalThreshold) {
      return MemoryWarningLevel.CRITICAL;
    } else if (data.percentage >= this.config.warningThreshold) {
      return MemoryWarningLevel.WARNING;
    }
    return MemoryWarningLevel.NORMAL;
  }
  
  /**
   * 处理内存泄漏
   */
  private handleMemoryLeak(report: MemoryLeakReport): void {
    if (this.config.logToConsole) {
      hilog.warn(0x0000, 'MemoryLeakDetector', '检测到内存泄漏:', JSON.stringify(report));
    }
    
    // 自动清理
    if (this.config.enableAutoCleanup) {
      this.performAutoCleanup(report);
    }
    
    // 上报到分析服务
    if (this.config.reportToAnalytics) {
      this.reportToAnalytics(report);
    }
  }
  
  /**
   * 处理内存警告
   */
  private handleMemoryWarning(data: MemoryData, level: MemoryWarningLevel): void {
    if (this.config.logToConsole) {
      hilog.warn(0x0000, 'MemoryLeakDetector', `内存警告 (${level}): ${data.percentage.toFixed(1)}%`);
    }
    
    // 根据警告级别采取不同措施
    switch (level) {
      case MemoryWarningLevel.DANGER:
        this.emergencyCleanup();
        break;
      case MemoryWarningLevel.CRITICAL:
        this.aggressiveCleanup();
        break;
      case MemoryWarningLevel.WARNING:
        this.moderateCleanup();
        break;
    }
  }
  
  /**
   * 执行自动清理
   */
  private performAutoCleanup(report: MemoryLeakReport): void {
    hilog.info(0x0000, 'MemoryLeakDetector', '开始自动清理...');
    
    // 清理标记的对象
    this.cleanupMarkedObjects();
    
    // 清理事件监听器
    this.cleanupEventListeners();
    
    // 清理缓存
    this.cleanupCaches();
    
    hilog.info(0x0000, 'MemoryLeakDetector', '自动清理完成');
  }
  
  /**
   * 中度清理
   */
  private moderateCleanup(): void {
    hilog.info(0x0000, 'MemoryLeakDetector', '执行中度清理...');
    this.cleanupCaches();
    this.cleanupMarkedObjects();
  }
  
  /**
   * 激进清理
   */
  private aggressiveCleanup(): void {
    hilog.info(0x0000, 'MemoryLeakDetector', '执行激进清理...');
    this.cleanupCaches();
    this.cleanupMarkedObjects();
    this.cleanupEventListeners();
    this.forceGarbageCollection();
  }
  
  /**
   * 紧急清理
   */
  private emergencyCleanup(): void {
    hilog.warn(0x0000, 'MemoryLeakDetector', '执行紧急清理...');
    this.cleanupCaches();
    this.cleanupMarkedObjects();
    this.cleanupEventListeners();
    this.forceGarbageCollection();
    this.releaseMemory();
  }
  
  /**
   * 清理标记的对象
   */
  private cleanupMarkedObjects(): void {
    let cleanedCount = 0;
    for (const [id, tracker] of this.objectTrackers) {
      if (tracker.markedForCleanup) {
        this.objectTrackers.delete(id);
        cleanedCount++;
      }
    }
    hilog.info(0x0000, 'MemoryLeakDetector', `清理了 ${cleanedCount} 个标记对象`);
  }
  
  /**
   * 清理事件监听器
   */
  private cleanupEventListeners(): void {
    // 清理不再需要的事件监听器
    hilog.info(0x0000, 'MemoryLeakDetector', '清理事件监听器');
  }
  
  /**
   * 清理缓存
   */
  private cleanupCaches(): void {
    // 清理各种缓存
    hilog.info(0x0000, 'MemoryLeakDetector', '清理缓存');
  }
  
  /**
   * 强制垃圾回收
   */
  private forceGarbageCollection(): void {
    hilog.info(0x0000, 'MemoryLeakDetector', '强制垃圾回收');
    // 在实际环境中，这里会调用系统的垃圾回收机制
  }
  
  /**
   * 释放内存
   */
  private releaseMemory(): void {
    hilog.info(0x0000, 'MemoryLeakDetector', '释放内存');
    // 释放不必要的内存占用
  }
  
  /**
   * 上报到分析服务
   */
  private reportToAnalytics(report: MemoryLeakReport): void {
    hilog.info(0x0000, 'MemoryLeakDetector', '上报内存泄漏数据到分析服务');
    // 在实际环境中，这里会上报到分析服务
  }
  
  /**
   * 通知监听器
   */
  private notifyListeners(data: MemoryData): void {
    this.listeners.forEach(listener => {
      try {
        listener(data);
      } catch (error) {
        hilog.error(0x0000, 'MemoryLeakDetector', '监听器调用失败:', error);
      }
    });
  }
  
  /**
   * 通知泄漏监听器
   */
  private notifyLeakListeners(report: MemoryLeakReport): void {
    this.leakListeners.forEach(listener => {
      try {
        listener(report);
      } catch (error) {
        hilog.error(0x0000, 'MemoryLeakDetector', '泄漏监听器调用失败:', error);
      }
    });
  }
  
  /**
   * 添加内存数据监听器
   */
  addMemoryListener(listener: (data: MemoryData) => void): void {
    this.listeners.add(listener);
  }
  
  /**
   * 移除内存数据监听器
   */
  removeMemoryListener(listener: (data: MemoryData) => void): void {
    this.listeners.delete(listener);
  }
  
  /**
   * 添加泄漏监听器
   */
  addLeakListener(listener: (report: MemoryLeakReport) => void): void {
    this.leakListeners.add(listener);
  }
  
  /**
   * 移除泄漏监听器
   */
  removeLeakListener(listener: (report: MemoryLeakReport) => void): void {
    this.leakListeners.delete(listener);
  }
  
  /**
   * 跟踪对象
   */
  trackObject(id: string, type: string, size: number = 0): void {
    if (!this.config.enableDetailedTracking) {
      return;
    }
    
    const now = Date.now();
    const existingTracker = this.objectTrackers.get(id);
    
    if (existingTracker) {
      existingTracker.lastAccessTime = now;
      existingTracker.accessCount++;
      existingTracker.markedForCleanup = false;
    } else {
      this.objectTrackers.set(id, {
        id,
        type,
        createTime: now,
        lastAccessTime: now,
        accessCount: 1,
        size,
        markedForCleanup: false
      });
    }
  }
  
  /**
   * 取消跟踪对象
   */
  untrackObject(id: string): void {
    this.objectTrackers.delete(id);
  }
  
  /**
   * 获取内存统计信息
   */
  getMemoryStats(): {
    currentMemory: MemoryData | null;
    memoryTrend: { slope: number; rSquared: number };
    trackedObjects: number;
    warnings: MemoryWarningLevel;
  } {
    const currentMemory = this.memoryData.length > 0 ? this.memoryData[this.memoryData.length - 1] : null;
    const memoryTrend = this.memoryData.length >= 3 ? 
      this.calculateMemoryTrend(this.memoryData.slice(-5)) : 
      { slope: 0, rSquared: 0 };
    
    const warnings = currentMemory ? this.getMemoryWarningLevel(currentMemory) : MemoryWarningLevel.NORMAL;
    
    return {
      currentMemory,
      memoryTrend,
      trackedObjects: this.objectTrackers.size,
      warnings
    };
  }
  
  /**
   * 获取内存泄漏报告
   */
  getLeakReport(): MemoryLeakReport | null {
    if (!this.memoryData.length) {
      return null;
    }
    
    const currentData = this.memoryData[this.memoryData.length - 1];
    return this.detectMemoryLeaks(currentData);
  }
}

// 内存泄漏报告接口
export interface MemoryLeakReport {
  hasLeak: boolean;
  timestamp: number;
  currentMemory: MemoryData;
  details: MemoryLeakDetail[];
}

// 内存泄漏详情接口
export interface MemoryLeakDetail {
  type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  data: any;
}

// 内存监控装饰器
export function MemoryTracked(size: number = 0) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalGetter = descriptor.get;
    const originalSetter = descriptor.set;
    
    let trackerId: string | null = null;
    
    descriptor.get = function () {
      if (trackerId) {
        const detector = MemoryLeakDetector.getInstance();
        detector.trackObject(trackerId, target.constructor.name, size);
      }
      return originalGetter?.call(this);
    };
    
    descriptor.set = function (value: any) {
      if (originalSetter) {
        originalSetter.call(this, value);
      }
      
      if (!trackerId && value) {
        trackerId = `${target.constructor.name}_${propertyKey}_${Date.now()}`;
        const detector = MemoryLeakDetector.getInstance();
        detector.trackObject(trackerId, target.constructor.name, size);
      }
    };
  };
}

// 使用示例
@Component
export struct MemoryLeakDetectorExample {
  @State private memoryStats: any = null;
  @State private leakReport: any = null;
  @State private isMonitoring: boolean = false;
  
  private detector: MemoryLeakDetector = MemoryLeakDetector.getInstance();
  
  aboutToAppear() {
    this.setupMemoryMonitoring();
  }
  
  /**
   * 设置内存监控
   */
  private setupMemoryMonitoring() {
    // 添加内存监听器
    this.detector.addMemoryListener((data) => {
      this.memoryStats = data;
    });
    
    // 添加泄漏监听器
    this.detector.addLeakListener((report) => {
      this.leakReport = report;
    });
  }
  
  /**
   * 开始监控
   */
  private startMonitoring() {
    this.detector.start();
    this.isMonitoring = true;
  }
  
  /**
   * 停止监控
   */
  private stopMonitoring() {
    this.detector.stop();
    this.isMonitoring = false;
  }
  
  /**
   * 手动触发内存检查
   */
  private async checkMemory() {
    await this.detector.checkMemory();
  }
  
  /**
   * 模拟内存泄漏
   */
  private simulateMemoryLeak() {
    hilog.info(0x0000, 'MemoryLeakExample', '模拟内存泄漏...');
    
    // 创建大量对象
    for (let i = 0; i < 1000; i++) {
      const id = `leak_object_${i}_${Date.now()}`;
      this.detector.trackObject(id, 'LeakObject', Math.random() * 100);
    }
  }
  
  /**
   * 清理内存
   */
  private cleanupMemory() {
    hilog.info(0x0000, 'MemoryLeakExample', '清理内存...');
    
    // 执行清理
    const stats = this.detector.getMemoryStats();
    if (stats.warnings !== MemoryWarningLevel.NORMAL) {
      this.detector.performAutoCleanup(this.detector.getLeakReport()!);
    }
  }
  
  build() {
    Column() {
      // 标题
      Text('内存泄漏检测示例')
        .fontSize(20)
        .fontWeight(600)
        .margin({ bottom: 16 });
      
      // 控制按钮
      Row() {
        Button(this.isMonitoring ? '停止监控' : '开始监控')
          .fontSize(14)
          .fontColor(Color.White)
          .backgroundColor(this.isMonitoring ? '#FF3B30' : '#007AFF')
          .borderRadius(6)
          .onClick(() => {
            if (this.isMonitoring) {
              this.stopMonitoring();
            } else {
              this.startMonitoring();
            }
          });
        
        Button('检查内存')
          .fontSize(14)
          .fontColor(Color.White)
          .backgroundColor('#34C759')
          .borderRadius(6)
          .margin({ left: 8 })
          .onClick(() => {
            this.checkMemory();
          });
      }
      .margin({ bottom: 16 });
      
      Row() {
        Button('模拟泄漏')
          .fontSize(14)
          .fontColor(Color.White)
          .backgroundColor('#FF9500')
          .borderRadius(6)
          .onClick(() => {
            this.simulateMemoryLeak();
          });
        
        Button('清理内存')
          .fontSize(14)
          .fontColor(Color.White)
          .backgroundColor('#AF52DE')
          .borderRadius(6)
          .margin({ left: 8 })
          .onClick(() => {
            this.cleanupMemory();
          });
      }
      .margin({ bottom: 16 });
      
      // 内存统计信息
      if (this.memoryStats) {
        Column() {
          Text('内存统计信息')
            .fontSize(16)
            .fontWeight(500)
            .margin({ bottom: 8 });
          
          Text(`使用内存: ${this.memoryStats.used.toFixed(1)}MB / ${this.memoryStats.total}MB`)
            .fontSize(14)
            .fontColor('#666666');
          
          Text(`内存使用率: ${this.memoryStats.percentage.toFixed(1)}%`)
            .fontSize(14)
            .fontColor('#666666');
          
          Text(`堆内存: ${this.memoryStats.heapUsed.toFixed(1)}MB / ${this.memoryStats.heapTotal.toFixed(1)}MB`)
            .fontSize(14)
            .fontColor('#666666');
        }
        .width('100%')
        .padding(12)
        .backgroundColor('#E8F4FD')
        .borderRadius(6)
        .margin({ bottom: 16 });
      }
      
      // 内存泄漏报告
      if (this.leakReport && this.leakReport.hasLeak) {
        Column() {
          Text('⚠️ 内存泄漏警告')
            .fontSize(16)
            .fontWeight(500)
            .fontColor('#FF3B30')
            .margin({ bottom: 8 });
          
          ForEach(this.leakReport.details, (detail: any) => {
            Text(`• ${detail.message}`)
              .fontSize(12)
              .fontColor('#666666')
              .margin({ bottom: 4 });
          });
        }
        .width('100%')
        .padding(12)
        .backgroundColor('#FFE8E8')
        .borderRadius(6)
        .margin({ bottom: 16 });
      }
      
      // 监控状态
      Text(`监控状态: ${this.isMonitoring ? '运行中' : '已停止'}`)
        .fontSize(14)
        .fontColor('#666666');
    }
    .width('100%')
    .height('100%')
    .padding(16)
    .backgroundColor('#F5F5F5');
  }
}