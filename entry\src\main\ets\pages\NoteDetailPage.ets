import { NoteModel } from '../model/NoteModel';
import { MorandiColors } from '../constants/MorandiColors';
import { DateUtils } from '../utils/DateUtils';
import { DatabaseService } from '../services/DatabaseService';
import router from '@ohos.router';


@Component
struct NoteDetailPage {
  @State note: NoteModel | null = null;
  @State isLoading: boolean = true;
  @State isEditing: boolean = false;
  @State editedTitle: string = '';
  @State editedContent: string = '';
  @State editedTags: string = '';
  
  private database: DatabaseService = DatabaseService.getInstance();
  
  aboutToAppear() {
    const params = router.getParams() as { noteId: number };
    if (params && params.noteId) {
      this.loadNote(params.noteId);
    }
  }
  
  /**
   * 加载笔记数据
   */
  private async loadNote(noteId: number) {
    try {
      // 由于数据库服务没有按ID查询的方法，我们需要先获取所有笔记然后筛选
      const notes = await this.database.getNotes();
      const foundNote = notes.find(n => n.id === noteId);
      
      if (foundNote) {
        this.note = foundNote;
        this.editedTitle = foundNote.title;
        this.editedContent = foundNote.content;
        this.editedTags = foundNote.tags || '';
      }
    } catch (error) {
      console.error('Failed to load note:', error);
    } finally {
      this.isLoading = false;
    }
  }
  
  /**
   * 保存笔记修改
   */
  private async saveNote() {
    if (!this.note) return;
    
    try {
      const updatedNote: NoteModel = {
        ...this.note,
        title: this.editedTitle,
        content: this.editedContent,
        tags: this.editedTags
      };
      
      await this.database.saveNote(updatedNote);
      this.note = updatedNote;
      this.isEditing = false;
    } catch (error) {
      console.error('Failed to save note:', error);
    }
  }
  
  /**
   * 删除笔记
   */
  private async deleteNote() {
    if (!this.note) return;
    
    try {
      await this.database.deleteNote(this.note.id);
      router.back();
    } catch (error) {
      console.error('Failed to delete note:', error);
    }
  }
  
  /**
   * 取消编辑
   */
  private cancelEdit() {
    if (this.note) {
      this.editedTitle = this.note.title;
      this.editedContent = this.note.content;
      this.editedTags = this.note.tags || '';
    }
    this.isEditing = false;
  }
  
  build() {
    Column() {
      // 顶部状态栏占位
      Row()
        .width('100%')
        .height(44)
        .backgroundColor(MorandiColors.background);
      
      // 页面内容
      if (this.isLoading) {
        Column() {
          LoadingProgress()
            .width(50)
            .height(50)
            .color(MorandiColors.accent);
          
          Text('加载中...')
            .fontSize(14)
            .fontColor(MorandiColors.textHint)
            .margin({ top: 16 });
        }
        .width('100%')
        .height('100%')
        .justifyContent(FlexAlign.Center);
      } else if (this.note) {
        this.NoteContent();
      } else {
        Column() {
          Text('笔记不存在')
            .fontSize(16)
            .fontColor(MorandiColors.textHint);
        }
        .width('100%')
        .height('100%')
        .justifyContent(FlexAlign.Center);
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor(MorandiColors.background);
  }
  
  /**
   * 笔记内容组件
   */
  @Builder NoteContent() {
    Column() {
      // 顶部导航栏
      Row() {
        // 返回按钮
        Text('←')
          .fontSize(24)
          .fontColor(MorandiColors.textPrimary)
          .onClick(() => router.back());
        
        // 标题
        Text('笔记详情')
          .fontSize(18)
          .fontWeight(500)
          .fontColor(MorandiColors.textPrimary)
          .layoutWeight(1);
        
        // 操作按钮
        if (this.isEditing) {
          Row() {
            Text('取消')
              .fontSize(14)
              .fontColor(MorandiColors.textHint)
              .onClick(() => this.cancelEdit());
            
            Text('保存')
              .fontSize(14)
              .fontColor(MorandiColors.accent)
              .onClick(() => this.saveNote());
          }
          .margin({ right: 8 });
        } else {
          Row() {
            Text('编辑')
              .fontSize(14)
              .fontColor(MorandiColors.textHint)
              .onClick(() => this.isEditing = true);
            
            Text('删除')
              .fontSize(14)
              .fontColor(MorandiColors.error)
              .onClick(() => this.deleteNote());
          }
          .margin({ right: 8 });
        }
      }
      .width('100%')
      .padding({ left: 24, right: 24, top: 16, bottom: 16 });
      
      // 笔记内容
      Scroll() {
        Column() {
          // 创建时间
          Row() {
            Text('创建时间：')
              .fontSize(12)
              .fontColor(MorandiColors.textTertiary);
            
            Text(DateUtils.formatDateTime(this.note!.createTime))
              .fontSize(12)
              .fontColor(MorandiColors.textTertiary);
          }
          .width('100%')
          .margin({ bottom: 16 });
          
          // 标题
          if (this.isEditing) {
            TextInput({ placeholder: '输入标题...' })
              .fontSize(20)
              .fontWeight(500)
              .fontColor(MorandiColors.textPrimary)
              .backgroundColor(MorandiColors.surface)
              .borderRadius(8)
              .padding(12)
              .margin({ bottom: 16 })
              .onChange((value: string) => this.editedTitle = value);
          } else {
            Text(this.note!.title)
              .fontSize(20)
              .fontWeight(500)
              .fontColor(MorandiColors.textPrimary)
              .margin({ bottom: 16 });
          }
          
          // 内容
          if (this.isEditing) {
            TextArea({ placeholder: '输入内容...' })
              .fontSize(16)
              .fontColor(MorandiColors.textSecondary)
              .backgroundColor(MorandiColors.surface)
              .borderRadius(8)
              .padding(12)
              .margin({ bottom: 16 })
              .onChange((value: string) => this.editedContent = value);
          } else {
            Text(this.note!.content)
              .fontSize(16)
              .fontColor(MorandiColors.textSecondary)
              .lineHeight(24)
              .margin({ bottom: 16 });
          }
          
          // 标签
          if (this.isEditing) {
            TextInput({ placeholder: '输入标签（用逗号分隔）...' })
              .fontSize(14)
              .fontColor(MorandiColors.textSecondary)
              .backgroundColor(MorandiColors.surface)
              .borderRadius(8)
              .padding(12)
              .margin({ bottom: 16 })
              .onChange((value: string) => this.editedTags = value);
          } else if (this.note!.tags) {
            Column() {
              Text('标签')
                .fontSize(14)
                .fontWeight(500)
                .fontColor(MorandiColors.textPrimary)
                .alignSelf(ItemAlign.Start)
                .margin({ bottom: 8 });
              
              Flex({ wrap: FlexWrap.Wrap }) {
                ForEach(this.note!.tags.split(','), (tag: string) => {
                  if (tag.trim()) {
                    Text(tag.trim())
                      .fontSize(12)
                      .fontColor(MorandiColors.textHint)
                      .backgroundColor(MorandiColors.border)
                      .padding({ left: 12, right: 12, top: 4, bottom: 4 })
                      .borderRadius(16)
                      .margin({ right: 8, bottom: 8 });
                  }
                });
              }
              .width('100%');
            }
            .width('100%')
            .margin({ bottom: 16 });
          }
        }
        .width('100%')
        .padding({ left: 24, right: 24, top: 16, bottom: 32 });
      }
      .layoutWeight(1);
    }
    .width('100%')
    .height('100%');
  }
}