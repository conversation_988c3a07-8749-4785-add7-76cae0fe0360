// 数据库服务单元测试
import { DatabaseService } from '../../services/DatabaseService';
import { MockDatabaseService, TestUtils, Assert, TestResult } from '../utils/TestFramework';
import { hilog } from '@kit.PerformanceAnalysisKit';

export class DatabaseServiceTest {
  private name = 'DatabaseServiceTest';
  private results: TestResult[] = [];
  private mockDatabaseService: MockDatabaseService;

  constructor() {
    this.mockDatabaseService = new MockDatabaseService();
  }

  async run(): Promise<TestResult[]> {
    hilog.info(0x0000, 'Test', '开始运行数据库服务测试...');
    
    try {
      await this.testInit();
      await this.testSaveNote();
      await this.testGetNotes();
      await this.testSearchNotes();
      await this.testDeleteNote();
      await this.testCreateCapsule();
      await this.testGetCapsules();
      await this.testGetSettings();
      await this.testPerformance();
    } catch (error) {
      hilog.error(0x0000, 'Test', '数据库服务测试失败:', error);
    }

    return this.results;
  }

  private async testInit(): Promise<void> {
    const testName = 'testInit';
    const startTime = Date.now();
    
    try {
      await TestUtils.measurePerformance(testName, async () => {
        await this.mockDatabaseService.init({} as Context);
        Assert.assertTrue(true, '数据库初始化成功');
      });
      
      this.results.push({
        name: testName,
        status: 'passed',
        duration: Date.now() - startTime
      });
    } catch (error) {
      this.results.push({
        name: testName,
        status: 'failed',
        duration: Date.now() - startTime,
        error: error.message
      });
    }
  }

  private async testSaveNote(): Promise<void> {
    const testName = 'testSaveNote';
    const startTime = Date.now();
    
    try {
      await TestUtils.measurePerformance(testName, async () => {
        const note = TestUtils.generateTestNote();
        const id = await this.mockDatabaseService.saveNote(note);
        
        Assert.assertTrue(id > 0, '笔记保存成功，返回有效ID');
        Assert.assertEquals(note.title, `测试笔记 1`, '笔记标题正确');
      });
      
      this.results.push({
        name: testName,
        status: 'passed',
        duration: Date.now() - startTime
      });
    } catch (error) {
      this.results.push({
        name: testName,
        status: 'failed',
        duration: Date.now() - startTime,
        error: error.message
      });
    }
  }

  private async testGetNotes(): Promise<void> {
    const testName = 'testGetNotes';
    const startTime = Date.now();
    
    try {
      await TestUtils.measurePerformance(testName, async () => {
        // 先保存一些测试数据
        for (let i = 1; i <= 5; i++) {
          await this.mockDatabaseService.saveNote(TestUtils.generateTestNote(i));
        }
        
        const notes = await this.mockDatabaseService.getNotes(0, 10);
        
        Assert.assertTrue(notes.length >= 5, '获取到足够的笔记数据');
        Assert.assertTrue(notes[0].id !== undefined, '笔记包含ID字段');
        Assert.assertTrue(notes[0].createTime !== undefined, '笔记包含创建时间字段');
      });
      
      this.results.push({
        name: testName,
        status: 'passed',
        duration: Date.now() - startTime
      });
    } catch (error) {
      this.results.push({
        name: testName,
        status: 'failed',
        duration: Date.now() - startTime,
        error: error.message
      });
    }
  }

  private async testSearchNotes(): Promise<void> {
    const testName = 'testSearchNotes';
    const startTime = Date.now();
    
    try {
      await TestUtils.measurePerformance(testName, async () => {
        // 保存包含特定关键词的笔记
        const specialNote = {
          ...TestUtils.generateTestNote(999),
          title: '特殊的测试笔记',
          content: '这是一个包含特殊关键词的笔记内容'
        };
        await this.mockDatabaseService.saveNote(specialNote);
        
        // 测试搜索
        const searchResults = await this.mockDatabaseService.searchNotes('特殊');
        
        Assert.assertTrue(searchResults.length > 0, '搜索结果不为空');
        Assert.assertTrue(searchResults[0].title.includes('特殊'), '搜索结果包含关键词');
      });
      
      this.results.push({
        name: testName,
        status: 'passed',
        duration: Date.now() - startTime
      });
    } catch (error) {
      this.results.push({
        name: testName,
        status: 'failed',
        duration: Date.now() - startTime,
        error: error.message
      });
    }
  }

  private async testDeleteNote(): Promise<void> {
    const testName = 'testDeleteNote';
    const startTime = Date.now();
    
    try {
      await TestUtils.measurePerformance(testName, async () => {
        // 先保存一个笔记
        const note = TestUtils.generateTestNote();
        const id = await this.mockDatabaseService.saveNote(note);
        
        // 删除笔记
        await this.mockDatabaseService.deleteNote(id);
        
        // 验证删除
        const notes = await this.mockDatabaseService.getNotes(0, 100);
        const deletedNote = notes.find(n => n.id === id);
        
        Assert.assertNull(deletedNote, '笔记已成功删除');
      });
      
      this.results.push({
        name: testName,
        status: 'passed',
        duration: Date.now() - startTime
      });
    } catch (error) {
      this.results.push({
        name: testName,
        status: 'failed',
        duration: Date.now() - startTime,
        error: error.message
      });
    }
  }

  private async testCreateCapsule(): Promise<void> {
    const testName = 'testCreateCapsule';
    const startTime = Date.now();
    
    try {
      await TestUtils.measurePerformance(testName, async () => {
        const capsule = TestUtils.generateTestCapsule();
        const id = await this.mockDatabaseService.createCapsule(capsule);
        
        Assert.assertTrue(id > 0, '时间胶囊创建成功，返回有效ID');
        Assert.assertEquals(capsule.title, `测试胶囊 1`, '时间胶囊标题正确');
      });
      
      this.results.push({
        name: testName,
        status: 'passed',
        duration: Date.now() - startTime
      });
    } catch (error) {
      this.results.push({
        name: testName,
        status: 'failed',
        duration: Date.now() - startTime,
        error: error.message
      });
    }
  }

  private async testGetCapsules(): Promise<void> {
    const testName = 'testGetCapsules';
    const startTime = Date.now();
    
    try {
      await TestUtils.measurePerformance(testName, async () => {
        // 先保存一些测试数据
        for (let i = 1; i <= 3; i++) {
          await this.mockDatabaseService.createCapsule(TestUtils.generateTestCapsule(i));
        }
        
        const capsules = await this.mockDatabaseService.getCapsules();
        
        Assert.assertTrue(capsules.length >= 3, '获取到足够的时间胶囊数据');
        Assert.assertTrue(capsules[0].id !== undefined, '时间胶囊包含ID字段');
        Assert.assertTrue(capsules[0].createTime !== undefined, '时间胶囊包含创建时间字段');
      });
      
      this.results.push({
        name: testName,
        status: 'passed',
        duration: Date.now() - startTime
      });
    } catch (error) {
      this.results.push({
        name: testName,
        status: 'failed',
        duration: Date.now() - startTime,
        error: error.message
      });
    }
  }

  private async testGetSettings(): Promise<void> {
    const testName = 'testGetSettings';
    const startTime = Date.now();
    
    try {
      await TestUtils.measurePerformance(testName, async () => {
        const settings = await this.mockDatabaseService.getSettings();
        
        Assert.assertNotNull(settings, '设置不为空');
        Assert.assertEquals(settings.darkMode, 2, '默认深色模式设置正确');
        Assert.assertEquals(settings.reminderTime, '21:00', '默认提醒时间正确');
        Assert.assertEquals(settings.fontSize, 16, '默认字体大小正确');
      });
      
      this.results.push({
        name: testName,
        status: 'passed',
        duration: Date.now() - startTime
      });
    } catch (error) {
      this.results.push({
        name: testName,
        status: 'failed',
        duration: Date.now() - startTime,
        error: error.message
      });
    }
  }

  private async testPerformance(): Promise<void> {
    const testName = 'testPerformance';
    const startTime = Date.now();
    
    try {
      await TestUtils.measurePerformance(testName, async () => {
        // 测试批量操作性能
        const batchStartTime = Date.now();
        
        // 批量保存100条笔记
        for (let i = 1; i <= 100; i++) {
          await this.mockDatabaseService.saveNote(TestUtils.generateTestNote(i));
        }
        
        const batchEndTime = Date.now();
        const batchDuration = batchEndTime - batchStartTime;
        
        hilog.info(0x0000, 'Test', `批量保存100条笔记耗时: ${batchDuration}ms`);
        
        // 测试查询性能
        const queryStartTime = Date.now();
        const notes = await this.mockDatabaseService.getNotes(0, 100);
        const queryEndTime = Date.now();
        const queryDuration = queryEndTime - queryStartTime;
        
        hilog.info(0x0000, 'Test', `查询100条笔记耗时: ${queryDuration}ms`);
        
        // 测试搜索性能
        const searchStartTime = Date.now();
        const searchResults = await this.mockDatabaseService.searchNotes('测试');
        const searchEndTime = Date.now();
        const searchDuration = searchEndTime - searchStartTime;
        
        hilog.info(0x0000, 'Test', `搜索包含"测试"的笔记耗时: ${searchDuration}ms`);
        
        // 性能断言
        Assert.assertTrue(batchDuration < 5000, `批量保存100条笔记应在5秒内完成，实际耗时: ${batchDuration}ms`);
        Assert.assertTrue(queryDuration < 1000, `查询100条笔记应在1秒内完成，实际耗时: ${queryDuration}ms`);
        Assert.assertTrue(searchDuration < 1000, `搜索应在1秒内完成，实际耗时: ${searchDuration}ms`);
        Assert.assertTrue(notes.length >= 100, '成功获取100条笔记');
        Assert.assertTrue(searchResults.length >= 100, '搜索结果包含足够的笔记');
      });
      
      this.results.push({
        name: testName,
        status: 'passed',
        duration: Date.now() - startTime
      });
    } catch (error) {
      this.results.push({
        name: testName,
        status: 'failed',
        duration: Date.now() - startTime,
        error: error.message
      });
    }
  }

  // 清理测试数据
  async cleanup(): Promise<void> {
    await this.mockDatabaseService.cleanup();
  }
}