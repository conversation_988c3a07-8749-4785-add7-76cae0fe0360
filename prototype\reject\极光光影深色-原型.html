<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时光拾光 - 极光光影（深色）原型</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: #0A0E1A;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .phone-container {
            width: 375px;
            height: 812px;
            background: linear-gradient(135deg, #0F1929 0%, #1A2332 100%);
            border-radius: 30px;
            overflow: hidden;
            box-shadow: 
                0 0 60px rgba(0, 255, 255, 0.1),
                0 0 100px rgba(138, 43, 226, 0.1),
                0 20px 40px rgba(0, 0, 0, 0.4);
            position: relative;
        }

        .status-bar {
            height: 44px;
            background: rgba(15, 25, 41, 0.8);
            backdrop-filter: blur(10px);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 24px;
            font-size: 14px;
            color: #E0E6ED;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        }

        .content {
            height: calc(100% - 44px);
            display: flex;
            flex-direction: column;
        }

        .header {
            padding: 20px 24px;
            background: linear-gradient(180deg, rgba(15, 25, 41, 0.9) 0%, rgba(26, 35, 50, 0.7) 100%);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        }

        .header h1 {
            font-size: 32px;
            font-weight: 200;
            color: #E0E6ED;
            margin-bottom: 8px;
            background: linear-gradient(135deg, #00FFFF 0%, #8A2BE2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            letter-spacing: 2px;
        }

        .header .date {
            font-size: 14px;
            color: #8892A6;
            letter-spacing: 0.5px;
        }

        .timeline {
            flex: 1;
            overflow-y: auto;
            padding: 0 24px;
            padding-bottom: 100px;
            background: linear-gradient(180deg, 
                rgba(15, 25, 41, 0) 0%, 
                rgba(10, 14, 26, 0.3) 100%);
        }

        .note-card {
            background: rgba(30, 41, 59, 0.6);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 20px;
            margin-bottom: 16px;
            position: relative;
            overflow: hidden;
            transition: all 0.4s ease;
        }

        .note-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, 
                transparent 0%, 
                rgba(0, 255, 255, 0.5) 50%, 
                transparent 100%);
            opacity: 0;
            transition: opacity 0.4s ease;
        }

        .note-card:hover {
            transform: translateY(-2px);
            border-color: rgba(0, 255, 255, 0.3);
            box-shadow: 
                0 10px 30px rgba(0, 255, 255, 0.1),
                0 0 20px rgba(138, 43, 226, 0.1);
        }

        .note-card:hover::before {
            opacity: 1;
        }

        .note-time {
            font-size: 12px;
            color: #8892A6;
            margin-bottom: 8px;
            letter-spacing: 0.5px;
        }

        .note-title {
            font-size: 18px;
            font-weight: 300;
            color: #E0E6ED;
            margin-bottom: 8px;
            line-height: 1.4;
        }

        .note-content {
            font-size: 14px;
            color: #B8C5D6;
            line-height: 1.6;
            margin-bottom: 12px;
        }

        .note-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .tag {
            font-size: 11px;
            color: #E0E6ED;
            background: rgba(0, 255, 255, 0.1);
            border: 1px solid rgba(0, 255, 255, 0.2);
            padding: 4px 12px;
            border-radius: 16px;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
        }

        .tag:hover {
            background: rgba(0, 255, 255, 0.2);
            border-color: rgba(0, 255, 255, 0.4);
            box-shadow: 0 0 10px rgba(0, 255, 255, 0.2);
        }

        .bottom-nav {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: rgba(15, 25, 41, 0.9);
            backdrop-filter: blur(20px);
            border-top: 1px solid rgba(255, 255, 255, 0.05);
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding-bottom: 20px;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            color: #5A6378;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }

        .nav-item.active {
            color: #00FFFF;
        }

        .nav-item.active::after {
            content: '';
            position: absolute;
            bottom: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 4px;
            height: 4px;
            background: #00FFFF;
            border-radius: 50%;
            box-shadow: 0 0 10px #00FFFF;
        }

        .nav-icon {
            width: 24px;
            height: 24px;
            fill: currentColor;
            transition: all 0.3s ease;
        }

        .nav-item:hover .nav-icon {
            transform: scale(1.1);
        }

        .nav-text {
            font-size: 10px;
            letter-spacing: 0.5px;
        }

        .fab {
            position: absolute;
            bottom: 100px;
            right: 24px;
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, #00FFFF 0%, #8A2BE2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 
                0 8px 20px rgba(0, 255, 255, 0.3),
                0 0 30px rgba(138, 43, 226, 0.2);
            cursor: pointer;
            transition: all 0.4s ease;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                box-shadow: 
                    0 8px 20px rgba(0, 255, 255, 0.3),
                    0 0 30px rgba(138, 43, 226, 0.2);
            }
            50% {
                box-shadow: 
                    0 8px 20px rgba(0, 255, 255, 0.5),
                    0 0 40px rgba(138, 43, 226, 0.3);
            }
            100% {
                box-shadow: 
                    0 8px 20px rgba(0, 255, 255, 0.3),
                    0 0 30px rgba(138, 43, 226, 0.2);
            }
        }

        .fab:hover {
            transform: scale(1.1) rotate(90deg);
        }

        .fab-icon {
            width: 24px;
            height: 24px;
            fill: #0A0E1A;
        }

        /* 滚动条样式 */
        .timeline::-webkit-scrollbar {
            width: 4px;
        }

        .timeline::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.05);
        }

        .timeline::-webkit-scrollbar-thumb {
            background: linear-gradient(180deg, #00FFFF 0%, #8A2BE2 100%);
            border-radius: 2px;
        }

        .category-glow {
            display: inline-block;
            width: 6px;
            height: 6px;
            border-radius: 50%;
            margin-right: 8px;
            vertical-align: middle;
        }

        .category-work { 
            background: #FF6B6B; 
            box-shadow: 0 0 10px #FF6B6B;
        }
        .category-life { 
            background: #4ECDC4; 
            box-shadow: 0 0 10px #4ECDC4;
        }
        .category-study { 
            background: #45B7D1; 
            box-shadow: 0 0 10px #45B7D1;
        }
        .category-plan { 
            background: #F7DC6F; 
            box-shadow: 0 0 10px #F7DC6F;
        }

        .floating-particles {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            overflow: hidden;
            pointer-events: none;
        }

        .particle {
            position: absolute;
            width: 2px;
            height: 2px;
            background: rgba(0, 255, 255, 0.5);
            border-radius: 50%;
            animation: float 20s infinite linear;
        }

        @keyframes float {
            from {
                transform: translateY(100vh) translateX(0);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            to {
                transform: translateY(-100vh) translateX(100px);
                opacity: 0;
            }
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <div class="floating-particles">
            <div class="particle" style="left: 10%; animation-delay: 0s;"></div>
            <div class="particle" style="left: 30%; animation-delay: 5s;"></div>
            <div class="particle" style="left: 50%; animation-delay: 10s;"></div>
            <div class="particle" style="left: 70%; animation-delay: 15s;"></div>
            <div class="particle" style="left: 90%; animation-delay: 20s;"></div>
        </div>
        
        <div class="status-bar">
            <span>9:41</span>
            <span>●●●●●</span>
            <span>100%</span>
        </div>
        
        <div class="content">
            <div class="header">
                <h1>时光拾光</h1>
                <div class="date">2024年3月15日 星期五</div>
            </div>
            
            <div class="timeline">
                <div class="note-card">
                    <div class="note-time">下午 2:30</div>
                    <div class="note-title">
                        <span class="category-glow category-life"></span>
                        春日随笔
                    </div>
                    <div class="note-content">今天在公园里看到了第一朵樱花，春天真的来了。阳光透过花瓣洒在地面上，形成斑驳的光影。</div>
                    <div class="note-tags">
                        <span class="tag">生活感悟</span>
                        <span class="tag">春天</span>
                    </div>
                </div>
                
                <div class="note-card">
                    <div class="note-time">上午 10:15</div>
                    <div class="note-title">
                        <span class="category-glow category-work"></span>
                        项目会议纪要
                    </div>
                    <div class="note-content">讨论了新产品的设计方案，确定了以用户体验为核心的设计理念。需要在下周完成原型设计。</div>
                    <div class="note-tags">
                        <span class="tag">工作</span>
                        <span class="tag">会议</span>
                    </div>
                </div>
                
                <div class="note-card">
                    <div class="note-time">昨天 晚上 8:45</div>
                    <div class="note-title">
                        <span class="category-glow category-study"></span>
                        读书笔记
                    </div>
                    <div class="note-content">《设计心理学》第三章：好的设计应该是直观的，用户不需要思考就能知道如何使用。</div>
                    <div class="note-tags">
                        <span class="tag">学习</span>
                        <span class="tag">设计</span>
                    </div>
                </div>
                
                <div class="note-card">
                    <div class="note-time">3月13日 下午 4:20</div>
                    <div class="note-title">
                        <span class="category-glow category-plan"></span>
                        周末计划
                    </div>
                    <div class="note-content">1. 去美术馆看展 2. 整理房间 3. 给父母打电话 4. 准备下周的演讲稿</div>
                    <div class="note-tags">
                        <span class="tag">计划</span>
                        <span class="tag">周末</span>
                    </div>
                </div>
                
                <div class="note-card">
                    <div class="note-time">3月12日 上午 11:00</div>
                    <div class="note-title">
                        <span class="category-glow category-life"></span>
                        美食探店
                    </div>
                    <div class="note-content">发现了一家很棒的咖啡馆，他们的手冲咖啡非常棒。店内装饰很有格调，适合一个人静静的看书。</div>
                    <div class="note-tags">
                        <span class="tag">美食</span>
                        <span class="tag">咖啡</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="bottom-nav">
            <div class="nav-item active">
                <svg class="nav-icon" viewBox="0 0 24 24">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
                <span class="nav-text">时间轴</span>
            </div>
            <div class="nav-item">
                <svg class="nav-icon" viewBox="0 0 24 24">
                    <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"/>
                </svg>
                <span class="nav-text">日历</span>
            </div>
            <div class="nav-item">
                <svg class="nav-icon" viewBox="0 0 24 24">
                    <path d="M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6z"/>
                </svg>
                <span class="nav-text">统计</span>
            </div>
            <div class="nav-item">
                <svg class="nav-icon" viewBox="0 0 24 24">
                    <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                </svg>
                <span class="nav-text">我的</span>
            </div>
        </div>
        
        <div class="fab">
            <svg class="fab-icon" viewBox="0 0 24 24">
                <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
            </svg>
        </div>
    </div>
</body>
</html>