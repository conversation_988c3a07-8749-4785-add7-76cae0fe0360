// 虚拟化列表组件 - 优化长列表性能
import { hilog } from '@kit.PerformanceAnalysisKit';

// 虚拟化列表项接口
export interface VirtualListItem {
  id: string | number;
  height: number;
  data: any;
}

// 虚拟化列表配置接口
export interface VirtualListConfig {
  itemHeight: number; // 默认项高度
  bufferSize: number; // 缓冲区大小（额外渲染的项数）
  overscanCount: number; // 过度渲染的项数
  enableDynamicHeight: boolean; // 是否启用动态高度
  estimatedItemHeight: number; // 预估项高度
}

// 虚拟化列表状态接口
export interface VirtualListState {
  startIndex: number; // 开始索引
  endIndex: number; // 结束索引
  scrollTop: number; // 滚动位置
  totalHeight: number; // 总高度
  visibleItems: VirtualListItem[]; // 可见项
  isScrolling: boolean; // 是否正在滚动
}

@Component
export struct VirtualList {
  @Prop items: VirtualListItem[];
  @Prop config: VirtualListConfig;
  @State private state: VirtualListState = {
    startIndex: 0,
    endIndex: 0,
    scrollTop: 0,
    totalHeight: 0,
    visibleItems: [],
    isScrolling: false
  };
  
  @State private itemHeights: Map<string | number, number> = new Map();
  @State private scrollTimer: number | null = null;
  @State private resizeObserver: any = null;
  
  private listRef: any = null;
  private containerRef: any = null;
  private onScrollCallback: ((scrollTop: number) => void) | null = null;
  private onEndReachedCallback: (() => void) | null = null;

  aboutToAppear() {
    this.initializeVirtualList();
    this.setupResizeObserver();
  }

  aboutToDisappear() {
    this.cleanup();
  }

  /**
   * 初始化虚拟列表
   */
  private initializeVirtualList() {
    const defaultConfig: VirtualListConfig = {
      itemHeight: 80,
      bufferSize: 5,
      overscanCount: 3,
      enableDynamicHeight: false,
      estimatedItemHeight: 80
    };
    
    this.config = { ...defaultConfig, ...this.config };
    this.updateVisibleItems();
  }

  /**
   * 设置尺寸观察器
   */
  private setupResizeObserver() {
    // 在实际环境中，这里会设置ResizeObserver来监听容器尺寸变化
    hilog.info(0x0000, 'VirtualList', '设置尺寸观察器');
  }

  /**
   * 清理资源
   */
  private cleanup() {
    if (this.scrollTimer) {
      clearTimeout(this.scrollTimer);
    }
    if (this.resizeObserver) {
      // 断开ResizeObserver
    }
  }

  /**
   * 更新可见项
   */
  private updateVisibleItems() {
    if (!this.items || this.items.length === 0) {
      this.state.visibleItems = [];
      this.state.totalHeight = 0;
      return;
    }

    const containerHeight = this.getContainerHeight();
    const { startIndex, endIndex } = this.calculateVisibleRange(containerHeight);
    
    // 计算总高度
    const totalHeight = this.calculateTotalHeight();
    
    // 获取可见项
    const visibleItems = this.items.slice(startIndex, endIndex + 1);
    
    this.state = {
      ...this.state,
      startIndex,
      endIndex,
      totalHeight,
      visibleItems,
      scrollTop: this.state.scrollTop
    };
  }

  /**
   * 计算可见范围
   */
  private calculateVisibleRange(containerHeight: number): { startIndex: number; endIndex: number } {
    const { scrollTop } = this.state;
    const { itemHeight, bufferSize, overscanCount } = this.config;
    
    // 计算可见区域的起始和结束索引
    let startIndex = Math.floor(scrollTop / itemHeight);
    let endIndex = Math.floor((scrollTop + containerHeight) / itemHeight);
    
    // 添加缓冲区
    startIndex = Math.max(0, startIndex - bufferSize);
    endIndex = Math.min(this.items.length - 1, endIndex + bufferSize);
    
    // 添加过度渲染
    startIndex = Math.max(0, startIndex - overscanCount);
    endIndex = Math.min(this.items.length - 1, endIndex + overscanCount);
    
    return { startIndex, endIndex };
  }

  /**
   * 计算总高度
   */
  private calculateTotalHeight(): number {
    if (this.config.enableDynamicHeight) {
      // 动态高度模式：使用已测量的高度
      let totalHeight = 0;
      for (let i = 0; i < this.items.length; i++) {
        const measuredHeight = this.itemHeights.get(this.items[i].id) || this.config.estimatedItemHeight;
        totalHeight += measuredHeight;
      }
      return totalHeight;
    } else {
      // 固定高度模式
      return this.items.length * this.config.itemHeight;
    }
  }

  /**
   * 获取容器高度
   */
  private getContainerHeight(): number {
    // 在实际环境中，这里会获取容器的实际高度
    return 600; // 模拟容器高度
  }

  /**
   * 处理滚动事件
   */
  private onScroll(scrollTop: number) {
    this.state.scrollTop = scrollTop;
    this.state.isScrolling = true;
    
    // 防抖更新可见项
    if (this.scrollTimer) {
      clearTimeout(this.scrollTimer);
    }
    
    this.scrollTimer = setTimeout(() => {
      this.updateVisibleItems();
      this.state.isScrolling = false;
      
      // 检查是否滚动到底部
      if (this.isNearBottom()) {
        this.onEndReached();
      }
    }, 16); // 60fps
    
    // 调用外部滚动回调
    if (this.onScrollCallback) {
      this.onScrollCallback(scrollTop);
    }
  }

  /**
   * 检查是否接近底部
   */
  private isNearBottom(): boolean {
    const { scrollTop } = this.state;
    const containerHeight = this.getContainerHeight();
    const totalHeight = this.state.totalHeight;
    
    return scrollTop + containerHeight >= totalHeight - 100; // 距离底部100px时触发
  }

  /**
   * 滚动到底部回调
   */
  private onEndReached() {
    if (this.onEndReachedCallback) {
      this.onEndReachedCallback();
    }
  }

  /**
   * 更新项高度
   */
  private updateItemHeight(itemId: string | number, height: number) {
    if (this.config.enableDynamicHeight) {
      this.itemHeights.set(itemId, height);
      this.updateVisibleItems();
    }
  }

  /**
   * 滚动到指定位置
   */
  public scrollTo(index: number, behavior: 'auto' | 'smooth' = 'auto') {
    if (index < 0 || index >= this.items.length) return;
    
    let scrollTop = 0;
    if (this.config.enableDynamicHeight) {
      // 动态高度模式：计算累计高度
      for (let i = 0; i < index; i++) {
        const height = this.itemHeights.get(this.items[i].id) || this.config.estimatedItemHeight;
        scrollTop += height;
      }
    } else {
      // 固定高度模式
      scrollTop = index * this.config.itemHeight;
    }
    
    this.state.scrollTop = scrollTop;
    this.updateVisibleItems();
  }

  /**
   * 滚动到顶部
   */
  public scrollToTop() {
    this.scrollTo(0);
  }

  /**
   * 滚动到底部
   */
  public scrollToBottom() {
    this.scrollTo(this.items.length - 1);
  }

  /**
   * 刷新列表
   */
  public refresh() {
    this.updateVisibleItems();
  }

  /**
   * 设置滚动回调
   */
  public setOnScroll(callback: (scrollTop: number) => void) {
    this.onScrollCallback = callback;
  }

  /**
   * 设置到达底部回调
   */
  public setOnEndReached(callback: () => void) {
    this.onEndReachedCallback = callback;
  }

  build() {
    Column() {
      // 列表容器
      Stack() {
        // 总高度占位元素
        Column()
          .width('100%')
          .height(this.state.totalHeight)
          .backgroundColor(Color.Transparent);
        
        // 可见项容器
        Column() {
          ForEach(this.state.visibleItems, (item: VirtualListItem) => {
            this.ItemRenderer({
              item: item,
              index: this.state.startIndex + this.state.visibleItems.indexOf(item),
              onHeightMeasured: (height: number) => {
                this.updateItemHeight(item.id, height);
              }
            });
          });
        }
        .width('100%')
        .position({ y: this.calculateVisibleItemsOffset() })
        .backgroundColor(Color.Transparent);
      }
      .width('100%')
      .height('100%')
      .clip(true)
      .onScroll((event: ScrollEvent) => {
        this.onScroll(event.scrollY);
      });
    }
    .width('100%')
    .height('100%')
    .backgroundColor(Color.Transparent);
  }

  /**
   * 计算可见项偏移量
   */
  private calculateVisibleItemsOffset(): number {
    if (this.config.enableDynamicHeight) {
      // 动态高度模式：计算到开始索引的累计高度
      let offset = 0;
      for (let i = 0; i < this.state.startIndex; i++) {
        const height = this.itemHeights.get(this.items[i].id) || this.config.estimatedItemHeight;
        offset += height;
      }
      return offset;
    } else {
      // 固定高度模式
      return this.state.startIndex * this.config.itemHeight;
    }
  }

  /**
   * 项渲染器
   */
  @Builder ItemRenderer(params: {
    item: VirtualListItem;
    index: number;
    onHeightMeasured: (height: number) => void;
  }) {
    Column() {
      // 这里渲染实际的列表项内容
      // 由外部通过@Builder提供
      Column() {
        // 默认渲染项数据
        Text(`Item ${params.item.id}`)
          .fontSize(16)
          .fontColor('#333333')
          .padding(16);
      }
      .width('100%')
      .backgroundColor('#FFFFFF')
      .borderRadius(8)
      .margin({ bottom: 8 });
    }
    .width('100%')
    .onAppear(() => {
      // 项出现时测量高度
      if (this.config.enableDynamicHeight) {
        setTimeout(() => {
          // 在实际环境中，这里会测量实际高度
          const estimatedHeight = params.item.height || this.config.estimatedItemHeight;
          params.onHeightMeasured(estimatedHeight);
        }, 0);
      }
    });
  }
}

// 使用示例组件
@Component
export struct VirtualListExample {
  @State private items: VirtualListItem[] = [];
  @State private loading: boolean = false;
  
  aboutToAppear() {
    this.generateTestData();
  }

  /**
   * 生成测试数据
   */
  private generateTestData() {
    const testData: VirtualListItem[] = [];
    for (let i = 0; i < 1000; i++) {
      testData.push({
        id: i,
        height: 60 + Math.random() * 40, // 随机高度60-100px
        data: {
          title: `项目 ${i}`,
          content: `这是第 ${i} 个项目的内容`,
          timestamp: new Date().toISOString()
        }
      });
    }
    this.items = testData;
  }

  /**
   * 加载更多数据
   */
  private loadMore() {
    if (this.loading) return;
    
    this.loading = true;
    hilog.info(0x0000, 'VirtualList', '加载更多数据...');
    
    // 模拟异步加载
    setTimeout(() => {
      const startIndex = this.items.length;
      const newData: VirtualListItem[] = [];
      
      for (let i = 0; i < 20; i++) {
        newData.push({
          id: startIndex + i,
          height: 60 + Math.random() * 40,
          data: {
            title: `新项目 ${startIndex + i}`,
            content: `这是新加载的第 ${startIndex + i} 个项目`,
            timestamp: new Date().toISOString()
          }
        });
      }
      
      this.items = [...this.items, ...newData];
      this.loading = false;
      hilog.info(0x0000, 'VirtualList', `加载了 ${newData.length} 条新数据`);
    }, 1000);
  }

  build() {
    Column() {
      // 顶部工具栏
      Row() {
        Text('虚拟化列表示例')
          .fontSize(18)
          .fontWeight(600);
        
        Blank();
        
        Text(`共 ${this.items.length} 项`)
          .fontSize(14)
          .fontColor('#666666');
      }
      .width('100%')
      .padding(16)
      .backgroundColor('#F5F5F5');
      
      // 虚拟化列表
      VirtualList({
        items: this.items,
        config: {
          itemHeight: 80,
          bufferSize: 5,
          overscanCount: 3,
          enableDynamicHeight: true,
          estimatedItemHeight: 80
        }
      }) {
        // 自定义项渲染
        @Builder itemRenderer(item: VirtualListItem, index: number) {
          Column() {
            Row() {
              Column() {
                Text(item.data.title)
                  .fontSize(16)
                  .fontWeight(500)
                  .fontColor('#333333');
                
                Text(item.data.content)
                  .fontSize(14)
                  .fontColor('#666666')
                  .margin({ top: 4 });
              }
              .alignItems(HorizontalAlign.Start)
              .layoutWeight(1);
              
              Text(new Date(item.data.timestamp).toLocaleTimeString())
                .fontSize(12)
                .fontColor('#999999');
            }
            .width('100%')
            .alignItems(VerticalAlign.Center);
          }
          .width('100%')
          .padding(16)
          .backgroundColor('#FFFFFF')
          .borderRadius(8)
          .shadow({
            radius: 2,
            color: '#000000',
            offsetX: 0,
            offsetY: 1,
            opacity: 0.1
          });
        }
      }
      .layoutWeight(1)
      .setOnEndReached(() => {
        this.loadMore();
      });
      
      // 加载指示器
      if (this.loading) {
        Row() {
          LoadingProgress()
            .width(20)
            .height(20)
            .color('#007AFF');
          
          Text('加载更多...')
            .fontSize(14)
            .fontColor('#666666')
            .margin({ left: 8 });
        }
        .width('100%')
        .height(50)
        .justifyContent(FlexAlign.Center)
        .backgroundColor('#F5F5F5');
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F5F5F5');
  }
}