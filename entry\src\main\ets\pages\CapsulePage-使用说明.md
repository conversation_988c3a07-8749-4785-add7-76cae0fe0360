# 时间胶囊页面使用说明

## 功能概述

时间胶囊页面是TimeNotes应用的核心功能之一，允许用户创建和管理时间胶囊，在指定的时间后才能开启查看。

## 主要功能

### 1. 创建时间胶囊
- 点击右上角的"创建"按钮
- 填写胶囊标题和内容
- 设置开启日期和时间
- 选择胶囊分类（回忆、目标、感悟、祝福、秘密）
- 可选择是否定时开启

### 2. 胶囊列表显示
- 显示所有时间胶囊的列表
- 每个胶囊显示：
  - 分类图标和名称
  - 胶囊标题
  - 锁定状态（🔒图标）
  - 倒计时或状态文本
  - 开启日期

### 3. 胶囊状态
- **已开启**：胶囊已经开启，可以查看内容
- **可开启**：胶囊已到开启时间，可以点击开启
- **锁定中**：胶囊还未到开启时间，显示剩余天数
- **今日可开启**：今天可以开启的胶囊
- **明日可开启**：明天可以开启的胶囊

### 4. 操作功能
- **开启**：点击"开启"按钮查看胶囊内容
- **编辑**：修改未开启的胶囊信息
- **删除**：删除时间胶囊

### 5. 智能分类
系统会根据胶囊标题和内容自动分类：
- **回忆** 🕰️：包含"回忆"、"过去"、"记忆"等关键词
- **目标** 🎯：包含"目标"、"计划"等关键词
- **感悟** 💭：包含"感悟"、"思考"、"感想"等关键词
- **祝福** 🌟：包含"祝福"、"希望"、"愿望"等关键词
- **秘密** 🔐：其他内容默认分类

## 技术实现

### 文件结构
```
entry/src/main/ets/
├── pages/
│   └── CapsulePage.ets              # 时间胶囊页面主文件
├── services/
│   ├── CapsuleService.ets           # 时间胶囊服务类
│   └── DatabaseService.ets          # 数据库服务类
├── model/
│   └── NoteModel.ets                # 数据模型定义
├── utils/
│   └── DateUtils.ets                # 日期时间工具类
└── constants/
    ├── MorandiColors.ets            # 莫兰迪色系定义
    └── AppConstants.ets             # 应用常量定义
```

### 核心组件

#### 1. CapsulePage (主页面)
- 胶囊列表展示
- 创建/编辑弹窗
- 胶囊详情弹窗
- 状态管理和用户交互

#### 2. CapsuleService (服务类)
- 胶囊CRUD操作
- 状态计算和管理
- 智能分类
- 数据验证

#### 3. CapsuleDialogContent (弹窗组件)
- 创建/编辑表单
- 分类选择
- 日期时间设置
- 表单验证

#### 4. CapsuleDetailContent (详情组件)
- 胶囊内容展示
- 创建和开启时间
- 分类信息

### 数据库设计

#### 时间胶囊表 (capsules)
```sql
CREATE TABLE capsules (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  title TEXT NOT NULL,              -- 胶囊标题
  content TEXT NOT NULL,            -- 胶囊内容
  openDate TEXT NOT NULL,           -- 开启日期
  createTime TEXT NOT NULL,         -- 创建时间
  isOpened INTEGER DEFAULT 0,       -- 是否已开启 (0/1)
  isOpenTime INTEGER DEFAULT 0      -- 是否定时开启 (0/1)
)
```

### 特色功能

#### 1. 实时倒计时
- 每分钟自动更新倒计时
- 智能显示剩余时间格式
- 状态文本动态更新

#### 2. 莫兰迪色系设计
- 使用柔和的莫兰迪色系
- 分类颜色区分
- 统一的视觉风格

#### 3. 响应式布局
- 适配不同屏幕尺寸
- 弹窗自适应内容
- 列表滚动优化

#### 4. 数据验证
- 开启时间必须在未来
- 标题和内容不能为空
- 时间范围限制（1天-10年）

#### 5. 智能分类
- 基于关键词的自动分类
- 支持用户手动选择
- 分类颜色标识

## 使用示例

### 创建时间胶囊
1. 点击右上角"创建"按钮
2. 输入标题："写给一年后的自己"
3. 输入内容："希望明年的你已经实现了所有目标..."
4. 设置开启日期：2024-12-31 23:59:59
5. 选择分类：目标
6. 点击"保存"

### 开启时间胶囊
1. 在胶囊列表中找到状态为"可开启"的胶囊
2. 点击"开启"按钮
3. 查看胶囊内容详情
4. 了解创建和开启时间

### 编辑时间胶囊
1. 找到未开启的胶囊
2. 点击"编辑"按钮
3. 修改胶囊信息
4. 点击"保存"更新

## 注意事项

1. 已开启的时间胶囊不能编辑或删除
2. 开启时间必须在当前时间之后
3. 胶囊内容在开启前无法查看
4. 建议定期检查可开启的胶囊
5. 分类系统支持智能识别，也可手动选择

## 扩展功能

未来可考虑添加：
- 胶囊提醒通知
- 胶囊分享功能
- 胶囊加密功能
- 胶囊模板功能
- 胶囊统计分析