import { EnhancedDatabaseService, DatabaseResult } from '../services/EnhancedDatabaseService';
import { RepositoryFactory } from '../repositories/index';
import { StateManager } from '../state/StateManager';
import { NoteModel, CapsuleModel, SettingsModel } from '../model/NoteModel';
import { DateUtils } from '../utils/DateUtils';

/**
 * 预加载策略配置
 */
export interface PreloadConfig {
  enabled: boolean;
  batchSize: number;
  maxConcurrentPreloads: number;
  cacheTTL: number; // 缓存生存时间（毫秒）
  preloadStrategies: {
    notes: boolean;
    capsules: boolean;
    settings: boolean;
    statistics: boolean;
    recentNotes: boolean;
    upcomingCapsules: boolean;
  };
}

/**
 * 预加载优先级
 */
export enum PreloadPriority {
  HIGH = 1,
  MEDIUM = 2,
  LOW = 3
}

/**
 * 预加载任务
 */
export interface PreloadTask {
  id: string;
  priority: PreloadPriority;
  executor: () => Promise<any>;
  cacheKey: string;
  dependencies?: string[];
  retryCount: number;
  maxRetries: number;
  timeout: number;
}

/**
 * 预加载结果
 */
export interface PreloadResult {
  taskId: string;
  success: boolean;
  data?: any;
  error?: Error;
  timestamp: string;
  executionTime: number;
}

/**
 * 数据预加载服务
 */
export class DataPreloadService {
  private static instance: DataPreloadService;
  private dbService: EnhancedDatabaseService;
  private stateManager: StateManager;
  private preloadQueue: PreloadTask[] = [];
  private runningTasks: Map<string, Promise<PreloadResult>> = new Map();
  private completedTasks: Map<string, PreloadResult> = new Map();
  private isProcessing = false;
  
  // 默认配置
  private config: PreloadConfig = {
    enabled: true,
    batchSize: 5,
    maxConcurrentPreloads: 3,
    cacheTTL: 5 * 60 * 1000, // 5分钟
    preloadStrategies: {
      notes: true,
      capsules: true,
      settings: true,
      statistics: true,
      recentNotes: true,
      upcomingCapsules: true
    }
  };
  
  private constructor() {
    this.dbService = EnhancedDatabaseService.getInstance();
    this.stateManager = StateManager.getInstance();
  }
  
  static getInstance(): DataPreloadService {
    if (!DataPreloadService.instance) {
      DataPreloadService.instance = new DataPreloadService();
    }
    return DataPreloadService.instance;
  }
  
  /**
   * 初始化预加载服务
   */
  async initialize(): Promise<void> {
    if (!this.config.enabled) {
      return;
    }
    
    // 注册状态观察者
    this.stateManager.registerObserver('preload_service', {
      onStateChanged: (event) => {
        this.handleStateChange(event);
      },
      getObservedTypes: () => ['UI_STATE_CHANGED', 'CACHE_UPDATED']
    });
    
    // 开始处理队列
    this.startProcessing();
    
    // 执行初始预加载
    await this.performInitialPreload();
  }
  
  /**
   * 执行初始预加载
   */
  private async performInitialPreload(): Promise<void> {
    console.log('Starting initial data preload...');
    
    // 高优先级任务：设置和统计数据
    if (this.config.preloadStrategies.settings) {
      this.enqueueTask({
        id: 'preload_settings',
        priority: PreloadPriority.HIGH,
        executor: () => this.preloadSettings(),
        cacheKey: 'settings',
        maxRetries: 3,
        timeout: 5000
      });
    }
    
    if (this.config.preloadStrategies.statistics) {
      this.enqueueTask({
        id: 'preload_statistics',
        priority: PreloadPriority.HIGH,
        executor: () => this.preloadStatistics(),
        cacheKey: 'statistics',
        maxRetries: 3,
        timeout: 10000
      });
    }
    
    // 中优先级任务：最近的笔记和即将到期的胶囊
    if (this.config.preloadStrategies.recentNotes) {
      this.enqueueTask({
        id: 'preload_recent_notes',
        priority: PreloadPriority.MEDIUM,
        executor: () => this.preloadRecentNotes(),
        cacheKey: 'recent_notes',
        maxRetries: 2,
        timeout: 8000
      });
    }
    
    if (this.config.preloadStrategies.upcomingCapsules) {
      this.enqueueTask({
        id: 'preload_upcoming_capsules',
        priority: PreloadPriority.MEDIUM,
        executor: () => this.preloadUpcomingCapsules(),
        cacheKey: 'upcoming_capsules',
        maxRetries: 2,
        timeout: 8000
      });
    }
    
    // 低优先级任务：所有笔记和胶囊
    if (this.config.preloadStrategies.notes) {
      this.enqueueTask({
        id: 'preload_all_notes',
        priority: PreloadPriority.LOW,
        executor: () => this.preloadAllNotes(),
        cacheKey: 'all_notes',
        maxRetries: 1,
        timeout: 15000
      });
    }
    
    if (this.config.preloadStrategies.capsules) {
      this.enqueueTask({
        id: 'preload_all_capsules',
        priority: PreloadPriority.LOW,
        executor: () => this.preloadAllCapsules(),
        cacheKey: 'all_capsules',
        maxRetries: 1,
        timeout: 10000
      });
    }
  }
  
  /**
   * 添加预加载任务
   */
  enqueueTask(task: PreloadTask): void {
    if (!this.config.enabled) {
      return;
    }
    
    // 检查任务是否已存在
    if (this.runningTasks.has(task.id) || this.completedTasks.has(task.id)) {
      return;
    }
    
    // 按优先级插入队列
    let inserted = false;
    for (let i = 0; i < this.preloadQueue.length; i++) {
      if (task.priority < this.preloadQueue[i].priority) {
        this.preloadQueue.splice(i, 0, task);
        inserted = true;
        break;
      }
    }
    
    if (!inserted) {
      this.preloadQueue.push(task);
    }
    
    console.log(`Enqueued preload task: ${task.id} (priority: ${task.priority})`);
  }
  
  /**
   * 开始处理队列
   */
  private startProcessing(): void {
    if (this.isProcessing) {
      return;
    }
    
    this.isProcessing = true;
    this.processQueue();
  }
  
  /**
   * 处理队列
   */
  private async processQueue(): Promise<void> {
    while (this.preloadQueue.length > 0 && this.runningTasks.size < this.config.maxConcurrentPreloads) {
      const task = this.preloadQueue.shift()!;
      
      // 检查依赖
      if (task.dependencies && task.dependencies.length > 0) {
        const allDependenciesCompleted = task.dependencies.every(dep => 
          this.completedTasks.has(dep) && this.completedTasks.get(dep)!.success
        );
        
        if (!allDependenciesCompleted) {
          // 依赖未完成，重新入队
          this.preloadQueue.push(task);
          continue;
        }
      }
      
      // 执行任务
      const taskPromise = this.executeTask(task);
      this.runningTasks.set(task.id, taskPromise);
      
      // 任务完成后清理
      taskPromise.finally(() => {
        this.runningTasks.delete(task.id);
        
        // 继续处理队列
        if (this.preloadQueue.length > 0) {
          this.processQueue();
        }
      });
    }
    
    // 如果队列为空且没有运行中的任务，停止处理
    if (this.preloadQueue.length === 0 && this.runningTasks.size === 0) {
      this.isProcessing = false;
      console.log('Preload queue processing completed');
    }
  }
  
  /**
   * 执行预加载任务
   */
  private async executeTask(task: PreloadTask): Promise<PreloadResult> {
    const startTime = Date.now();
    
    try {
      // 设置超时
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error(`Task ${task.id} timed out`)), task.timeout);
      });
      
      // 执行任务
      const data = await Promise.race([task.executor(), timeoutPromise]);
      
      const result: PreloadResult = {
        taskId: task.id,
        success: true,
        data,
        timestamp: new Date().toISOString(),
        executionTime: Date.now() - startTime
      };
      
      // 缓存结果
      this.completedTasks.set(task.id, result);
      
      // 更新状态
      this.updateStateWithPreloadResult(result);
      
      console.log(`Preload task completed: ${task.id} (${result.executionTime}ms)`);
      
      return result;
      
    } catch (error) {
      const result: PreloadResult = {
        taskId: task.id,
        success: false,
        error: error as Error,
        timestamp: new Date().toISOString(),
        executionTime: Date.now() - startTime
      };
      
      // 重试逻辑
      if (task.retryCount < task.maxRetries) {
        task.retryCount++;
        console.warn(`Preload task failed, retrying (${task.retryCount}/${task.maxRetries}): ${task.id}`);
        
        // 重新入队
        this.preloadQueue.push(task);
        
        // 继续处理队列
        this.processQueue();
      } else {
        console.error(`Preload task failed permanently: ${task.id}`, error);
        this.completedTasks.set(task.id, result);
      }
      
      return result;
    }
  }
  
  /**
   * 预加载设置
   */
  private async preloadSettings(): Promise<SettingsModel | null> {
    const result = await RepositoryFactory.getSettingsRepository().getSettings();
    return result;
  }
  
  /**
   * 预加载统计数据
   */
  private async preloadStatistics(): Promise<{
    totalNotes: number;
    totalCapsules: number;
    thisMonthNotes: number;
    writingStreak: number;
  }> {
    const [noteCount, capsuleCount, monthlyStats] = await Promise.all([
      RepositoryFactory.getNoteRepository().count(),
      RepositoryFactory.getCapsuleRepository().count(),
      RepositoryFactory.getNoteRepository().getDailyStats(
        DateUtils.formatDate(new Date(), 'YYYY-MM-01'),
        DateUtils.formatDate(new Date(), 'YYYY-MM-DD')
      )
    ]);
    
    const thisMonthNotes = monthlyStats.success ? 
      monthlyStats.data!.reduce((sum, stat) => sum + stat.count, 0) : 0;
    
    return {
      totalNotes: noteCount.success ? noteCount.data! : 0,
      totalCapsules: capsuleCount.success ? capsuleCount.data! : 0,
      thisMonthNotes,
      writingStreak: await this.calculateWritingStreak()
    };
  }
  
  /**
   * 预加载最近的笔记
   */
  private async preloadRecentNotes(): Promise<NoteModel[]> {
    const result = await RepositoryFactory.getNoteRepository().findAll(0, 20);
    return result.success ? result.data! : [];
  }
  
  /**
   * 预加载即将到期的胶囊
   */
  private async preloadUpcomingCapsules(): Promise<CapsuleModel[]> {
    const result = await RepositoryFactory.getCapsuleRepository().getUpcomingCapsules(7);
    return result.success ? result.data! : [];
  }
  
  /**
   * 预加载所有笔记
   */
  private async preloadAllNotes(): Promise<NoteModel[]> {
    const result = await RepositoryFactory.getNoteRepository().findAll(0, 999999);
    return result.success ? result.data! : [];
  }
  
  /**
   * 预加载所有胶囊
   */
  private async preloadAllCapsules(): Promise<CapsuleModel[]> {
    const result = await RepositoryFactory.getCapsuleRepository().findAll(0, 999999);
    return result.success ? result.data! : [];
  }
  
  /**
   * 计算连续写作天数
   */
  private async calculateWritingStreak(): Promise<number> {
    try {
      const noteDates = await RepositoryFactory.getNoteRepository().getDailyStats(
        DateUtils.formatDate(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), 'YYYY-MM-DD'),
        DateUtils.formatDate(new Date(), 'YYYY-MM-DD')
      );
      
      if (!noteDates.success) {
        return 0;
      }
      
      const dates = noteDates.data!.map(stat => stat.date).sort();
      let streak = 0;
      let currentDate = new Date();
      
      for (let i = dates.length - 1; i >= 0; i--) {
        const noteDate = new Date(dates[i]);
        const diffDays = Math.floor((currentDate.getTime() - noteDate.getTime()) / (1000 * 60 * 60 * 24));
        
        if (diffDays === 0 || diffDays === 1) {
          streak++;
          currentDate = noteDate;
        } else if (diffDays > 1) {
          break;
        }
      }
      
      return streak;
    } catch (error) {
      console.error('Error calculating writing streak:', error);
      return 0;
    }
  }
  
  /**
   * 使用预加载结果更新状态
   */
  private updateStateWithPreloadResult(result: PreloadResult): void {
    if (!result.success || !result.data) {
      return;
    }
    
    switch (result.taskId) {
      case 'preload_settings':
        this.stateManager.updateState({ userSettings: result.data }, 'USER_SETTINGS_CHANGED');
        break;
      
      case 'preload_statistics':
        this.stateManager.updateState({ stats: result.data }, 'STATS_UPDATED');
        break;
      
      case 'preload_recent_notes':
        this.stateManager.updateState({ notes: result.data }, 'NOTES_LOADED');
        break;
      
      case 'preload_upcoming_capsules':
        this.stateManager.updateState({ expiredCapsules: result.data }, 'CAPSULES_LOADED');
        break;
    }
  }
  
  /**
   * 处理状态变化
   */
  private handleStateChange(event: any): void {
    // 根据状态变化触发相应的预加载
    switch (event.type) {
      case 'UI_STATE_CHANGED':
        if (event.payload?.currentView) {
          this.handleViewChange(event.payload.currentView);
        }
        break;
      
      case 'CACHE_UPDATED':
        // 缓存更新后可以触发相关数据的预加载
        break;
    }
  }
  
  /**
   * 处理视图变化
   */
  private handleViewChange(view: string): void {
    switch (view) {
      case 'timeline':
        // 时间线视图需要预加载最近的笔记
        this.enqueueTask({
          id: 'preload_timeline_notes',
          priority: PreloadPriority.HIGH,
          executor: () => this.preloadRecentNotes(),
          cacheKey: 'timeline_notes',
          maxRetries: 2,
          timeout: 5000
        });
        break;
      
      case 'calendar':
        // 日历视图需要预加载日期统计
        this.enqueueTask({
          id: 'preload_calendar_stats',
          priority: PreloadPriority.HIGH,
          executor: () => this.preloadCalendarStats(),
          cacheKey: 'calendar_stats',
          maxRetries: 2,
          timeout: 8000
        });
        break;
      
      case 'capsule':
        // 胶囊视图需要预加载胶囊数据
        this.enqueueTask({
          id: 'preload_capsule_data',
          priority: PreloadPriority.HIGH,
          executor: () => this.preloadAllCapsules(),
          cacheKey: 'capsule_data',
          maxRetries: 2,
          timeout: 10000
        });
        break;
      
      case 'stats':
        // 统计视图需要预加载详细统计
        this.enqueueTask({
          id: 'preload_detailed_stats',
          priority: PreloadPriority.HIGH,
          executor: () => this.preloadDetailedStats(),
          cacheKey: 'detailed_stats',
          maxRetries: 2,
          timeout: 15000
        });
        break;
    }
    
    // 重新启动队列处理
    if (!this.isProcessing) {
      this.startProcessing();
    }
  }
  
  /**
   * 预加载日历统计
   */
  private async preloadCalendarStats(): Promise<{ date: string; count: number }[]> {
    const now = new Date();
    const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
    const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    
    const result = await RepositoryFactory.getNoteRepository().getDailyStats(
      DateUtils.formatDate(firstDay, 'YYYY-MM-DD'),
      DateUtils.formatDate(lastDay, 'YYYY-MM-DD')
    );
    
    return result.success ? result.data! : [];
  }
  
  /**
   * 预加载详细统计
   */
  private async preloadDetailedStats(): Promise<{
    dailyStats: { date: string; count: number }[];
    moodStats: { mood: string; count: number }[];
    monthlyStats: { month: string; count: number }[];
  }> {
    const [dailyStats, moodStats] = await Promise.all([
      RepositoryFactory.getNoteRepository().getDailyStats(
        DateUtils.formatDate(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), 'YYYY-MM-DD'),
        DateUtils.formatDate(new Date(), 'YYYY-MM-DD')
      ),
      RepositoryFactory.getNoteRepository().getMoodStats()
    ]);
    
    return {
      dailyStats: dailyStats.success ? dailyStats.data! : [],
      moodStats: moodStats.success ? moodStats.data! : [],
      monthlyStats: [] // 可以添加月度统计逻辑
    };
  }
  
  /**
   * 获取预加载任务状态
   */
  getTaskStatus(taskId: string): {
    status: 'pending' | 'running' | 'completed' | 'failed';
    result?: PreloadResult;
  } {
    if (this.runningTasks.has(taskId)) {
      return { status: 'running' };
    }
    
    if (this.completedTasks.has(taskId)) {
      const result = this.completedTasks.get(taskId)!;
      return { 
        status: result.success ? 'completed' : 'failed',
        result 
      };
    }
    
    const isInQueue = this.preloadQueue.some(task => task.id === taskId);
    return { status: isInQueue ? 'pending' : 'not_found' };
  }
  
  /**
   * 获取所有预加载任务状态
   */
  getAllTaskStatus(): Array<{
    taskId: string;
    status: 'pending' | 'running' | 'completed' | 'failed';
    result?: PreloadResult;
  }> {
    const statuses: Array<{
      taskId: string;
      status: 'pending' | 'running' | 'completed' | 'failed';
      result?: PreloadResult;
    }> = [];
    
    // 队列中的任务
    this.preloadQueue.forEach(task => {
      statuses.push({
        taskId: task.id,
        status: 'pending'
      });
    });
    
    // 运行中的任务
    this.runningTasks.forEach((_, taskId) => {
      statuses.push({
        taskId,
        status: 'running'
      });
    });
    
    // 已完成的任务
    this.completedTasks.forEach((result, taskId) => {
      statuses.push({
        taskId,
        status: result.success ? 'completed' : 'failed',
        result
      });
    });
    
    return statuses;
  }
  
  /**
   * 清理过期的预加载结果
   */
  cleanupExpiredResults(): void {
    const now = Date.now();
    
    for (const [taskId, result] of this.completedTasks) {
      if (now - new Date(result.timestamp).getTime() > this.config.cacheTTL) {
        this.completedTasks.delete(taskId);
      }
    }
  }
  
  /**
   * 更新配置
   */
  updateConfig(config: Partial<PreloadConfig>): void {
    this.config = { ...this.config, ...config };
  }
  
  /**
   * 重置服务
   */
  reset(): void {
    this.preloadQueue = [];
    this.runningTasks.clear();
    this.completedTasks.clear();
    this.isProcessing = false;
  }
  
  /**
   * 销毁服务
   */
  destroy(): void {
    this.reset();
    this.stateManager.unregisterObserver('preload_service');
  }
}