# 统计页面使用说明

## 文件说明

### 核心文件
- `StatsPage.ets` - 统计页面主要组件
- `StatsService.ets` - 统计数据服务
- `StatsPageEntry.ets` - 统计页面独立入口
- `TestDataGenerator.ets` - 测试数据生成器

### 功能特性

#### 1. 数据概览
- **总笔记数**: 显示用户创建的总笔记数量
- **连续记录**: 显示连续记录的天数
- **分类数**: 显示使用的分类数量
- **完成率**: 显示本月记录完成率

#### 2. 图表展示
- **本月记录趋势图**: 显示最近7天的笔记记录趋势
- **分类占比图**: 显示各分类笔记的占比情况

#### 3. 详细统计
- **分类统计**: 显示每个分类的笔记数量和占比
- **进度条**: 可视化显示各分类的相对比例

#### 4. 交互功能
- **刷新按钮**: 点击刷新统计数据
- **加载状态**: 数据加载时显示加载动画
- **实时更新**: 显示最后更新时间

## 使用方法

### 1. 在主应用中使用
统计页面已集成到主应用的底部导航栏中，用户可以直接点击"统计"标签访问。

### 2. 独立访问
可以通过 `StatsPageEntry.ets` 独立访问统计页面，用于测试和演示。

### 3. 生成测试数据
使用测试数据生成器创建演示数据：

```typescript
import { TestDataGenerator } from '../utils/TestDataGenerator';

// 生成基础示例数据
await TestDataGenerator.generateSampleNotes();

// 生成大量测试数据用于统计演示
await TestDataGenerator.generateStatsTestData();
```

## 技术实现

### 数据架构
- **数据层**: 使用 RDB 数据库存储笔记数据
- **服务层**: StatsService 负责数据统计和计算
- **展示层**: StatsPageComponent 负责UI展示

### 统计算法
- **连续记录**: 通过分析有记录的日期计算连续天数
- **完成率**: 基于本月天数和记录天数计算
- **分类统计**: 按分类ID分组统计笔记数量

### UI设计
- **莫兰迪色系**: 使用柔和的莫兰迪色调
- **响应式设计**: 适配不同屏幕尺寸
- **动画效果**: 包含加载动画和交互动画

## 注意事项

1. **数据库初始化**: 使用前需要确保数据库已正确初始化
2. **性能优化**: 大量数据统计时注意性能问题
3. **错误处理**: 统计功能包含完善的错误处理机制
4. **数据同步**: 支持实时数据更新和刷新

## 扩展功能

可以进一步扩展的功能：
- 时间范围筛选
- 数据导出功能
- 更详细的图表分析
- 数据对比功能
- 统计报告生成