import { NoteModel } from '../model/NoteModel';
import { MorandiColors } from '../constants/MorandiColors';
import { AppConstants } from '../constants/AppConstants';
import { DateUtils } from '../utils/DateUtils';
import { DatabaseService } from '../services/DatabaseService';
import router from '@ohos.router';

// 搜索筛选类型
enum SearchFilter {
  ALL = 'all',
  LIFE = 'life',
  WORK = 'work',
  STUDY = 'study',
  PLAN = 'plan'
}

// 搜索历史记录项
interface SearchHistoryItem {
  keyword: string;
  timestamp: string;
}

// 搜索结果项
interface SearchResultItem {
  note: NoteModel;
  relevanceScore: number;
  highlightedTitle: { text: string; isHighlighted: boolean }[];
  highlightedContent: { text: string; isHighlighted: boolean }[];
}

@Component
export struct SearchPageComponent {
  @State searchKeyword: string = '';
  @State searchResults: SearchResultItem[] = [];
  @State searchHistory: SearchHistoryItem[] = [];
  @State selectedFilter: SearchFilter = SearchFilter.ALL;
  @State isLoading: boolean = false;
  @State showHistory: boolean = true;
  @State searchTimer: number | null = null;
  
  private database: DatabaseService = DatabaseService.getInstance();
  
  aboutToAppear() {
    this.loadSearchHistory();
  }
  
  aboutToDisappear() {
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
    }
  }
  
  /**
   * 加载搜索历史
   */
  private async loadSearchHistory() {
    try {
      // 从本地存储加载搜索历史
      const historyStr = localStorage.getItem('searchHistory');
      if (historyStr) {
        this.searchHistory = JSON.parse(historyStr);
      }
    } catch (error) {
      console.error('Failed to load search history:', error);
    }
  }
  
  /**
   * 保存搜索历史
   */
  private saveSearchHistory() {
    try {
      localStorage.setItem('searchHistory', JSON.stringify(this.searchHistory));
    } catch (error) {
      console.error('Failed to save search history:', error);
    }
  }
  
  /**
   * 添加搜索历史
   */
  private addSearchHistory(keyword: string) {
    if (!keyword.trim()) return;
    
    // 移除重复项
    this.searchHistory = this.searchHistory.filter(item => item.keyword !== keyword);
    
    // 添加到开头
    this.searchHistory.unshift({
      keyword: keyword.trim(),
      timestamp: DateUtils.now()
    });
    
    // 限制历史记录数量
    if (this.searchHistory.length > 10) {
      this.searchHistory = this.searchHistory.slice(0, 10);
    }
    
    this.saveSearchHistory();
  }
  
  /**
   * 清空搜索历史
   */
  private clearSearchHistory() {
    this.searchHistory = [];
    this.saveSearchHistory();
  }
  
  /**
   * 执行搜索
   */
  private async performSearch() {
    if (!this.searchKeyword.trim()) {
      this.searchResults = [];
      this.showHistory = true;
      return;
    }
    
    this.isLoading = true;
    this.showHistory = false;
    
    try {
      // 添加到搜索历史
      this.addSearchHistory(this.searchKeyword);
      
      // 从数据库搜索
      let notes = await this.database.searchNotes(this.searchKeyword);
      
      // 根据筛选条件过滤
      if (this.selectedFilter !== SearchFilter.ALL) {
        notes = notes.filter(note => this.filterByCategory(note, this.selectedFilter));
      }
      
      // 计算相关性分数并高亮关键词
      this.searchResults = notes.map(note => {
        const relevanceScore = this.calculateRelevanceScore(note, this.searchKeyword);
        const highlightedTitle = this.highlightKeyword(note.title, this.searchKeyword);
        const highlightedContent = this.highlightKeyword(note.content, this.searchKeyword);
        
        return {
          note,
          relevanceScore,
          highlightedTitle,
          highlightedContent
        };
      }).sort((a, b) => b.relevanceScore - a.relevanceScore);
      
    } catch (error) {
      console.error('Search failed:', error);
    } finally {
      this.isLoading = false;
    }
  }
  
  /**
   * 根据分类筛选
   */
  private filterByCategory(note: NoteModel, filter: SearchFilter): boolean {
    switch (filter) {
      case SearchFilter.LIFE:
        return note.categoryId === 1;
      case SearchFilter.WORK:
        return note.categoryId === 2;
      case SearchFilter.STUDY:
        return note.categoryId === 3;
      case SearchFilter.PLAN:
        return note.categoryId === 4;
      default:
        return true;
    }
  }
  
  /**
   * 计算相关性分数
   */
  private calculateRelevanceScore(note: NoteModel, keyword: string): number {
    const keywordLower = keyword.toLowerCase();
    const titleLower = note.title.toLowerCase();
    const contentLower = note.content.toLowerCase();
    
    let score = 0;
    
    // 标题匹配权重更高
    if (titleLower.includes(keywordLower)) {
      score += 10;
    }
    
    // 内容匹配
    if (contentLower.includes(keywordLower)) {
      score += 5;
    }
    
    // 标签匹配
    if (note.tags && note.tags.toLowerCase().includes(keywordLower)) {
      score += 3;
    }
    
    // 计算匹配次数
    const titleMatches = (titleLower.match(new RegExp(keywordLower, 'g')) || []).length;
    const contentMatches = (contentLower.match(new RegExp(keywordLower, 'g')) || []).length;
    
    score += titleMatches * 2 + contentMatches;
    
    return score;
  }
  
  /**
   * 高亮关键词 - 返回文本片段数组
   */
  private highlightKeyword(text: string, keyword: string): { text: string; isHighlighted: boolean }[] {
    if (!keyword.trim()) {
      return [{ text, isHighlighted: false }];
    }
    
    const regex = new RegExp(`(${keyword})`, 'gi');
    const parts: { text: string; isHighlighted: boolean }[] = [];
    let lastIndex = 0;
    let match;
    
    while ((match = regex.exec(text)) !== null) {
      // 添加匹配前的文本
      if (match.index > lastIndex) {
        parts.push({
          text: text.substring(lastIndex, match.index),
          isHighlighted: false
        });
      }
      
      // 添加匹配的文本
      parts.push({
        text: match[0],
        isHighlighted: true
      });
      
      lastIndex = match.index + match[0].length;
    }
    
    // 添加剩余的文本
    if (lastIndex < text.length) {
      parts.push({
        text: text.substring(lastIndex),
        isHighlighted: false
      });
    }
    
    return parts;
  }
  
  /**
   * 处理搜索输入变化
   */
  private onSearchInput(value: string) {
    this.searchKeyword = value;
    
    // 防抖处理
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
    }
    
    this.searchTimer = setTimeout(() => {
      this.performSearch();
    }, 300);
  }
  
  /**
   * 处理筛选条件变化
   */
  private onFilterChange(filter: SearchFilter) {
    this.selectedFilter = filter;
    this.performSearch();
  }
  
  /**
   * 跳转到笔记详情
   */
  private navigateToNoteDetail(note: NoteModel) {
    router.pushUrl({
      url: 'pages/NoteDetailPage',
      params: {
        noteId: note.id
      }
    });
  }
  
  build() {
    Column() {
      // 顶部状态栏占位
      Row()
        .width('100%')
        .height(44)
        .backgroundColor(MorandiColors.background);
      
      // 搜索页面内容
      Column() {
        // 搜索框
        this.SearchBar();
        
        // 筛选标签
        this.FilterTags();
        
        // 搜索内容区域
        if (this.showHistory) {
          this.SearchHistory();
        } else {
          this.SearchResults();
        }
      }
      .layoutWeight(1);
    }
    .width('100%')
    .height('100%')
    .backgroundColor(MorandiColors.background);
  }
  
  /**
   * 搜索框组件
   */
  @Builder SearchBar() {
    Column() {
      Row() {
        // 搜索图标
        Text('🔍')
          .fontSize(20)
          .fontColor(MorandiColors.textHint)
          .margin({ right: 12 });
        
        // 搜索输入框
        TextInput({ placeholder: '搜索笔记内容、标题或标签...' })
          .fontSize(16)
          .fontColor(MorandiColors.textPrimary)
          .placeholderColor(MorandiColors.textHint)
          .backgroundColor(MorandiColors.surface)
          .borderRadius(20)
          .padding({ left: 16, right: 16, top: 12, bottom: 12 })
          .onChange((value: string) => this.onSearchInput(value))
          .layoutWeight(1);
        
        // 清空按钮
        if (this.searchKeyword) {
          Text('✕')
            .fontSize(18)
            .fontColor(MorandiColors.textHint)
            .onClick(() => {
              this.searchKeyword = '';
              this.searchResults = [];
              this.showHistory = true;
            });
        }
      }
      .width('100%')
      .padding({ left: 24, right: 24, top: 16, bottom: 8 })
      .backgroundColor(MorandiColors.cardBackground)
      .borderRadius(25)
      .border({ width: 1, color: MorandiColors.border });
    }
    .width('100%')
    .backgroundColor(MorandiColors.background);
  }
  
  /**
   * 筛选标签组件
   */
  @Builder FilterTags() {
    Row() {
      ForEach([
        { type: SearchFilter.ALL, label: '全部' },
        { type: SearchFilter.LIFE, label: '生活感悟' },
        { type: SearchFilter.WORK, label: '工作' },
        { type: SearchFilter.STUDY, label: '学习' },
        { type: SearchFilter.PLAN, label: '计划' }
      ], (item: { type: SearchFilter, label: string }) => {
        Text(item.label)
          .fontSize(14)
          .fontColor(this.selectedFilter === item.type ? MorandiColors.background : MorandiColors.textSecondary)
          .backgroundColor(this.selectedFilter === item.type ? MorandiColors.accent : MorandiColors.surface)
          .padding({ left: 16, right: 16, top: 8, bottom: 8 })
          .borderRadius(20)
          .border({ width: 1, color: this.selectedFilter === item.type ? MorandiColors.accent : MorandiColors.border })
          .margin({ right: 8 })
          .onClick(() => this.onFilterChange(item.type));
      });
    }
    .width('100%')
    .padding({ left: 24, right: 24, top: 8, bottom: 16 })
    .justifyContent(FlexAlign.Start);
  }
  
  /**
   * 搜索历史组件
   */
  @Builder SearchHistory() {
    Column() {
      // 搜索历史标题
      Row() {
        Text('搜索历史')
          .fontSize(16)
          .fontWeight(500)
          .fontColor(MorandiColors.textPrimary)
          .layoutWeight(1);
        
        if (this.searchHistory.length > 0) {
          Text('清空')
            .fontSize(14)
            .fontColor(MorandiColors.textHint)
            .onClick(() => this.clearSearchHistory());
        }
      }
      .width('100%')
      .padding({ left: 24, right: 24, top: 16, bottom: 12 });
      
      // 搜索历史列表
      if (this.searchHistory.length > 0) {
        Flex({ wrap: FlexWrap.Wrap }) {
          ForEach(this.searchHistory, (item: SearchHistoryItem) => {
            Text(item.keyword)
              .fontSize(14)
              .fontColor(MorandiColors.textSecondary)
              .backgroundColor(MorandiColors.surface)
              .padding({ left: 16, right: 16, top: 8, bottom: 8 })
              .borderRadius(16)
              .border({ width: 1, color: MorandiColors.border })
              .margin({ bottom: 8, right: 8 })
              .onClick(() => {
                this.searchKeyword = item.keyword;
                this.performSearch();
              });
          });
        }
        .width('100%')
        .padding({ left: 24, right: 24 });
      } else {
        // 空状态
        Column() {
          Text('暂无搜索历史')
            .fontSize(14)
            .fontColor(MorandiColors.textHint)
            .margin({ top: 40 });
        }
        .width('100%')
        .height(100)
        .justifyContent(FlexAlign.Center);
      }
    }
    .width('100%')
    .layoutWeight(1);
  }
  
  /**
   * 搜索结果组件
   */
  @Builder SearchResults() {
    Column() {
      // 加载状态
      if (this.isLoading) {
        Column() {
          LoadingProgress()
            .width(40)
            .height(40)
            .color(MorandiColors.accent);
          
          Text('搜索中...')
            .fontSize(14)
            .fontColor(MorandiColors.textHint)
            .margin({ top: 16 });
        }
        .width('100%')
        .height(200)
        .justifyContent(FlexAlign.Center);
      } else {
        // 搜索结果统计
        if (this.searchResults.length > 0) {
          Row() {
            Text(`找到 ${this.searchResults.length} 条相关笔记`)
              .fontSize(14)
              .fontColor(MorandiColors.textHint);
          }
          .width('100%')
          .padding({ left: 24, right: 24, top: 8, bottom: 12 });
        }
        
        // 搜索结果列表
        if (this.searchResults.length > 0) {
          List() {
            ForEach(this.searchResults, (result: SearchResultItem) => {
              ListItem() {
                this.SearchResultCard(result);
              }
            });
          }
          .width('100%')
          .layoutWeight(1)
          .listDirection(Axis.Vertical)
          .edgeEffect(EdgeEffect.Spring);
        } else {
          // 无搜索结果
          this.EmptyState();
        }
      }
    }
    .width('100%')
    .layoutWeight(1);
  }
  
  /**
   * 搜索结果卡片组件
   */
  @Builder SearchResultCard(result: SearchResultItem) {
    Column() {
      // 笔记时间
      Row() {
        Text(DateUtils.getFriendlyTime(result.note.createTime))
          .fontSize(12)
          .fontColor(MorandiColors.textTertiary);
        
        if (result.relevanceScore > 10) {
          Text('高匹配')
            .fontSize(10)
            .fontColor(MorandiColors.background)
            .backgroundColor(MorandiColors.accent)
            .padding({ left: 8, right: 8, top: 2, bottom: 2 })
            .borderRadius(10)
            .margin({ left: 8 });
        }
      }
      .width('100%')
      .justifyContent(FlexAlign.SpaceBetween);
      
      // 笔记标题（高亮关键词）
      Flex({ wrap: FlexWrap.Wrap }) {
        ForEach(result.highlightedTitle, (part: { text: string; isHighlighted: boolean }) => {
          Text(part.text)
            .fontSize(16)
            .fontWeight(part.isHighlighted ? 600 : 500)
            .fontColor(part.isHighlighted ? MorandiColors.accent : MorandiColors.textPrimary);
        });
      }
      .width('100%')
      .margin({ top: 8, bottom: 8 })
      .maxLines(2);
      
      // 笔记内容（高亮关键词）
      Flex({ wrap: FlexWrap.Wrap }) {
        ForEach(result.highlightedContent, (part: { text: string; isHighlighted: boolean }) => {
          Text(part.text)
            .fontSize(14)
            .fontColor(part.isHighlighted ? MorandiColors.accent : MorandiColors.textSecondary);
        });
      }
      .width('100%')
      .lineHeight(20)
      .maxLines(3);
      
      // 标签
      if (result.note.tags) {
        Flex({ wrap: FlexWrap.Wrap }) {
          ForEach(result.note.tags.split(','), (tag: string) => {
            if (tag.trim()) {
              Text(tag.trim())
                .fontSize(11)
                .fontColor(MorandiColors.textHint)
                .backgroundColor(MorandiColors.border)
                .padding({ left: 12, right: 12, top: 4, bottom: 4 })
                .borderRadius(16)
                .margin({ top: 12, right: 8 });
            }
          });
        }
      }
      
      // 相关性分数
      Row() {
        Text(`相关性: ${result.relevanceScore}`)
          .fontSize(10)
          .fontColor(MorandiColors.textHint);
      }
      .width('100%')
      .margin({ top: 8 });
    }
    .width('100%')
    .padding(16)
    .backgroundColor(MorandiColors.cardBackground)
    .borderRadius(16)
    .border({ width: 1, color: MorandiColors.border })
    .margin({ left: 24, right: 24, bottom: 16 })
    .onClick(() => this.navigateToNoteDetail(result.note));
  }
  
  /**
   * 空状态组件
   */
  @Builder EmptyState() {
    Column() {
      // 空状态图标
      Text('🔍')
        .fontSize(48)
        .margin({ bottom: 16 });
      
      // 空状态文本
      Text('未找到相关笔记')
        .fontSize(16)
        .fontWeight(500)
        .fontColor(MorandiColors.textPrimary)
        .margin({ bottom: 8 });
      
      Text('尝试使用不同的关键词或调整筛选条件')
        .fontSize(14)
        .fontColor(MorandiColors.textHint)
        .textAlign(TextAlign.Center)
        .margin({ bottom: 24 });
      
      // 建议
      Column() {
        Text('搜索建议：')
          .fontSize(14)
          .fontWeight(500)
          .fontColor(MorandiColors.textSecondary)
          .alignSelf(ItemAlign.Start)
          .margin({ bottom: 8 });
        
        Text('• 使用更简洁的关键词')
          .fontSize(12)
          .fontColor(MorandiColors.textHint)
          .alignSelf(ItemAlign.Start);
        
        Text('• 尝试同义词或相关词汇')
          .fontSize(12)
          .fontColor(MorandiColors.textHint)
          .alignSelf(ItemAlign.Start);
        
        Text('• 检查拼写是否正确')
          .fontSize(12)
          .fontColor(MorandiColors.textHint)
          .alignSelf(ItemAlign.Start);
      }
      .alignItems(HorizontalAlign.Start)
      .padding(16)
      .backgroundColor(MorandiColors.surface)
      .borderRadius(12)
      .border({ width: 1, color: MorandiColors.border });
    }
    .width('100%')
    .height(400)
    .justifyContent(FlexAlign.Center)
    .padding({ left: 24, right: 24 });
  }
}

// 独立页面入口

@Component
struct SearchPage {
  build() {
    SearchPageComponent();
  }
}