import { NoteModel } from '../model/NoteModel';
import { MorandiColors } from '../constants/MorandiColors';
import { DateUtils } from '../utils/DateUtils';
import { DatabaseService } from '../services/DatabaseService';
import router from '@ohos.router';

// 心情类型定义
interface MoodType {
  id: string;
  name: string;
  icon: string;
  color: string;
}

// 天气类型定义
interface WeatherType {
  id: string;
  name: string;
  icon: string;
}

// 格式化工具类型
interface FormatTool {
  id: string;
  name: string;
  icon: string;
}


@Component
struct NoteEditPage {
  @State note: NoteModel | null = null;
  @State isLoading: boolean = true;
  @State isSaving: boolean = false;
  @State autoSaveTimer: number | null = null;
  
  // 编辑状态
  @State title: string = '';
  @State content: string = '';
  @State tags: string = '';
  @State mood: string = '';
  @State weather: string = '';
  @State location: string = '';
  
  // UI状态
  @State showMoodPicker: boolean = false;
  @State showWeatherPicker: boolean = false;
  @State showCategoryPicker: boolean = false;
  @State lastSavedTime: string = '';
  @State wordCount: number = 0;
  @State isDirty: boolean = false;
  
  // 心情选项
  private moods: MoodType[] = [
    { id: 'happy', name: '开心', icon: '😊', color: MorandiColors.success },
    { id: 'calm', name: '平静', icon: '😌', color: MorandiColors.info },
    { id: 'sad', name: '悲伤', icon: '😢', color: MorandiColors.textTertiary },
    { id: 'angry', name: '愤怒', icon: '😠', color: MorandiColors.error },
    { id: 'excited', name: '兴奋', icon: '🤩', color: MorandiColors.accent },
    { id: 'tired', name: '疲惫', icon: '😴', color: MorandiColors.textHint }
  ];
  
  // 天气选项
  private weathers: WeatherType[] = [
    { id: 'sunny', name: '晴天', icon: '☀️' },
    { id: 'cloudy', name: '多云', icon: '☁️' },
    { id: 'rainy', name: '雨天', icon: '🌧️' },
    { id: 'snowy', name: '雪天', icon: '❄️' },
    { id: 'windy', name: '大风', icon: '🌪️' }
  ];
  
  // 格式化工具
  private formatTools: FormatTool[] = [
    { id: 'bold', name: '加粗', icon: 'B' },
    { id: 'italic', name: '斜体', icon: 'I' },
    { id: 'list', name: '列表', icon: '☰' }
  ];
  
  // 分类选项
  private categories = [
    { id: 1, name: '生活', color: MorandiColors.categoryLife },
    { id: 2, name: '工作', color: MorandiColors.categoryWork },
    { id: 3, name: '学习', color: MorandiColors.categoryStudy },
    { id: 4, name: '计划', color: MorandiColors.categoryPlan }
  ];
  
  private database: DatabaseService = DatabaseService.getInstance();
  
  aboutToAppear() {
    const params = router.getParams() as { noteId?: number };
    
    if (params && params.noteId) {
      this.loadNote(params.noteId);
    } else {
      // 创建新笔记
      this.note = {
        title: '',
        content: '',
        categoryId: 1,
        tags: '',
        mood: '',
        weather: '',
        location: '',
        images: '',
        voiceNote: '',
        createTime: DateUtils.now(),
        updateTime: DateUtils.now(),
        isDeleted: 0
      };
      this.title = '';
      this.content = '';
      this.tags = '';
      this.mood = '';
      this.weather = '';
      this.location = '';
      this.isLoading = false;
    }
    
    this.startAutoSave();
  }
  
  aboutToDisappear() {
    this.stopAutoSave();
  }
  
  /**
   * 加载笔记数据
   */
  private async loadNote(noteId: number) {
    try {
      const notes = await this.database.getNotes();
      const foundNote = notes.find(n => n.id === noteId);
      
      if (foundNote) {
        this.note = foundNote;
        this.title = foundNote.title;
        this.content = foundNote.content;
        this.tags = foundNote.tags || '';
        this.mood = foundNote.mood || '';
        this.weather = foundNote.weather || '';
        this.location = foundNote.location || '';
        this.updateWordCount();
      }
    } catch (error) {
      console.error('Failed to load note:', error);
    } finally {
      this.isLoading = false;
    }
  }
  
  /**
   * 保存笔记
   */
  private async saveNote() {
    if (!this.note) return;
    
    try {
      this.isSaving = true;
      
      const updatedNote: NoteModel = {
        ...this.note,
        title: this.title || '无标题',
        content: this.content,
        tags: this.tags,
        mood: this.mood,
        weather: this.weather,
        location: this.location,
        updateTime: DateUtils.now()
      };
      
      await this.database.saveNote(updatedNote);
      this.note = updatedNote;
      this.isDirty = false;
      this.lastSavedTime = DateUtils.formatTime(new Date());
    } catch (error) {
      console.error('Failed to save note:', error);
    } finally {
      this.isSaving = false;
    }
  }
  
  /**
   * 自动保存
   */
  private async autoSave() {
    if (this.isDirty) {
      await this.saveNote();
    }
  }
  
  /**
   * 开始自动保存
   */
  private startAutoSave() {
    this.autoSaveTimer = setInterval(() => {
      this.autoSave();
    }, 30000); // 每30秒自动保存
  }
  
  /**
   * 停止自动保存
   */
  private stopAutoSave() {
    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer);
      this.autoSaveTimer = null;
    }
  }
  
  /**
   * 更新字数统计
   */
  private updateWordCount() {
    this.wordCount = this.content.length;
  }
  
  /**
   * 选择心情
   */
  private selectMood(moodId: string) {
    this.mood = moodId;
    this.showMoodPicker = false;
    this.isDirty = true;
  }
  
  /**
   * 选择天气
   */
  private selectWeather(weatherId: string) {
    this.weather = weatherId;
    this.showWeatherPicker = false;
    this.isDirty = true;
  }
  
  /**
   * 获取当前心情图标
   */
  private getCurrentMoodIcon(): string {
    const mood = this.moods.find(m => m.id === this.mood);
    return mood ? mood.icon : '😐';
  }
  
  /**
   * 获取当前天气图标
   */
  private getCurrentWeatherIcon(): string {
    const weather = this.weathers.find(w => w.id === this.weather);
    return weather ? weather.icon : '🌤️';
  }
  
  /**
   * 获取当前分类名称
   */
  private getCurrentCategoryName(): string {
    const category = this.categories.find(c => c.id === this.note?.categoryId);
    return category ? category.name : '生活';
  }
  
  /**
   * 返回上一页
   */
  private goBack() {
    if (this.isDirty) {
      // 如果有未保存的更改，先保存
      this.saveNote().then(() => {
        router.back();
      });
    } else {
      router.back();
    }
  }
  
  build() {
    Column() {
      // 顶部状态栏占位
      Row()
        .width('100%')
        .height(44)
        .backgroundColor(MorandiColors.background);
      
      if (this.isLoading) {
        this.LoadingView();
      } else {
        this.MainContent();
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor(MorandiColors.background);
  }
  
  /**
   * 加载视图
   */
  @Builder LoadingView() {
    Column() {
      LoadingProgress()
        .width(50)
        .height(50)
        .color(MorandiColors.accent);
      
      Text('加载中...')
        .fontSize(14)
        .fontColor(MorandiColors.textHint)
        .margin({ top: 16 });
    }
    .width('100%')
    .height('100%')
    .justifyContent(FlexAlign.Center);
  }
  
  /**
   * 主要内容
   */
  @Builder MainContent() {
    Column() {
      // 顶部栏
      this.TopBar();
      
      // 工具栏
      this.Toolbar();
      
      // 编辑区
      this.EditArea();
      
      // 底部栏
      this.BottomBar();
    }
    .width('100%')
    .height('100%');
  }
  
  /**
   * 顶部栏
   */
  @Builder TopBar() {
    Row() {
      // 返回按钮
      Text('←')
        .fontSize(24)
        .fontColor(MorandiColors.textPrimary)
        .onClick(() => this.goBack());
      
      // 标题输入框
      TextInput({ placeholder: '输入标题...' })
        .fontSize(18)
        .fontWeight(500)
        .fontColor(MorandiColors.textPrimary)
        .backgroundColor(MorandiColors.surface)
        .borderRadius(8)
        .padding(12)
        .layoutWeight(1)
        .margin({ horizontal: 12 })
        .onChange((value: string) => {
          this.title = value;
          this.isDirty = true;
        });
      
      // 保存按钮
      Text('保存')
        .fontSize(14)
        .fontColor(this.isSaving ? MorandiColors.textHint : MorandiColors.accent)
        .onClick(() => this.saveNote());
    }
    .width('100%')
    .padding({ left: 16, right: 16, top: 8, bottom: 8 });
  }
  
  /**
   * 工具栏
   */
  @Builder Toolbar() {
    Column() {
      // 第一行工具栏
      Flex({ wrap: FlexWrap.Wrap, justifyContent: FlexAlign.SpaceBetween }) {
        // 心情选择
        this.MoodSelector();
        
        // 天气选择
        this.WeatherSelector();
        
        // 分类选择
        this.CategorySelector();
        
        // 语音输入
        Text('🎤')
          .fontSize(20)
          .fontColor(MorandiColors.textPrimary)
          .onClick(() => {
            // TODO: 实现语音输入功能
          });
        
        // 位置标记
        Text('📍')
          .fontSize(20)
          .fontColor(MorandiColors.textPrimary)
          .onClick(() => {
            // TODO: 实现位置标记功能
          });
      }
      .width('100%')
      .padding({ horizontal: 16, vertical: 8 });
      
      // 第二行工具栏
      Flex({ wrap: FlexWrap.Wrap, justifyContent: FlexAlign.SpaceBetween }) {
        // 格式化工具
        ForEach(this.formatTools, (tool: FormatTool) => {
          Text(tool.icon)
            .fontSize(16)
            .fontWeight(FontWeight.Bold)
            .fontColor(MorandiColors.textPrimary)
            .backgroundColor(MorandiColors.surface)
            .padding(8)
            .borderRadius(4)
            .margin({ right: 8 })
            .onClick(() => {
              // TODO: 实现格式化功能
            });
        });
        
        // 插入图片
        Text('🖼️')
          .fontSize(20)
          .fontColor(MorandiColors.textPrimary)
          .onClick(() => {
            // TODO: 实现插入图片功能
          });
        
        // 插入视频
        Text('🎥')
          .fontSize(20)
          .fontColor(MorandiColors.textPrimary)
          .onClick(() => {
            // TODO: 实现插入视频功能
          });
      }
      .width('100%')
      .padding({ horizontal: 16, vertical: 8 });
    }
    .width('100%')
    .backgroundColor(MorandiColors.cardBackground);
  }
  
  /**
   * 心情选择器
   */
  @Builder MoodSelector() {
    Column() {
      Row() {
        Text(this.getCurrentMoodIcon())
          .fontSize(20);
        
        Text('心情')
          .fontSize(12)
          .fontColor(MorandiColors.textSecondary)
          .margin({ left: 4 });
      }
      .padding(8)
      .backgroundColor(MorandiColors.surface)
      .borderRadius(8)
      .onClick(() => {
        this.showMoodPicker = !this.showMoodPicker;
      });
      
      if (this.showMoodPicker) {
        Column() {
          Flex({ wrap: FlexWrap.Wrap, justifyContent: FlexAlign.Start }) {
            ForEach(this.moods, (mood: MoodType) => {
              Column() {
                Text(mood.icon)
                  .fontSize(20);
                
                Text(mood.name)
                  .fontSize(10)
                  .fontColor(MorandiColors.textSecondary);
              }
              .padding(8)
              .backgroundColor(this.mood === mood.id ? MorandiColors.accent : MorandiColors.surface)
              .borderRadius(8)
              .margin({ right: 4, bottom: 4 })
              .onClick(() => this.selectMood(mood.id));
            });
          }
        }
        .position({ x: 0, y: 40 })
        .backgroundColor(MorandiColors.cardBackground)
        .borderRadius(8)
        .padding(8)
        .zIndex(1)
        .width('100%')
        .maxWidth(200);
      }
    }
  }
  
  /**
   * 天气选择器
   */
  @Builder WeatherSelector() {
    Column() {
      Row() {
        Text(this.getCurrentWeatherIcon())
          .fontSize(20);
        
        Text('天气')
          .fontSize(12)
          .fontColor(MorandiColors.textSecondary)
          .margin({ left: 4 });
      }
      .padding(8)
      .backgroundColor(MorandiColors.surface)
      .borderRadius(8)
      .onClick(() => {
        this.showWeatherPicker = !this.showWeatherPicker;
      });
      
      if (this.showWeatherPicker) {
        Column() {
          Flex({ wrap: FlexWrap.Wrap, justifyContent: FlexAlign.Start }) {
            ForEach(this.weathers, (weather: WeatherType) => {
              Column() {
                Text(weather.icon)
                  .fontSize(20);
                
                Text(weather.name)
                  .fontSize(10)
                  .fontColor(MorandiColors.textSecondary);
              }
              .padding(8)
              .backgroundColor(this.weather === weather.id ? MorandiColors.accent : MorandiColors.surface)
              .borderRadius(8)
              .margin({ right: 4, bottom: 4 })
              .onClick(() => this.selectWeather(weather.id));
            });
          }
        }
        .position({ x: 0, y: 40 })
        .backgroundColor(MorandiColors.cardBackground)
        .borderRadius(8)
        .padding(8)
        .zIndex(1)
        .width('100%')
        .maxWidth(200);
      }
    }
  }
  
  /**
   * 分类选择器
   */
  @Builder CategorySelector() {
    Row() {
      Text('📁')
        .fontSize(16);
      
      Text(this.getCurrentCategoryName())
        .fontSize(12)
        .fontColor(MorandiColors.textSecondary)
        .margin({ left: 4 });
    }
    .padding(8)
    .backgroundColor(MorandiColors.surface)
    .borderRadius(8)
    .onClick(() => {
      this.showCategoryPicker = !this.showCategoryPicker;
    });
  }
  
  /**
   * 编辑区
   */
  @Builder EditArea() {
    Column() {
      // 主要内容编辑区
      TextArea({ placeholder: '记录你的想法...' })
        .fontSize(16)
        .fontColor(MorandiColors.textSecondary)
        .backgroundColor(MorandiColors.surface)
        .borderRadius(8)
        .padding(16)
        .layoutWeight(1)
        .onChange((value: string) => {
          this.content = value;
          this.updateWordCount();
          this.isDirty = true;
        });
      
      // 标签和字数统计
      Row() {
        // 标签输入
        TextInput({ placeholder: '添加标签...' })
          .fontSize(12)
          .fontColor(MorandiColors.textSecondary)
          .backgroundColor(MorandiColors.surface)
          .borderRadius(16)
          .padding(8)
          .layoutWeight(1)
          .onChange((value: string) => {
            this.tags = value;
            this.isDirty = true;
          });
        
        // 字数统计
        Text(`${this.wordCount} 字`)
          .fontSize(12)
          .fontColor(MorandiColors.textTertiary);
      }
      .width('100%')
      .margin({ top: 8 });
      
      // 自动保存提示
      if (this.lastSavedTime) {
        Text(`自动保存于 ${this.lastSavedTime}`)
          .fontSize(10)
          .fontColor(MorandiColors.textHint)
          .alignSelf(ItemAlign.End)
          .margin({ top: 4 });
      }
    }
    .width('100%')
    .layoutWeight(1)
    .padding({ left: 16, right: 16, top: 16, bottom: 16 });
  }
  
  /**
   * 底部栏
   */
  @Builder BottomBar() {
    Flex({ justifyContent: FlexAlign.SpaceBetween, alignItems: ItemAlign.Center }) {
      // 创建时间
      if (this.note) {
        Text(DateUtils.formatDateTime(this.note.createTime))
          .fontSize(10)
          .fontColor(MorandiColors.textTertiary)
          .maxWidth('40%')
          .textOverflow({ overflow: TextOverflow.Ellipsis });
      }
      
      // 右侧图标组
      Row() {
        // 心情图标
        Text(this.getCurrentMoodIcon())
          .fontSize(14);
        
        // 天气图标
        Text(this.getCurrentWeatherIcon())
          .fontSize(14)
          .margin({ left: 8 });
        
        // 同步状态
        Text('🔄')
          .fontSize(14)
          .fontColor(MorandiColors.textHint)
          .margin({ left: 8 });
      }
    }
    .width('100%')
    .padding({ left: 16, right: 16, top: 8, bottom: 8 })
    .backgroundColor(MorandiColors.cardBackground);
  }
}