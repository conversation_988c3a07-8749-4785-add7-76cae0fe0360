import { NoteModel } from '../model/NoteModel';
import { MorandiColors } from '../constants/MorandiColors';
import { DatabaseService } from '../services/DatabaseService';
import { DateUtils } from '../utils/DateUtils';

// 日历页面组件
@Component
export struct CalendarPageComponent {
  @State currentDate: Date = new Date();
  @State selectedDate: Date = new Date();
  @State calendarDays: Array<{ date: Date; isCurrentMonth: boolean; isToday: boolean }> = [];
  @State noteDates: Set<string> = new Set();
  @State noteCounts: Map<string, number> = new Map();
  @State selectedNotes: NoteModel[] = [];
  @State isLoading: boolean = false;
  @State animationProgress: number = 1;
  @State screenWidth: number = 360;

  aboutToAppear() {
    this.initCalendar();
  }

  /**
   * 初始化日历
   */
  private async initCalendar() {
    await this.loadNoteDates();
    this.updateCalendar();
    this.loadSelectedDateNotes();
  }

  /**
   * 加载有笔记的日期和数量
   */
  private async loadNoteDates() {
    try {
      const db = DatabaseService.getInstance();
      const dates = await db.getNoteDates();
      this.noteDates = new Set(dates);
      
      // 加载当前月份的笔记统计
      const stats = await db.getMonthlyNoteStats(this.currentDate.getFullYear(), this.currentDate.getMonth() + 1);
      this.noteCounts = new Map();
      stats.forEach(stat => {
        this.noteCounts.set(stat.date, stat.count);
      });
    } catch (error) {
      console.error('Failed to load note dates:', error);
    }
  }

  /**
   * 更新日历显示
   */
  private updateCalendar() {
    this.calendarDays = DateUtils.getMonthCalendar(this.currentDate);
  }

  /**
   * 加载选中日期的笔记
   */
  private async loadSelectedDateNotes() {
    this.isLoading = true;
    try {
      const db = DatabaseService.getInstance();
      const dateStr = DateUtils.formatDate(this.selectedDate);
      this.selectedNotes = await db.getNotesByDate(dateStr);
    } catch (error) {
      console.error('Failed to load notes for selected date:', error);
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * 切换到上个月
   */
  private async previousMonth() {
    this.animationProgress = 0;
    setTimeout(async () => {
      this.currentDate = DateUtils.addMonths(this.currentDate, -1);
      await this.loadNoteDates();
      this.updateCalendar();
      this.animationProgress = 1;
    }, 150);
  }

  /**
   * 切换到下个月
   */
  private async nextMonth() {
    this.animationProgress = 0;
    setTimeout(async () => {
      this.currentDate = DateUtils.addMonths(this.currentDate, 1);
      await this.loadNoteDates();
      this.updateCalendar();
      this.animationProgress = 1;
    }, 150);
  }

  /**
   * 选择日期
   */
  private selectDate(date: Date) {
    this.selectedDate = date;
    this.loadSelectedDateNotes();
  }

  /**
   * 判断日期是否有笔记
   */
  private hasNotes(date: Date): boolean {
    const dateStr = DateUtils.formatDate(date);
    return this.noteDates.has(dateStr);
  }

  /**
   * 获取日期的笔记数量
   */
  private getNoteCount(date: Date): number {
    const dateStr = DateUtils.formatDate(date);
    return this.noteCounts.get(dateStr) || 0;
  }

  build() {
    Column() {
      // 顶部导航栏
      this.Header();
      
      // 日历网格
      this.CalendarGrid();
      
      // 底部笔记列表
      this.NotesList();
    }
    .width('100%')
    .height('100%')
    .backgroundColor(MorandiColors.background);
  }

  /**
   * 顶部导航栏
   */
  @Builder Header() {
    Column() {
      // 月份导航
      Row() {
        // 左箭头
        Text('‹')
          .fontSize(24)
          .fontColor(MorandiColors.textSecondary)
          .onClick(() => this.previousMonth())
          .padding(12)
          .backgroundColor({ color: MorandiColors.cardBackground, radius: 20 });
        
        // 月份年份显示
        Text(DateUtils.formatMonthYear(this.currentDate))
          .fontSize(20)
          .fontWeight(500)
          .fontColor(MorandiColors.textPrimary)
          .layoutWeight(1)
          .textAlign(TextAlign.Center);
        
        // 右箭头
        Text('›')
          .fontSize(24)
          .fontColor(MorandiColors.textSecondary)
          .onClick(() => this.nextMonth())
          .padding(12)
          .backgroundColor({ color: MorandiColors.cardBackground, radius: 20 });
      }
      .width('100%')
      .justifyContent(FlexAlign.SpaceBetween);
      
      // 今天按钮
      if (!DateUtils.isToday(this.currentDate) || !DateUtils.isToday(this.selectedDate)) {
        Row() {
          Text('返回今天')
            .fontSize(12)
            .fontColor(MorandiColors.textSecondary)
            .padding({ left: 12, right: 12, top: 6, bottom: 6 })
            .backgroundColor({ color: MorandiColors.border, radius: 12 })
            .onClick(() => {
              this.currentDate = new Date();
              this.selectedDate = new Date();
              this.initCalendar();
            });
        }
        .width('100%')
        .margin({ top: 8 })
        .justifyContent(FlexAlign.Center);
      }
    }
    .width('100%')
    .padding({ left: 24, right: 24, top: 20, bottom: 16 });
  }

  /**
   * 日历网格
   */
  @Builder CalendarGrid() {
    Column() {
      // 星期标题
      Row() {
        ForEach(['日', '一', '二', '三', '四', '五', '六'], (day: string) => {
          Text(day)
            .fontSize(12)
            .fontColor(MorandiColors.textTertiary)
            .layoutWeight(1)
            .textAlign(TextAlign.Center);
        });
      }
      .width('100%')
      .padding({ bottom: 8 });

      // 日历日期网格
      Grid() {
        ForEach(this.calendarDays, (dayInfo, index) => {
          GridItem() {
            this.DateCell(dayInfo);
          }
          .columnStart(index % 7)
          .columnEnd(index % 7)
          .rowStart(Math.floor(index / 7))
          .rowEnd(Math.floor(index / 7));
        });
      }
      .columnsTemplate('1fr 1fr 1fr 1fr 1fr 1fr 1fr')
      .rowsTemplate('1fr 1fr 1fr 1fr 1fr 1fr')
      .width('100%')
      .height(320)
      .opacity(this.animationProgress)
      .transition({ opacity: { duration: 300, curve: Curve.EaseInOut } });
    }
    .width('100%')
    .padding({ left: 16, right: 16 })
    .backgroundColor(MorandiColors.cardBackground)
    .borderRadius(16)
    .margin({ left: 24, right: 24, bottom: 16 });
  }

  /**
   * 日期单元格
   */
  @Builder DateCell(dayInfo: { date: Date; isCurrentMonth: boolean; isToday: boolean }) {
    Column() {
      // 日期数字
      Text(dayInfo.date.getDate().toString())
        .fontSize(14)
        .fontWeight(dayInfo.isToday ? 500 : 400)
        .fontColor(dayInfo.isToday ? MorandiColors.background : 
                   dayInfo.isCurrentMonth ? MorandiColors.textPrimary : MorandiColors.textHint)
        .width(32)
        .height(32)
        .textAlign(TextAlign.Center)
        .borderRadius(16)
        .backgroundColor(dayInfo.isToday ? MorandiColors.accent : 'transparent');

      // 笔记指示器
      if (this.hasNotes(dayInfo.date)) {
        Row() {
          if (this.getNoteCount(dayInfo.date) > 1) {
            Text(this.getNoteCount(dayInfo.date).toString())
              .fontSize(8)
              .fontColor(MorandiColors.background)
              .fontWeight(500);
          } else {
            Circle()
              .width(4)
              .height(4)
              .fill(MorandiColors.background);
          }
        }
        .width(16)
        .height(16)
        .justifyContent(FlexAlign.Center)
        .backgroundColor(MorandiColors.accent)
        .borderRadius(8)
        .margin({ top: 2 });
      }
    }
    .width('100%')
    .height(48)
    .justifyContent(FlexAlign.Center)
    .onClick(() => {
      if (dayInfo.isCurrentMonth) {
        this.selectDate(dayInfo.date);
      }
    })
    .backgroundColor(dayInfo.isCurrentMonth ? 'transparent' : MorandiColors.border)
    .borderRadius(8)
    .opacity(dayInfo.isCurrentMonth ? 1 : 0.6);
  }

  /**
   * 笔记列表
   */
  @Builder NotesList() {
    Column() {
      // 标题
      Row() {
        Text(DateUtils.formatDate(this.selectedDate, 'MM月DD日') + ' 的笔记')
          .fontSize(16)
          .fontWeight(500)
          .fontColor(MorandiColors.textPrimary);
        
        Text(`${this.selectedNotes.length} 条`)
          .fontSize(12)
          .fontColor(MorandiColors.textTertiary)
          .margin({ left: 8 });
      }
      .width('100%')
      .margin({ bottom: 12 });

      // 笔记列表
      if (this.isLoading) {
        LoadingProgress()
          .width(30)
          .height(30)
          .margin({ top: 20 });
      } else if (this.selectedNotes.length === 0) {
        Column() {
          Text('该日期暂无笔记')
            .fontSize(14)
            .fontColor(MorandiColors.textHint);
        }
        .width('100%')
        .padding({ top: 40, bottom: 40 })
        .justifyContent(FlexAlign.Center);
      } else {
        List() {
          ForEach(this.selectedNotes, (note: NoteModel) => {
            ListItem() {
              this.NoteCard(note);
            }
          });
        }
        .width('100%')
        .layoutWeight(1)
        .listDirection(Axis.Vertical)
        .edgeEffect(EdgeEffect.Spring);
      }
    }
    .width('100%')
    .layoutWeight(1)
    .padding({ left: 24, right: 24, bottom: 24 });
  }

  /**
   * 笔记卡片
   */
  @Builder NoteCard(note: NoteModel) {
    Column() {
      // 时间
      Text(DateUtils.formatDate(new Date(note.createTime), 'HH:mm'))
        .fontSize(12)
        .fontColor(MorandiColors.textTertiary);
      
      // 标题
      Text(note.title)
        .fontSize(16)
        .fontWeight(400)
        .fontColor(MorandiColors.textPrimary)
        .margin({ top: 4, bottom: 4 })
        .maxLines(2)
        .textOverflow({ overflow: TextOverflow.Ellipsis });
      
      // 内容
      Text(note.content)
        .fontSize(14)
        .fontColor(MorandiColors.textSecondary)
        .lineHeight(18)
        .maxLines(2)
        .textOverflow({ overflow: TextOverflow.Ellipsis });
      
      // 标签
      if (note.tags) {
        Flex({ wrap: FlexWrap.Wrap }) {
          ForEach(note.tags.split(','), (tag: string) => {
            if (tag.trim()) {
              Text(tag.trim())
                .fontSize(10)
                .fontColor(MorandiColors.textHint)
                .backgroundColor(MorandiColors.border)
                .padding({ left: 8, right: 8, top: 2, bottom: 2 })
                .borderRadius(12)
                .margin({ top: 8, right: 6 });
            }
          });
        }
      }
    }
    .width('100%')
    .padding(12)
    .backgroundColor(MorandiColors.cardBackground)
    .borderRadius(12)
    .border({ width: 1, color: MorandiColors.border })
    .margin({ bottom: 8 })
    .onClick(() => {
      // TODO: 跳转到笔记详情页
    });
  }
}