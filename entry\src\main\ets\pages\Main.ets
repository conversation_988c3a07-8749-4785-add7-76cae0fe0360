import { MorandiColors } from '../constants/MorandiColors';
import { DateUtils } from '../utils/DateUtils';
import router from '@ohos.router';

// 页面枚举
enum Page {
  TIMELINE = 'timeline',
  CALENDAR = 'calendar',
  STATS = 'stats',
  PROFILE = 'profile',
  SEARCH = 'search',
  CAPSULE = 'capsule'
}

@Entry
@Component
struct Main {
  @State currentPage: Page = Page.TIMELINE;
  
  build() {
    Column() {
      // 顶部状态栏占位
      Row()
        .width('100%')
        .height(44)
        .backgroundColor(MorandiColors.background);
      
      // 页面内容区域 - 使用路由导航
      Row()
        .layoutWeight(1)
        .backgroundColor(MorandiColors.background);
      
      // 底部导航栏
      this.BottomNavigationBar();
    }
    .width('100%')
    .height('100%')
    .backgroundColor(MorandiColors.background);
  }
    
  /**
   * 底部导航栏
   */
  @Builder BottomNavigationBar() {
    Row() {
      ForEach([
        { page: Page.TIMELINE, icon: 'timeline', label: '时间轴', url: 'pages/TimelinePage' },
        { page: Page.CALENDAR, icon: 'calendar', label: '日历', url: 'pages/CalendarPage' },
        { page: Page.CAPSULE, icon: 'capsule', label: '胶囊', url: 'pages/CapsulePage' },
        { page: Page.SEARCH, icon: 'search', label: '搜索', url: 'pages/SearchPage' },
        { page: Page.PROFILE, icon: 'profile', label: '我的', url: 'pages/ProfilePage' }
      ], (item: { page: Page, icon: string, label: string, url: string }) => {
        Column() {
          // 图标占位
          Text(this.getIconSymbol(item.icon))
            .fontSize(24)
            .fontColor(this.currentPage === item.page ? MorandiColors.accent : MorandiColors.textHint);
          
          Text(item.label)
            .fontSize(10)
            .fontColor(this.currentPage === item.page ? MorandiColors.accent : MorandiColors.textHint)
            .margin({ top: 4 });
        }
        .layoutWeight(1)
        .justifyContent(FlexAlign.Center)
        .onClick(() => {
          this.currentPage = item.page;
          // 使用路由跳转到对应页面
          router.replaceUrl({
            url: item.url
          }).catch((error: Error) => {
            console.error('Navigation failed:', error);
          });
        });
      });
    }
    .width('100%')
    .height(80)
    .backgroundColor(MorandiColors.background)
    .border({ width: { top: 1 }, color: MorandiColors.border })
    .padding({ bottom: 20 });
  }
  
  /**
   * 获取图标符号（简单文本代替）
   */
  private getIconSymbol(icon: string): string {
    switch (icon) {
      case 'timeline': return '✓';
      case 'calendar': return '📅';
      case 'capsule': return '🕒';
      case 'stats': return '📊';
      case 'search': return '🔍';
      case 'profile': return '👤';
      default: return '○';
    }
  }
  }