import { CapsuleModel } from '../model/NoteModel';
import { CapsuleService } from '../services/CapsuleService';
import { DateUtils } from '../utils/DateUtils';
import { MorandiColors } from '../constants/MorandiColors';
import { AppConstants } from '../constants/AppConstants';

// 时间胶囊分类
interface CapsuleCategory {
  id: number;
  name: string;
  color: string;
  icon: string;
}

// 时间胶囊状态
interface CapsuleStatus {
  isLocked: boolean;
  remainingDays: number;
  canOpen: boolean;
  statusText: string;
}

// 弹窗参数接口
interface CapsuleDialogParams {
  capsule: CapsuleModel | null;
  onSave: (capsule: CapsuleModel) => void;
  onCancel: () => void;
}

// 详情弹窗参数接口
interface CapsuleDetailParams {
  capsule: CapsuleModel;
  onClose: () => void;
}


@Component
struct CapsulePage {
  @State private capsules: CapsuleModel[] = [];
  @State private showDialog: boolean = false;
  @State private selectedCapsule: CapsuleModel | null = null;
  @State private showCapsuleDetail: boolean = false;
  @State private loading: boolean = false;
  @State private countdownTimers: Map<number, number> = new Map();
  
  private capsuleService = CapsuleService.getInstance();
  
  // 时间胶囊分类
  private categories: CapsuleCategory[] = [
    { id: 1, name: '回忆', color: MorandiColors.categoryLife, icon: '🕰️' },
    { id: 2, name: '目标', color: MorandiColors.categoryWork, icon: '🎯' },
    { id: 3, name: '感悟', color: MorandiColors.categoryStudy, icon: '💭' },
    { id: 4, name: '祝福', color: MorandiColors.categoryPlan, icon: '🌟' },
    { id: 5, name: '秘密', color: MorandiColors.accent, icon: '🔐' }
  ];

  aboutToAppear() {
    this.loadCapsules();
    this.startCountdownTimer();
  }

  aboutToDisappear() {
    this.clearCountdownTimer();
  }

  // 加载时间胶囊列表
  private async loadCapsules() {
    this.loading = true;
    try {
      this.capsules = await this.capsuleService.getCapsules();
    } catch (error) {
      console.error('加载时间胶囊失败:', error);
    } finally {
      this.loading = false;
    }
  }

  // 开始倒计时定时器
  private startCountdownTimer() {
    // 每分钟更新一次倒计时
    setInterval(() => {
      this.updateCountdowns();
    }, 60000);
  }

  // 清除倒计时定时器
  private clearCountdownTimer() {
    // 清理定时器逻辑
  }

  // 更新所有胶囊的倒计时
  private updateCountdowns() {
    this.capsules.forEach(capsule => {
      const status = this.getCapsuleStatus(capsule);
      this.countdownTimers.set(capsule.id!, status.remainingDays);
    });
  }

  // 获取时间胶囊状态
  private getCapsuleStatus(capsule: CapsuleModel): CapsuleStatus {
    const status = this.capsuleService.getCapsuleStatus(capsule);
    return {
      isLocked: status.isLocked,
      remainingDays: status.remainingDays,
      canOpen: status.canOpen,
      statusText: status.statusText
    };
  }

  // 获取胶囊分类
  private getCapsuleCategory(capsule: CapsuleModel): CapsuleCategory {
    const category = this.capsuleService.categorizeCapsule(capsule);
    return {
      id: category.id,
      name: category.name,
      color: category.color,
      icon: category.icon
    };
  }

  // 创建新时间胶囊
  private onCreateCapsule() {
    this.selectedCapsule = null;
    this.showDialog = true;
  }

  // 编辑时间胶囊
  private onEditCapsule(capsule: CapsuleModel) {
    if (capsule.isOpened) {
      // 已开启的胶囊不能编辑
      return;
    }
    this.selectedCapsule = capsule;
    this.showDialog = true;
  }

  // 开启时间胶囊
  private async onOpenCapsule(capsule: CapsuleModel) {
    const status = this.getCapsuleStatus(capsule);
    if (!status.canOpen) {
      return;
    }
    
    try {
      // 开启胶囊
      const openedCapsule = await this.capsuleService.openCapsule(capsule.id!);
      await this.loadCapsules();
      
      // 显示胶囊详情
      this.selectedCapsule = openedCapsule;
      this.showCapsuleDetail = true;
    } catch (error) {
      console.error('开启时间胶囊失败:', error);
    }
  }

  // 删除时间胶囊
  private async onDeleteCapsule(capsule: CapsuleModel) {
    // 确认删除逻辑
    try {
      await this.capsuleService.deleteCapsule(capsule.id!);
      await this.loadCapsules();
    } catch (error) {
      console.error('删除时间胶囊失败:', error);
    }
  }

  // 保存时间胶囊
  private async onSaveCapsule(capsule: CapsuleModel) {
    try {
      if (capsule.id) {
        await this.capsuleService.updateCapsule(capsule.id!, capsule);
      } else {
        await this.capsuleService.createCapsule(capsule);
      }
      await this.loadCapsules();
      this.showDialog = false;
    } catch (error) {
      console.error('保存时间胶囊失败:', error);
    }
  }

  build() {
    Column() {
      // 顶部标题栏
      Row() {
        Text('时间胶囊')
          .fontSize(24)
          .fontWeight(FontWeight.Bold)
          .fontColor(MorandiColors.textPrimary)
        
        Blank()
        
        Button('创建')
          .backgroundColor(MorandiColors.primary)
          .fontColor(Color.White)
          .borderRadius(20)
          .padding({ left: 24, right: 24, top: 8, bottom: 8 })
          .onClick(() => this.onCreateCapsule())
      }
      .width('100%')
      .padding(16)
      .justifyContent(FlexAlign.SpaceBetween)

      if (this.loading) {
        // 加载状态
        LoadingProgress()
          .width(50)
          .height(50)
          .margin(50)
      } else if (this.capsules.length === 0) {
        // 空状态
        Column() {
          Text('📦')
            .fontSize(80)
            .margin({ bottom: 20 })
          
          Text('还没有时间胶囊')
            .fontSize(16)
            .fontColor(MorandiColors.textSecondary)
            .margin({ bottom: 12 })
          
          Text('创建你的第一个时间胶囊，给未来的自己写一封信')
            .fontSize(14)
            .fontColor(MorandiColors.textTertiary)
            .textAlign(TextAlign.Center)
            .padding({ left: 40, right: 40 })
          
          Button('创建时间胶囊')
            .backgroundColor(MorandiColors.primary)
            .fontColor(Color.White)
            .borderRadius(20)
            .padding({ left: 32, right: 32, top: 12, bottom: 12 })
            .margin({ top: 24 })
            .onClick(() => this.onCreateCapsule())
        }
        .width('100%')
        .margin(50)
      } else {
        // 胶囊列表
        List({ space: 12 }) {
          ForEach(this.capsules, (capsule: CapsuleModel) => {
            ListItem() {
              this.CapsuleItem({ capsule: capsule })
            }
          });
        }
        .width('100%')
        .layoutWeight(1)
        .padding({ left: 16, right: 16 })
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor(MorandiColors.background)
    
    // 创建/编辑弹窗
    if (this.showDialog) {
      this.CapsuleDialog({
        capsule: this.selectedCapsule,
        onSave: (capsule) => this.onSaveCapsule(capsule),
        onCancel: () => this.showDialog = false
      })
    }
    
    // 胶囊详情弹窗
    if (this.showCapsuleDetail && this.selectedCapsule) {
      this.CapsuleDetailDialog({
        capsule: this.selectedCapsule,
        onClose: () => this.showCapsuleDetail = false
      })
    }
  }

  // 胶囊列表项组件
  @Builder CapsuleItem($$: { capsule: CapsuleModel }) {
    const capsule = $$.capsule;
    const status = this.getCapsuleStatus(capsule);
    const category = this.getCapsuleCategory(capsule);
    
    Column() {
      Row() {
        // 分类图标
        Text(category.icon)
          .fontSize(24)
          .margin({ right: 12 })
        
        // 主要信息
        Column() {
          Text(capsule.title)
            .fontSize(16)
            .fontWeight(FontWeight.Medium)
            .fontColor(MorandiColors.textPrimary)
            .maxLines(1)
            .textOverflow({ overflow: TextOverflow.Ellipsis })
          
          Row() {
            Text(category.name)
              .fontSize(12)
              .fontColor(category.color)
              .backgroundColor(category.color + '20')
              .padding({ left: 8, right: 8, top: 4, bottom: 4 })
              .borderRadius(12)
            
            if (status.isLocked) {
              Text('🔒')
                .fontSize(12)
                .margin({ left: 8 })
            }
          }
          .margin({ top: 4 })
        }
        .layoutWeight(1)
        .alignItems(HorizontalAlign.Start)
        
        // 状态信息
        Column() {
          Text(status.statusText)
            .fontSize(14)
            .fontWeight(FontWeight.Medium)
            .fontColor(status.canOpen ? MorandiColors.success : MorandiColors.textSecondary)
          
          Text(DateUtils.formatDate(new Date(capsule.openDate), 'MM-DD'))
            .fontSize(12)
            .fontColor(MorandiColors.textTertiary)
            .margin({ top: 2 })
        }
        .alignItems(HorizontalAlign.End)
      }
      .width('100%')
      .alignItems(VerticalAlign.Center)
      
      // 操作按钮
      Row() {
        if (!capsule.isOpened) {
          if (status.canOpen) {
            Button('开启')
              .backgroundColor(MorandiColors.success)
              .fontColor(Color.White)
              .borderRadius(16)
              .padding({ left: 20, right: 20, top: 6, bottom: 6 })
              .onClick(() => this.onOpenCapsule(capsule))
          } else {
            Button('编辑')
              .backgroundColor(MorandiColors.primary)
              .fontColor(Color.White)
              .borderRadius(16)
              .padding({ left: 20, right: 20, top: 6, bottom: 6 })
              .onClick(() => this.onEditCapsule(capsule))
          }
        }
        
        Blank()
        
        if (!capsule.isOpened) {
          Button('删除')
            .backgroundColor(Color.Transparent)
            .fontColor(MorandiColors.error)
            .borderRadius(16)
            .padding({ left: 20, right: 20, top: 6, bottom: 6 })
            .onClick(() => this.onDeleteCapsule(capsule))
        }
      }
      .width('100%')
      .margin({ top: 12 })
      .justifyContent(FlexAlign.SpaceBetween)
    }
    .width('100%')
    .padding(16)
    .backgroundColor(MorandiColors.cardBackground)
    .borderRadius(16)
    .shadow({
      radius: 8,
      color: MorandiColors.shadow,
      offsetX: 0,
      offsetY: 2
    })
    .onClick(() => {
      if (capsule.isOpened) {
        this.selectedCapsule = capsule;
        this.showCapsuleDetail = true;
      }
    })
  }

  // 创建/编辑弹窗组件
  @Builder CapsuleDialog($$: CapsuleDialogParams) {
    CapsuleDialogContent({ params: $$ })
  }

  // 弹窗内容组件
  @Component
  struct CapsuleDialogContent {
    @Prop params: CapsuleDialogParams;
    @State title: string = this.params.capsule?.title || '';
    @State content: string = this.params.capsule?.content || '';
    @State openDate: string = this.params.capsule?.openDate || '';
    @State selectedCategory: number = 1;
    @State isOpenTime: boolean = this.params.capsule?.isOpenTime || false;
    
    build() {
      const editingCapsule = this.params.capsule;
      const categories = [
        { id: 1, name: '回忆', color: MorandiColors.categoryLife, icon: '🕰️' },
        { id: 2, name: '目标', color: MorandiColors.categoryWork, icon: '🎯' },
        { id: 3, name: '感悟', color: MorandiColors.categoryStudy, icon: '💭' },
        { id: 4, name: '祝福', color: MorandiColors.categoryPlan, icon: '🌟' },
        { id: 5, name: '秘密', color: MorandiColors.accent, icon: '🔐' }
      ];
      
      Column() {
        // 弹窗标题
        Text(editingCapsule ? '编辑时间胶囊' : '创建时间胶囊')
          .fontSize(20)
          .fontWeight(FontWeight.Bold)
          .fontColor(MorandiColors.textPrimary)
          .margin({ bottom: 20 })
        
        // 表单内容
        Column() {
          // 标题输入
          TextInput({ placeholder: '输入胶囊标题' })
            .width('100%')
            .height(50)
            .backgroundColor(MorandiColors.surface)
            .borderRadius(8)
            .padding(12)
            .fontSize(16)
            .fontColor(MorandiColors.textPrimary)
            .onChange((value: string) => {
              this.title = value;
            })
          
          // 内容输入
          TextArea({ placeholder: '写下你想对未来的自己说的话...' })
            .width('100%')
            .height(120)
            .backgroundColor(MorandiColors.surface)
            .borderRadius(8)
            .padding(12)
            .fontSize(14)
            .fontColor(MorandiColors.textPrimary)
            .onChange((value: string) => {
              this.content = value;
            })
          
          // 开启日期
          Row() {
            Text('开启日期:')
              .fontSize(14)
              .fontColor(MorandiColors.textPrimary)
              .margin({ right: 12 })
            
            TextInput({ placeholder: 'YYYY-MM-DD HH:mm:ss' })
              .width('60%')
              .height(40)
              .backgroundColor(MorandiColors.surface)
              .borderRadius(8)
              .padding(8)
              .fontSize(14)
              .fontColor(MorandiColors.textPrimary)
              .onChange((value: string) => {
                this.openDate = value;
              })
          }
          .width('100%')
          .margin({ top: 12 })
          .alignItems(VerticalAlign.Center)
          
          // 分类选择
          Row() {
            Text('分类:')
              .fontSize(14)
              .fontColor(MorandiColors.textPrimary)
              .margin({ right: 12 })
            
            Flex({ wrap: FlexWrap.Wrap }) {
              ForEach(categories, (category: CapsuleCategory) => {
                Text(category.icon + ' ' + category.name)
                  .fontSize(12)
                  .fontColor(this.selectedCategory === category.id ? Color.White : category.color)
                  .backgroundColor(this.selectedCategory === category.id ? category.color : (category.color + '20'))
                  .padding({ left: 12, right: 12, top: 6, bottom: 6 })
                  .borderRadius(16)
                  .margin({ right: 8, bottom: 8 })
                  .onClick(() => {
                    this.selectedCategory = category.id;
                  })
              });
            }
          }
          .width('100%')
          .margin({ top: 12 })
          .alignItems(VerticalAlign.Start)
          
          // 定时开启开关
          Row() {
            Text('定时开启')
              .fontSize(14)
              .fontColor(MorandiColors.textPrimary)
            
            Blank()
            
            Toggle({ type: ToggleType.Switch, isOn: this.isOpenTime })
              .onChange((isOn: boolean) => {
                this.isOpenTime = isOn;
              })
          }
          .width('100%')
          .margin({ top: 12 })
          .alignItems(VerticalAlign.Center)
        }
        .width('100%')
        
        // 操作按钮
        Row() {
          Button('取消')
            .backgroundColor(Color.Transparent)
            .fontColor(MorandiColors.textSecondary)
            .borderRadius(20)
            .padding({ left: 32, right: 32, top: 12, bottom: 12 })
            .onClick(() => this.params.onCancel())
          
          Button('保存')
            .backgroundColor(MorandiColors.primary)
            .fontColor(Color.White)
            .borderRadius(20)
            .padding({ left: 32, right: 32, top: 12, bottom: 12 })
            .onClick(() => {
              if (this.title.trim() && this.content.trim() && this.openDate) {
                this.params.onSave({
                  id: editingCapsule?.id,
                  title: this.title.trim(),
                  content: this.content.trim(),
                  openDate: this.openDate,
                  createTime: editingCapsule?.createTime || DateUtils.now(),
                  isOpened: editingCapsule?.isOpened || 0,
                  isOpenTime: this.isOpenTime
                });
              }
            })
        }
        .width('100%')
        .margin({ top: 24 })
        .justifyContent(FlexAlign.SpaceAround)
      }
      .width('90%')
      .maxHeight('80%')
      .backgroundColor(MorandiColors.surface)
      .borderRadius(16)
      .padding(24)
      .shadow({
        radius: 16,
        color: MorandiColors.shadow,
        offsetX: 0,
        offsetY: 8
      })
    }
  }

  // 胶囊详情弹窗组件
  @Builder CapsuleDetailDialog($$: CapsuleDetailParams) {
    CapsuleDetailContent({ params: $$ })
  }

  // 胶囊详情内容组件
  @Component
  struct CapsuleDetailContent {
    @Prop params: CapsuleDetailParams;
    
    build() {
      const capsule = this.params.capsule;
      const categories = [
        { id: 1, name: '回忆', color: MorandiColors.categoryLife, icon: '🕰️' },
        { id: 2, name: '目标', color: MorandiColors.categoryWork, icon: '🎯' },
        { id: 3, name: '感悟', color: MorandiColors.categoryStudy, icon: '💭' },
        { id: 4, name: '祝福', color: MorandiColors.categoryPlan, icon: '🌟' },
        { id: 5, name: '秘密', color: MorandiColors.accent, icon: '🔐' }
      ];
      
      // 智能分类
      const category = this.getCapsuleCategory(capsule);
      
      Column() {
        // 弹窗标题
        Text('时间胶囊已开启')
          .fontSize(20)
          .fontWeight(FontWeight.Bold)
          .fontColor(MorandiColors.success)
          .margin({ bottom: 20 })
        
        // 胶囊信息
        Column() {
          Row() {
            Text(category.icon)
              .fontSize(20)
              .margin({ right: 8 })
            
            Text(capsule.title)
              .fontSize(18)
              .fontWeight(FontWeight.Medium)
              .fontColor(MorandiColors.textPrimary)
          }
          .width('100%')
          .alignItems(VerticalAlign.Center)
          
          Divider()
            .color(MorandiColors.divider)
            .margin({ top: 16, bottom: 16 })
          
          // 内容
          Scroll() {
            Text(capsule.content)
              .fontSize(16)
              .fontColor(MorandiColors.textPrimary)
              .lineHeight(24)
          }
          .width('100%')
          .height(200)
          
          Divider()
            .color(MorandiColors.divider)
            .margin({ top: 16, bottom: 16 })
          
          // 创建时间
          Row() {
            Text('创建时间:')
              .fontSize(14)
              .fontColor(MorandiColors.textSecondary)
              .margin({ right: 8 })
            
            Text(DateUtils.formatDate(new Date(capsule.createTime), 'YYYY-MM-DD HH:mm'))
              .fontSize(14)
              .fontColor(MorandiColors.textTertiary)
          }
          .width('100%')
          .margin({ bottom: 8 })
          .alignItems(VerticalAlign.Center)
          
          Row() {
            Text('开启时间:')
              .fontSize(14)
              .fontColor(MorandiColors.textSecondary)
              .margin({ right: 8 })
            
            Text(DateUtils.formatDate(new Date(capsule.openDate), 'YYYY-MM-DD'))
              .fontSize(14)
              .fontColor(MorandiColors.textTertiary)
          }
          .width('100%')
          .alignItems(VerticalAlign.Center)
        }
        .width('100%')
        
        // 关闭按钮
        Button('关闭')
          .backgroundColor(MorandiColors.primary)
          .fontColor(Color.White)
          .borderRadius(20)
          .padding({ left: 48, right: 48, top: 12, bottom: 12 })
          .margin({ top: 24 })
          .onClick(() => this.params.onClose())
      }
      .width('90%')
      .maxHeight('80%')
      .backgroundColor(MorandiColors.surface)
      .borderRadius(16)
      .padding(24)
      .shadow({
        radius: 16,
        color: MorandiColors.shadow,
        offsetX: 0,
        offsetY: 8
      })
    }
    
    // 获取胶囊分类
    private getCapsuleCategory(capsule: CapsuleModel): CapsuleCategory {
      const categories = [
        { id: 1, name: '回忆', color: MorandiColors.categoryLife, icon: '🕰️' },
        { id: 2, name: '目标', color: MorandiColors.categoryWork, icon: '🎯' },
        { id: 3, name: '感悟', color: MorandiColors.categoryStudy, icon: '💭' },
        { id: 4, name: '祝福', color: MorandiColors.categoryPlan, icon: '🌟' },
        { id: 5, name: '秘密', color: MorandiColors.accent, icon: '🔐' }
      ];
      
      // 根据胶囊标题或内容智能分类
      const title = capsule.title.toLowerCase();
      const content = capsule.content.toLowerCase();
      
      if (title.includes('目标') || title.includes('计划') || content.includes('目标')) {
        return categories[1]; // 目标
      } else if (title.includes('回忆') || title.includes('过去') || content.includes('回忆')) {
        return categories[0]; // 回忆
      } else if (title.includes('感悟') || title.includes('思考') || content.includes('感悟')) {
        return categories[2]; // 感悟
      } else if (title.includes('祝福') || title.includes('希望') || content.includes('祝福')) {
        return categories[3]; // 祝福
      } else {
        return categories[4]; // 秘密
      }
    }
  }
}