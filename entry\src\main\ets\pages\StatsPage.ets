import { StatsService, StatsOverview, MonthlyTrend, CategoryStats } from '../services/StatsService';
import { MorandiColors } from '../constants/MorandiColors';
import { DateUtils } from '../utils/DateUtils';
import { MyCard } from '../components/Basic/MyCard';

@Component
export struct StatsPageComponent {
  @State private isLoading: boolean = true;
  @State private overview: StatsOverview = {
    totalNotes: 0,
    consecutiveDays: 0,
    categoryCount: 0,
    completionRate: 0
  };
  @State private monthlyTrend: MonthlyTrend[] = [];
  @State private categoryStats: CategoryStats[] = [];
  @State private lastRefreshTime: string = '';
  
  private statsService = StatsService.getInstance();
  
  aboutToAppear() {
    this.loadStats();
  }
  
  /**
   * 加载统计数据
   */
  private async loadStats() {
    this.isLoading = true;
    try {
      const [overview, monthlyTrend, categoryStats] = await Promise.all([
        this.statsService.getOverview(),
        this.statsService.getMonthlyTrend(),
        this.statsService.getCategoryStats()
      ]);
      
      this.overview = overview;
      this.monthlyTrend = monthlyTrend;
      this.categoryStats = categoryStats;
      this.lastRefreshTime = DateUtils.formatTime(new Date());
    } catch (error) {
      console.error('Failed to load stats:', error);
    } finally {
      this.isLoading = false;
    }
  }
  
  build() {
    Column() {
      // 顶部标题栏
      this.HeaderBar();
      
      if (this.isLoading) {
        // 加载状态
        this.LoadingView();
      } else {
        // 统计内容
        Scroll() {
          Column() {
            // 数据概览卡片
            this.OverviewCards();
            
            // 图表区域
            this.ChartsSection();
            
            // 详细统计
            this.DetailStats();
            
            // 刷新时间
            this.RefreshInfo();
          }
          .padding({ left: 16, right: 16, bottom: 100 });
        }
        .scrollBar(BarState.Off);
        .edgeEffect(EdgeEffect.Spring);
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor(MorandiColors.background);
  }
  
  /**
   * 顶部标题栏
   */
  @Builder HeaderBar() {
    Row() {
      Column() {
        Text('数据统计')
          .fontSize(24)
          .fontWeight(500)
          .fontColor(MorandiColors.textPrimary);
        
        Text('了解你的记录习惯')
          .fontSize(14)
          .fontColor(MorandiColors.textTertiary)
          .margin({ top: 4 });
      }
      .alignItems(HorizontalAlign.Start)
      .layoutWeight(1);
      
      // 刷新按钮
      Button() {
        Text('🔄')
          .fontSize(20);
      }
      .width(40)
      .height(40)
      .backgroundColor(MorandiColors.cardBackground)
      .borderRadius(20)
      .border({ width: 1, color: MorandiColors.border })
      .onClick(() => {
        this.loadStats();
      });
    }
    .width('100%')
    .padding({ left: 24, right: 24, top: 44, bottom: 16 })
    .backgroundColor(MorandiColors.background);
  }
  
  /**
   * 加载视图
   */
  @Builder LoadingView() {
    Column() {
      LoadingProgress()
        .width(60)
        .height(60)
        .color(MorandiColors.primary);
      
      Text('正在加载统计数据...')
        .fontSize(16)
        .fontColor(MorandiColors.textTertiary)
        .margin({ top: 20 });
    }
    .width('100%')
    .height('100%')
    .justifyContent(FlexAlign.Center);
  }
  
  /**
   * 数据概览卡片
   */
  @Builder OverviewCards() {
    Column() {
      Text('数据概览')
        .fontSize(18)
        .fontWeight(500)
        .fontColor(MorandiColors.textPrimary)
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 16 });
      
      // 统计卡片网格
      Grid() {
        GridItem() {
          this.StatCard(
            '总笔记数',
            this.overview.totalNotes.toString(),
            '篇',
            MorandiColors.categoryWork,
            '📝'
          );
        }
        
        GridItem() {
          this.StatCard(
            '连续记录',
            this.overview.consecutiveDays.toString(),
            '天',
            MorandiColors.categoryLife,
            '📅'
          );
        }
        
        GridItem() {
          this.StatCard(
            '分类数量',
            this.overview.categoryCount.toString(),
            '个',
            MorandiColors.categoryStudy,
            '🏷️'
          );
        }
        
        GridItem() {
          this.StatCard(
            '完成率',
            this.overview.completionRate.toString(),
            '%',
            MorandiColors.categoryPlan,
            '📊'
          );
        }
      }
      .columnsTemplate('1fr 1fr')
      .rowsTemplate('1fr 1fr')
      .columnsGap(12)
      .rowsGap(12)
      .width('100%')
      .height(200);
    }
    .margin({ bottom: 24 });
  }
  
  /**
   * 统计卡片组件
   */
  @Builder StatCard(title: string, value: string, unit: string, color: string, icon: string) {
    MyCard({
      padding: 16,
      clickable: false,
      showShadow: true
    }) {
      Column() {
        Row() {
          Text(icon)
            .fontSize(24)
            .margin({ right: 8 });
          
          Text(title)
            .fontSize(14)
            .fontWeight(500)
            .fontColor(MorandiColors.textSecondary);
        }
        .alignSelf(ItemAlign.Start);
        
        Row() {
          Text(value)
            .fontSize(32)
            .fontWeight(600)
            .fontColor(color);
          
          Text(unit)
            .fontSize(16)
            .fontWeight(400)
            .fontColor(MorandiColors.textTertiary)
            .margin({ left: 4, top: 8 });
        }
        .alignSelf(ItemAlign.Start)
        .margin({ top: 8 });
      }
      .width('100%')
      .height('100%')
      .justifyContent(FlexAlign.Center);
    }
    .width('100%')
    .height('100%');
  }
  
  /**
   * 图表区域
   */
  @Builder ChartsSection() {
    Column() {
      Text('数据图表')
        .fontSize(18)
        .fontWeight(500)
        .fontColor(MorandiColors.textPrimary)
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 16 });
      
      // 本月记录趋势图
      this.TrendChart();
      
      // 分类占比图
      this.CategoryChart();
    }
    .margin({ bottom: 24 });
  }
  
  /**
   * 趋势图
   */
  @Builder TrendChart() {
    MyCard({
      title: '本月记录趋势',
      padding: 16,
      clickable: false
    }) {
      Column() {
        // 简化的折线图
        this.SimpleLineChart();
        
        Text(`本月共记录 ${this.monthlyTrend.reduce((sum, item) => sum + item.count, 0)} 篇笔记`)
          .fontSize(12)
          .fontColor(MorandiColors.textTertiary)
          .margin({ top: 8 });
      }
      .width('100%');
    }
    .margin({ bottom: 16 });
  }
  
  /**
   * 简化的折线图
   */
  @Builder SimpleLineChart() {
    Stack() {
      // 网格线
      Column() {
        ForEach([0, 1, 2, 3, 4], (index: number) => {
          Divider()
            .color(MorandiColors.border)
            .strokeWidth(1)
            .margin({ top: index === 0 ? 0 : 20 });
        });
      }
      .width('100%')
      .height(100);
      
      // 折线
      Row() {
        ForEach(this.monthlyTrend.slice(-7), (item: MonthlyTrend, index: number) => {
          Column() {
            // 数据点
            Circle({ width: 6, height: 6 })
              .fill(item.count > 0 ? MorandiColors.accent : MorandiColors.border)
              .margin({ bottom: item.count * 2 });
            
            // 日期
            Text(item.date.substring(8, 10))
              .fontSize(10)
              .fontColor(MorandiColors.textHint);
          }
          .layoutWeight(1)
          .alignItems(HorizontalAlign.Center);
        });
      }
      .width('100%')
      .height(120);
    }
    .width('100%')
    .height(120);
  }
  
  /**
   * 分类图表
   */
  @Builder CategoryChart() {
    MyCard({
      title: '分类占比',
      padding: 16,
      clickable: false
    }) {
      Column() {
        // 简化的饼图
        this.SimplePieChart();
        
        // 图例
        this.CategoryLegend();
      }
      .width('100%');
    }
  }
  
  /**
   * 简化的饼图
   */
  @Builder SimplePieChart() {
    Stack() {
      // 饼图背景
      Circle({ width: 120, height: 120 })
        .fill(MorandiColors.border);
      
      // 根据比例绘制扇形（简化版本）
      if (this.categoryStats.length > 0) {
        let startAngle = 0;
        ForEach(this.categoryStats, (stat: CategoryStats) => {
          if (stat.count > 0) {
            const angle = (stat.percentage / 100) * 360;
            // 这里简化处理，实际应该使用Canvas绘制扇形
            Circle({ width: 120, height: 120 })
              .fill(stat.color)
              .opacity(0.8)
              .width(120 * (stat.percentage / 100))
              .height(120 * (stat.percentage / 100));
            startAngle += angle;
          }
        });
      }
    }
    .width(120)
    .height(120)
    .margin({ bottom: 16 });
  }
  
  /**
   * 分类图例
   */
  @Builder CategoryLegend() {
    Column() {
      ForEach(this.categoryStats, (stat: CategoryStats) => {
        if (stat.count > 0) {
          Row() {
            Circle({ width: 12, height: 12 })
              .fill(stat.color)
              .margin({ right: 8 });
            
            Text(`${stat.categoryName} (${stat.count}篇)`)
              .fontSize(12)
              .fontColor(MorandiColors.textSecondary)
              .layoutWeight(1);
            
            Text(`${stat.percentage}%`)
              .fontSize(12)
              .fontWeight(500)
              .fontColor(MorandiColors.textPrimary);
          }
          .width('100%')
          .justifyContent(FlexAlign.SpaceBetween)
          .margin({ bottom: 4 });
        }
      });
    }
    .width('100%');
  }
  
  /**
   * 详细统计
   */
  @Builder DetailStats() {
    Column() {
      Text('详细统计')
        .fontSize(18)
        .fontWeight(500)
        .fontColor(MorandiColors.textPrimary)
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 16 });
      
      // 分类详细统计
      MyCard({
        padding: 16,
        clickable: false
      }) {
        Column() {
          ForEach(this.categoryStats, (stat: CategoryStats) => {
            this.CategoryStatRow(stat);
          });
        }
        .width('100%');
      });
    }
    .margin({ bottom: 24 });
  }
  
  /**
   * 分类统计行
   */
  @Builder CategoryStatRow(stat: CategoryStats) {
    Column() {
      Row() {
        Row() {
          Circle({ width: 8, height: 8 })
            .fill(stat.color)
            .margin({ right: 8 });
          
          Text(stat.categoryName)
            .fontSize(14)
            .fontColor(MorandiColors.textPrimary);
        }
        .alignItems(VerticalAlign.Center);
        
        Text(`${stat.count} 篇`)
          .fontSize(14)
          .fontWeight(500)
          .fontColor(stat.color);
      }
      .width('100%')
      .justifyContent(FlexAlign.SpaceBetween)
      .margin({ bottom: 8 });
      
      // 进度条
      Row() {
        Row()
          .width(`${stat.percentage}%`)
          .height(4)
          .backgroundColor(stat.color)
          .borderRadius(2);
        
        Row()
          .layoutWeight(1)
          .height(4)
          .backgroundColor(MorandiColors.border)
          .borderRadius(2);
      }
      .width('100%')
      .margin({ bottom: 12 });
    }
    .width('100%');
  }
  
  /**
   * 刷新信息
   */
  @Builder RefreshInfo() {
    Row() {
      Text(`最后更新: ${this.lastRefreshTime}`)
        .fontSize(12)
        .fontColor(MorandiColors.textHint);
      
      Blank();
      
      Text('点击刷新按钮更新数据')
        .fontSize(12)
        .fontColor(MorandiColors.textHint);
    }
    .width('100%')
    .padding({ top: 8, bottom: 8 });
  }
}